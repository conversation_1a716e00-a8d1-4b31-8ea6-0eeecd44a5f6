<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.14.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.myweb.daa</groupId>
    <artifactId>adapter1049-2024-http</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>adapter1049-2024-http</name>
    <description>adapter1049-2024-http</description>

    <properties>
        <java.version>1.8</java.version>
        <module.version>1.0.0-SNAPSHOT</module.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <querydsl.version>4.2.1</querydsl.version>
        <swagger2.version>2.9.2</swagger2.version>
        <spring-cloud.version>Greenwich.SR6</spring-cloud.version>
        <druid.version>1.0.20</druid.version>
        <khs-spring-boot-publish-swagger-starter.version>2.1.1</khs-spring-boot-publish-swagger-starter.version>
        <guava.version>20.0</guava.version>
        <spring-rabbit-junit.version>1.7.11.RELEASE</spring-rabbit-junit.version>
        <spring-boot-admin-starter-client.version>2.1.1</spring-boot-admin-starter-client.version>
        <xstream.version>********</xstream.version>
        <nacos.version>2.1.1.RELEASE</nacos.version>
        <common-jpa.version>0.0.2-SNAPSHOT</common-jpa.version>
        <common-stream.version>0.0.1-SNAPSHOT</common-stream.version>
        <netty.version>4.1.50.Final</netty.version>
        <h2.version>1.4.200</h2.version>
        <common-ops.version>0.0.2-SNAPSHOT</common-ops.version>
        <swagger-spring-boot-starter.version>1.9.1.RELEASE</swagger-spring-boot-starter.version>
        <ojdbc6.version>11.2</ojdbc6.version>
        <groovy-all.version>2.5.12</groovy-all.version>

    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--https://mvnrepository.com/artifact/com.thoughtworks.xstream/xstream-->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>

        <!--
      <dependency>
          <groupId>com.alibaba.cloud</groupId>
          <artifactId>spring-cloud-alibaba-nacos-discovery</artifactId>
          <version>${nacos.version}</version>
      </dependency>
      <dependency>
          <groupId>com.alibaba.cloud</groupId>
          <artifactId>spring-cloud-alibaba-nacos-config</artifactId>
          <version>${nacos.version}</version>
      </dependency>
      -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.keyholesoftware</groupId>
            <artifactId>khs-spring-boot-publish-swagger-starter</artifactId>
            <version>${khs-spring-boot-publish-swagger-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin-starter-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-jpa</artifactId>
            <version>${common-jpa.version}</version>
        </dependency>

        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-stream</artifactId>
            <version>${common-stream.version}</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml</groupId>
            <artifactId>aalto-xml</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.les</groupId>
            <artifactId>netty-jssc</artifactId>
            <version>0.1.1-SNAPSHOT</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.h2database/h2 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>${h2.version}</version>
        </dependency>

        <!-- Micrometer Prometheus registry  -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!--基础设备定义项-->
        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-ops</artifactId>
            <version>${common-ops.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.spring4all/swagger-spring-boot-starter -->
        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
            <version>${swagger-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>fakepath</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>${ojdbc6.version}</version>
        </dependency>

        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.5.4</version><!-- or latest version -->
        </dependency>

        <dependency>
            <groupId>com.les.its.scm</groupId>
            <artifactId>data-struct</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <!-- 规则引擎 -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>


    </dependencies>

    <distributionManagement>
        <repository>
            <id>wechatmx-releases</id>
            <name>wechatmx Project Releases Repository</name>
            <url>http://192.168.110.236:8080/content/repositories/wechatmx-releases</url>
        </repository>
        <snapshotRepository>
            <id>wechatmx-snapshots</id>
            <name>wechatmx Project Snapshots Repository</name>
            <url>http://192.168.110.236:8080/content/repositories/wechatmx-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processors>
                                <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                                <processor>lombok.launch.AnnotationProcessorHider$AnnotationProcessor</processor>
                            </processors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.github.swagger2markup</groupId>
                <artifactId>swagger2markup-maven-plugin</artifactId>
                <version>1.3.3</version>
                <configuration>
                    <swaggerInput>http://localhost:16000/v2/api-docs</swaggerInput>
                    <outputDir>src/docs/markdown/generated-by-plugin</outputDir>
                    <config>
                        <swagger2markup.markupLanguage>MARKDOWN</swagger2markup.markupLanguage>
                    </config>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.31.0</version>
                <configuration>
                    <dockerHost>tcp://*************:2375</dockerHost>
                    <images>
                        <image>
                            <name>lesregistry:5000/les/${project.artifactId}</name>
                            <alias>les-${project.artifactId}</alias>
                            <build>
                                <from>les/oracle-jre</from>
                                <tags>
                                    <tag></tag>
                                </tags>
                                <entryPoint>
                                    <exec>
                                        <arg>java</arg>
                                        <arg>-Xms512m</arg>
                                        <arg>-Xmx512m</arg>
                                        <arg>-Dfile.encoding=UTF-8</arg>
                                        <arg>-Duser.timezone=GMT+08</arg>
                                        <arg>-Dserver.port=18091</arg>
                                        <arg>-Dspring.profiles.active=tz</arg>
                                        <arg>-jar</arg>
                                        <arg>/home/<USER>/${project.build.finalName}.jar</arg>
                                    </exec>
                                </entryPoint>
                                <assembly>
                                    <name>/home/<USER>/name>
                                    <descriptorRef>artifact</descriptorRef>
                                </assembly>
                            </build>
                            <run>
                                <log>
                                    <driver>
                                        <opts>
                                            <max-size>10m</max-size>
                                        </opts>
                                    </driver>
                                </log>
                                <namingStrategy>alias</namingStrategy>
                                <restartPolicy>
                                    <name>always</name>
                                </restartPolicy>
                                <network>
                                    <mode>custom</mode>
                                    <name>bridge</name>
                                </network>
                                <ports>
                                    <port>
                                        18088:18091
                                    </port>
                                </ports>
                            </run>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
