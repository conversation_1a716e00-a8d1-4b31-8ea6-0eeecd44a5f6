<!doctype html>
<html>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width initial-scale=1'>
    <title>paths</title>
    <link href='https://fonts.loli.net/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext'
          rel='stylesheet' type='text/css'/>
    <style type='text/css'>html {
        overflow-x: initial !important;
    }

    :root {
        --bg-color: #ffffff;
        --text-color: #333333;
        --select-text-bg-color: #B5D6FC;
        --select-text-font-color: auto;
        --monospace: "Lucida Console", Consolas, "Courier", monospace;
    }

    html {
        font-size: 14px;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
    }

    body {
        margin: 0px;
        padding: 0px;
        height: auto;
        bottom: 0px;
        top: 0px;
        left: 0px;
        right: 0px;
        font-size: 1rem;
        line-height: 1.42857;
        overflow-x: hidden;
        background: inherit;
        tab-size: 4;
    }

    iframe {
        margin: auto;
    }

    a.url {
        word-break: break-all;
    }

    a:active, a:hover {
        outline: 0px;
    }

    .in-text-selection, ::selection {
        text-shadow: none;
        background: var(--select-text-bg-color);
        color: var(--select-text-font-color);
    }

    #write {
        margin: 0px auto;
        height: auto;
        width: inherit;
        word-break: normal;
        overflow-wrap: break-word;
        position: relative;
        white-space: normal;
        overflow-x: visible;
        padding-top: 40px;
    }

    #write.first-line-indent p {
        text-indent: 2em;
    }

    #write.first-line-indent li p, #write.first-line-indent p * {
        text-indent: 0px;
    }

    #write.first-line-indent li {
        margin-left: 2em;
    }

    .for-image #write {
        padding-left: 8px;
        padding-right: 8px;
    }

    body.typora-export {
        padding-left: 30px;
        padding-right: 30px;
    }

    .typora-export .footnote-line, .typora-export li, .typora-export p {
        white-space: pre-wrap;
    }

    .typora-export .task-list-item input {
        pointer-events: none;
    }

    @media screen and (max-width: 500px) {
        body.typora-export {
            padding-left: 0px;
            padding-right: 0px;
        }

        #write {
            padding-left: 20px;
            padding-right: 20px;
        }

        .CodeMirror-sizer {
            margin-left: 0px !important;
        }

        .CodeMirror-gutters {
            display: none !important;
        }
    }

    #write li > figure:last-child {
        margin-bottom: 0.5rem;
    }

    #write ol, #write ul {
        position: relative;
    }

    img {
        max-width: 100%;
        vertical-align: middle;
        image-orientation: from-image;
    }

    button, input, select, textarea {
        color: inherit;
        font: inherit;
    }

    input[type="checkbox"], input[type="radio"] {
        line-height: normal;
        padding: 0px;
    }

    *, ::after, ::before {
        box-sizing: border-box;
    }

    #write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre {
        width: inherit;
    }

    #write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p {
        position: relative;
    }

    p {
        line-height: inherit;
    }

    h1, h2, h3, h4, h5, h6 {
        break-after: avoid-page;
        break-inside: avoid;
        orphans: 4;
    }

    p {
        orphans: 4;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    h3 {
        font-size: 1.6rem;
    }

    h4 {
        font-size: 1.4rem;
    }

    h5 {
        font-size: 1.2rem;
    }

    h6 {
        font-size: 1rem;
    }

    .md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .hidden {
        display: none;
    }

    .md-blockmeta {
        color: rgb(204, 204, 204);
        font-weight: 700;
        font-style: italic;
    }

    a {
        cursor: pointer;
    }

    sup.md-footnote {
        padding: 2px 4px;
        background-color: rgba(238, 238, 238, 0.7);
        color: rgb(85, 85, 85);
        border-radius: 4px;
        cursor: pointer;
    }

    sup.md-footnote a, sup.md-footnote a:hover {
        color: inherit;
        text-transform: inherit;
        text-decoration: inherit;
    }

    #write input[type="checkbox"] {
        cursor: pointer;
        width: inherit;
        height: inherit;
    }

    figure {
        overflow-x: auto;
        margin: 1.2em 0px;
        max-width: calc(100% + 16px);
        padding: 0px;
    }

    figure > table {
        margin: 0px;
    }

    tr {
        break-inside: avoid;
        break-after: auto;
    }

    thead {
        display: table-header-group;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0px;
        width: 100%;
        overflow: auto;
        break-inside: auto;
        text-align: left;
    }

    table.md-table td {
        min-width: 32px;
    }

    .CodeMirror-gutters {
        border-right: 0px;
        background-color: inherit;
    }

    .CodeMirror-linenumber {
        user-select: none;
    }

    .CodeMirror {
        text-align: left;
    }

    .CodeMirror-placeholder {
        opacity: 0.3;
    }

    .CodeMirror pre {
        padding: 0px 4px;
    }

    .CodeMirror-lines {
        padding: 0px;
    }

    div.hr:focus {
        cursor: none;
    }

    #write pre {
        white-space: pre-wrap;
    }

    #write.fences-no-line-wrapping pre {
        white-space: pre;
    }

    #write pre.ty-contain-cm {
        white-space: normal;
    }

    .CodeMirror-gutters {
        margin-right: 4px;
    }

    .md-fences {
        font-size: 0.9rem;
        display: block;
        break-inside: avoid;
        text-align: left;
        overflow: visible;
        white-space: pre;
        background: inherit;
        position: relative !important;
    }

    .md-diagram-panel {
        width: 100%;
        margin-top: 10px;
        text-align: center;
        padding-top: 0px;
        padding-bottom: 8px;
        overflow-x: auto;
    }

    #write .md-fences.mock-cm {
        white-space: pre-wrap;
    }

    .md-fences.md-fences-with-lineno {
        padding-left: 0px;
    }

    #write.fences-no-line-wrapping .md-fences.mock-cm {
        white-space: pre;
        overflow-x: auto;
    }

    .md-fences.mock-cm.md-fences-with-lineno {
        padding-left: 8px;
    }

    .CodeMirror-line, twitterwidget {
        break-inside: avoid;
    }

    .footnotes {
        opacity: 0.8;
        font-size: 0.9rem;
        margin-top: 1em;
        margin-bottom: 1em;
    }

    .footnotes + .footnotes {
        margin-top: 0px;
    }

    .md-reset {
        margin: 0px;
        padding: 0px;
        border: 0px;
        outline: 0px;
        vertical-align: top;
        background: 0px 0px;
        text-decoration: none;
        text-shadow: none;
        float: none;
        position: static;
        width: auto;
        height: auto;
        white-space: nowrap;
        cursor: inherit;
        -webkit-tap-highlight-color: transparent;
        line-height: normal;
        font-weight: 400;
        text-align: left;
        box-sizing: content-box;
        direction: ltr;
    }

    li div {
        padding-top: 0px;
    }

    blockquote {
        margin: 1rem 0px;
    }

    li .mathjax-block, li p {
        margin: 0.5rem 0px;
    }

    li {
        margin: 0px;
        position: relative;
    }

    blockquote > :last-child {
        margin-bottom: 0px;
    }

    blockquote > :first-child, li > :first-child {
        margin-top: 0px;
    }

    .footnotes-area {
        color: rgb(136, 136, 136);
        margin-top: 0.714rem;
        padding-bottom: 0.143rem;
        white-space: normal;
    }

    #write .footnote-line {
        white-space: pre-wrap;
    }

    @media print {
        body, html {
            border: 1px solid transparent;
            height: 99%;
            break-after: avoid;
            break-before: avoid;
            font-variant-ligatures: no-common-ligatures;
        }

        #write {
            margin-top: 0px;
            padding-top: 0px;
            border-color: transparent !important;
        }

        .typora-export * {
            -webkit-print-color-adjust: exact;
        }

        html.blink-to-pdf {
            font-size: 13px;
        }

        .typora-export #write {
            break-after: avoid;
        }

        .typora-export #write::after {
            height: 0px;
        }

        .is-mac table {
            break-inside: avoid;
        }
    }

    .footnote-line {
        margin-top: 0.714em;
        font-size: 0.7em;
    }

    a img, img a {
        cursor: pointer;
    }

    pre.md-meta-block {
        font-size: 0.8rem;
        min-height: 0.8rem;
        white-space: pre-wrap;
        background: rgb(204, 204, 204);
        display: block;
        overflow-x: hidden;
    }

    p > .md-image:only-child:not(.md-img-error) img, p > img:only-child {
        display: block;
        margin: auto;
    }

    #write.first-line-indent p > .md-image:only-child:not(.md-img-error) img {
        left: -2em;
        position: relative;
    }

    p > .md-image:only-child {
        display: inline-block;
        width: 100%;
    }

    #write .MathJax_Display {
        margin: 0.8em 0px 0px;
    }

    .md-math-block {
        width: 100%;
    }

    .md-math-block:not(:empty)::after {
        display: none;
    }

    [contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus {
        outline: 0px;
        box-shadow: none;
    }

    .md-task-list-item {
        position: relative;
        list-style-type: none;
    }

    .task-list-item.md-task-list-item {
        padding-left: 0px;
    }

    .md-task-list-item > input {
        position: absolute;
        top: 0px;
        left: 0px;
        margin-left: -1.2em;
        margin-top: calc(1em - 10px);
        border: none;
    }

    .math {
        font-size: 1rem;
    }

    .md-toc {
        min-height: 3.58rem;
        position: relative;
        font-size: 0.9rem;
        border-radius: 10px;
    }

    .md-toc-content {
        position: relative;
        margin-left: 0px;
    }

    .md-toc-content::after, .md-toc::after {
        display: none;
    }

    .md-toc-item {
        display: block;
        color: rgb(65, 131, 196);
    }

    .md-toc-item a {
        text-decoration: none;
    }

    .md-toc-inner:hover {
        text-decoration: underline;
    }

    .md-toc-inner {
        display: inline-block;
        cursor: pointer;
    }

    .md-toc-h1 .md-toc-inner {
        margin-left: 0px;
        font-weight: 700;
    }

    .md-toc-h2 .md-toc-inner {
        margin-left: 2em;
    }

    .md-toc-h3 .md-toc-inner {
        margin-left: 4em;
    }

    .md-toc-h4 .md-toc-inner {
        margin-left: 6em;
    }

    .md-toc-h5 .md-toc-inner {
        margin-left: 8em;
    }

    .md-toc-h6 .md-toc-inner {
        margin-left: 10em;
    }

    @media screen and (max-width: 48em) {
        .md-toc-h3 .md-toc-inner {
            margin-left: 3.5em;
        }

        .md-toc-h4 .md-toc-inner {
            margin-left: 5em;
        }

        .md-toc-h5 .md-toc-inner {
            margin-left: 6.5em;
        }

        .md-toc-h6 .md-toc-inner {
            margin-left: 8em;
        }
    }

    a.md-toc-inner {
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
        line-height: inherit;
    }

    .footnote-line a:not(.reversefootnote) {
        color: inherit;
    }

    .md-attr {
        display: none;
    }

    .md-fn-count::after {
        content: ".";
    }

    code, pre, samp, tt {
        font-family: var(--monospace);
    }

    kbd {
        margin: 0px 0.1em;
        padding: 0.1em 0.6em;
        font-size: 0.8em;
        color: rgb(36, 39, 41);
        background: rgb(255, 255, 255);
        border: 1px solid rgb(173, 179, 185);
        border-radius: 3px;
        box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset;
        white-space: nowrap;
        vertical-align: middle;
    }

    .md-comment {
        color: rgb(162, 127, 3);
        opacity: 0.8;
        font-family: var(--monospace);
    }

    code {
        text-align: left;
        vertical-align: initial;
    }

    a.md-print-anchor {
        white-space: pre !important;
        border-width: initial !important;
        border-style: none !important;
        border-color: initial !important;
        display: inline-block !important;
        position: absolute !important;
        width: 1px !important;
        right: 0px !important;
        outline: 0px !important;
        background: 0px 0px !important;
        text-decoration: initial !important;
        text-shadow: initial !important;
    }

    .md-inline-math .MathJax_SVG .noError {
        display: none !important;
    }

    .html-for-mac .inline-math-svg .MathJax_SVG {
        vertical-align: 0.2px;
    }

    .md-math-block .MathJax_SVG_Display {
        text-align: center;
        margin: 0px;
        position: relative;
        text-indent: 0px;
        max-width: none;
        max-height: none;
        min-height: 0px;
        min-width: 100%;
        width: auto;
        overflow-y: hidden;
        display: block !important;
    }

    .MathJax_SVG_Display, .md-inline-math .MathJax_SVG_Display {
        width: auto;
        margin: inherit;
        display: inline-block !important;
    }

    .MathJax_SVG .MJX-monospace {
        font-family: var(--monospace);
    }

    .MathJax_SVG .MJX-sans-serif {
        font-family: sans-serif;
    }

    .MathJax_SVG {
        display: inline;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        zoom: 90%;
        text-indent: 0px;
        text-align: left;
        text-transform: none;
        letter-spacing: normal;
        word-spacing: normal;
        overflow-wrap: normal;
        white-space: nowrap;
        float: none;
        direction: ltr;
        max-width: none;
        max-height: none;
        min-width: 0px;
        min-height: 0px;
        border: 0px;
        padding: 0px;
        margin: 0px;
    }

    .MathJax_SVG * {
        transition: none 0s ease 0s;
    }

    .MathJax_SVG_Display svg {
        vertical-align: middle !important;
        margin-bottom: 0px !important;
        margin-top: 0px !important;
    }

    .os-windows.monocolor-emoji .md-emoji {
        font-family: "Segoe UI Symbol", sans-serif;
    }

    .md-diagram-panel > svg {
        max-width: 100%;
    }

    [lang="flow"] svg, [lang="mermaid"] svg {
        max-width: 100%;
        height: auto;
    }

    [lang="mermaid"] .node text {
        font-size: 1rem;
    }

    table tr th {
        border-bottom: 0px;
    }

    video {
        max-width: 100%;
        display: block;
        margin: 0px auto;
    }

    iframe {
        max-width: 100%;
        width: 100%;
        border: none;
    }

    .highlight td, .highlight tr {
        border: 0px;
    }

    svg[id^="mermaidChart"] {
        line-height: 1em;
    }

    mark {
        background: rgb(255, 255, 0);
        color: rgb(0, 0, 0);
    }

    .md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong {
        color: inherit;
    }

    mark .md-meta {
        color: rgb(0, 0, 0);
        opacity: 0.3 !important;
    }

    @media print {
        .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 {
            break-inside: avoid;
        }
    }


    .CodeMirror {
        height: auto;
    }

    .CodeMirror.cm-s-inner {
        background: inherit;
    }

    .CodeMirror-scroll {
        overflow: auto hidden;
        z-index: 3;
    }

    .CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler {
        background-color: rgb(255, 255, 255);
    }

    .CodeMirror-gutters {
        border-right: 1px solid rgb(221, 221, 221);
        background: inherit;
        white-space: nowrap;
    }

    .CodeMirror-linenumber {
        padding: 0px 3px 0px 5px;
        text-align: right;
        color: rgb(153, 153, 153);
    }

    .cm-s-inner .cm-keyword {
        color: rgb(119, 0, 136);
    }

    .cm-s-inner .cm-atom, .cm-s-inner.cm-atom {
        color: rgb(34, 17, 153);
    }

    .cm-s-inner .cm-number {
        color: rgb(17, 102, 68);
    }

    .cm-s-inner .cm-def {
        color: rgb(0, 0, 255);
    }

    .cm-s-inner .cm-variable {
        color: rgb(0, 0, 0);
    }

    .cm-s-inner .cm-variable-2 {
        color: rgb(0, 85, 170);
    }

    .cm-s-inner .cm-variable-3 {
        color: rgb(0, 136, 85);
    }

    .cm-s-inner .cm-string {
        color: rgb(170, 17, 17);
    }

    .cm-s-inner .cm-property {
        color: rgb(0, 0, 0);
    }

    .cm-s-inner .cm-operator {
        color: rgb(152, 26, 26);
    }

    .cm-s-inner .cm-comment, .cm-s-inner.cm-comment {
        color: rgb(170, 85, 0);
    }

    .cm-s-inner .cm-string-2 {
        color: rgb(255, 85, 0);
    }

    .cm-s-inner .cm-meta {
        color: rgb(85, 85, 85);
    }

    .cm-s-inner .cm-qualifier {
        color: rgb(85, 85, 85);
    }

    .cm-s-inner .cm-builtin {
        color: rgb(51, 0, 170);
    }

    .cm-s-inner .cm-bracket {
        color: rgb(153, 153, 119);
    }

    .cm-s-inner .cm-tag {
        color: rgb(17, 119, 0);
    }

    .cm-s-inner .cm-attribute {
        color: rgb(0, 0, 204);
    }

    .cm-s-inner .cm-header, .cm-s-inner.cm-header {
        color: rgb(0, 0, 255);
    }

    .cm-s-inner .cm-quote, .cm-s-inner.cm-quote {
        color: rgb(0, 153, 0);
    }

    .cm-s-inner .cm-hr, .cm-s-inner.cm-hr {
        color: rgb(153, 153, 153);
    }

    .cm-s-inner .cm-link, .cm-s-inner.cm-link {
        color: rgb(0, 0, 204);
    }

    .cm-negative {
        color: rgb(221, 68, 68);
    }

    .cm-positive {
        color: rgb(34, 153, 34);
    }

    .cm-header, .cm-strong {
        font-weight: 700;
    }

    .cm-del {
        text-decoration: line-through;
    }

    .cm-em {
        font-style: italic;
    }

    .cm-link {
        text-decoration: underline;
    }

    .cm-error {
        color: red;
    }

    .cm-invalidchar {
        color: red;
    }

    .cm-constant {
        color: rgb(38, 139, 210);
    }

    .cm-defined {
        color: rgb(181, 137, 0);
    }

    div.CodeMirror span.CodeMirror-matchingbracket {
        color: rgb(0, 255, 0);
    }

    div.CodeMirror span.CodeMirror-nonmatchingbracket {
        color: rgb(255, 34, 34);
    }

    .cm-s-inner .CodeMirror-activeline-background {
        background: inherit;
    }

    .CodeMirror {
        position: relative;
        overflow: hidden;
    }

    .CodeMirror-scroll {
        height: 100%;
        outline: 0px;
        position: relative;
        box-sizing: content-box;
        background: inherit;
    }

    .CodeMirror-sizer {
        position: relative;
    }

    .CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar {
        position: absolute;
        z-index: 6;
        display: none;
    }

    .CodeMirror-vscrollbar {
        right: 0px;
        top: 0px;
        overflow: hidden;
    }

    .CodeMirror-hscrollbar {
        bottom: 0px;
        left: 0px;
        overflow: hidden;
    }

    .CodeMirror-scrollbar-filler {
        right: 0px;
        bottom: 0px;
    }

    .CodeMirror-gutter-filler {
        left: 0px;
        bottom: 0px;
    }

    .CodeMirror-gutters {
        position: absolute;
        left: 0px;
        top: 0px;
        padding-bottom: 30px;
        z-index: 3;
    }

    .CodeMirror-gutter {
        white-space: normal;
        height: 100%;
        box-sizing: content-box;
        padding-bottom: 30px;
        margin-bottom: -32px;
        display: inline-block;
    }

    .CodeMirror-gutter-wrapper {
        position: absolute;
        z-index: 4;
        background: 0px 0px !important;
        border: none !important;
    }

    .CodeMirror-gutter-background {
        position: absolute;
        top: 0px;
        bottom: 0px;
        z-index: 4;
    }

    .CodeMirror-gutter-elt {
        position: absolute;
        cursor: default;
        z-index: 4;
    }

    .CodeMirror-lines {
        cursor: text;
    }

    .CodeMirror pre {
        border-radius: 0px;
        border-width: 0px;
        background: 0px 0px;
        font-family: inherit;
        font-size: inherit;
        margin: 0px;
        white-space: pre;
        overflow-wrap: normal;
        color: inherit;
        z-index: 2;
        position: relative;
        overflow: visible;
    }

    .CodeMirror-wrap pre {
        overflow-wrap: break-word;
        white-space: pre-wrap;
        word-break: normal;
    }

    .CodeMirror-code pre {
        border-right: 30px solid transparent;
        width: fit-content;
    }

    .CodeMirror-wrap .CodeMirror-code pre {
        border-right: none;
        width: auto;
    }

    .CodeMirror-linebackground {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        z-index: 0;
    }

    .CodeMirror-linewidget {
        position: relative;
        z-index: 2;
        overflow: auto;
    }

    .CodeMirror-wrap .CodeMirror-scroll {
        overflow-x: hidden;
    }

    .CodeMirror-measure {
        position: absolute;
        width: 100%;
        height: 0px;
        overflow: hidden;
        visibility: hidden;
    }

    .CodeMirror-measure pre {
        position: static;
    }

    .CodeMirror div.CodeMirror-cursor {
        position: absolute;
        visibility: hidden;
        border-right: none;
        width: 0px;
    }

    .CodeMirror div.CodeMirror-cursor {
        visibility: hidden;
    }

    .CodeMirror-focused div.CodeMirror-cursor {
        visibility: inherit;
    }

    .cm-searching {
        background: rgba(255, 255, 0, 0.4);
    }

    @media print {
        .CodeMirror div.CodeMirror-cursor {
            visibility: hidden;
        }
    }


    :root {
        --side-bar-bg-color: #fafafa;
        --control-text-color: #777;
    }

    @include-when-export url(https://fonts.loli.net/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);

    /* open-sans-regular - latin-ext_latin */
    /* open-sans-italic - latin-ext_latin */
    /* open-sans-700 - latin-ext_latin */
    /* open-sans-700italic - latin-ext_latin */
    html {
        font-size: 16px;
    }

    body {
        font-family: "Open Sans", "Clear Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
        color: rgb(51, 51, 51);
        line-height: 1.6;
    }

    #write {
        max-width: 860px;
        margin: 0 auto;
        padding: 30px;
        padding-bottom: 100px;
    }

    @media only screen and (min-width: 1400px) {
        #write {
            max-width: 1024px;
        }
    }

    @media only screen and (min-width: 1800px) {
        #write {
            max-width: 1200px;
        }
    }

    #write > ul:first-child,
    #write > ol:first-child {
        margin-top: 30px;
    }

    a {
        color: #4183C4;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 1rem;
        font-weight: bold;
        line-height: 1.4;
        cursor: text;
    }

    h1:hover a.anchor,
    h2:hover a.anchor,
    h3:hover a.anchor,
    h4:hover a.anchor,
    h5:hover a.anchor,
    h6:hover a.anchor {
        text-decoration: none;
    }

    h1 tt,
    h1 code {
        font-size: inherit;
    }

    h2 tt,
    h2 code {
        font-size: inherit;
    }

    h3 tt,
    h3 code {
        font-size: inherit;
    }

    h4 tt,
    h4 code {
        font-size: inherit;
    }

    h5 tt,
    h5 code {
        font-size: inherit;
    }

    h6 tt,
    h6 code {
        font-size: inherit;
    }

    h1 {
        font-size: 2.25em;
        line-height: 1.2;
        border-bottom: 1px solid #eee;
    }

    h2 {
        font-size: 1.75em;
        line-height: 1.225;
        border-bottom: 1px solid #eee;
    }

    /*@media print {
    .typora-export h1,
    .typora-export h2 {
        border-bottom: none;
        padding-bottom: initial;
    }

    .typora-export h1::after,
    .typora-export h2::after {
        content: "";
        display: block;
        height: 100px;
        margin-top: -96px;
        border-top: 1px solid #eee;
    }
}*/

    h3 {
        font-size: 1.5em;
        line-height: 1.43;
    }

    h4 {
        font-size: 1.25em;
    }

    h5 {
        font-size: 1em;
    }

    h6 {
        font-size: 1em;
        color: #777;
    }

    p,
    blockquote,
    ul,
    ol,
    dl,
    table {
        margin: 0.8em 0;
    }

    li > ol,
    li > ul {
        margin: 0 0;
    }

    hr {
        height: 2px;
        padding: 0;
        margin: 16px 0;
        background-color: #e7e7e7;
        border: 0 none;
        overflow: hidden;
        box-sizing: content-box;
    }

    li p.first {
        display: inline-block;
    }

    ul,
    ol {
        padding-left: 30px;
    }

    ul:first-child,
    ol:first-child {
        margin-top: 0;
    }

    ul:last-child,
    ol:last-child {
        margin-bottom: 0;
    }

    blockquote {
        border-left: 4px solid #dfe2e5;
        padding: 0 15px;
        color: #777777;
    }

    blockquote blockquote {
        padding-right: 0;
    }

    table {
        padding: 0;
        word-break: initial;
    }

    table tr {
        border-top: 1px solid #dfe2e5;
        margin: 0;
        padding: 0;
    }

    table tr:nth-child(2n),
    thead {
        background-color: #f8f8f8;
    }

    table tr th {
        font-weight: bold;
        border: 1px solid #dfe2e5;
        border-bottom: 0;
        margin: 0;
        padding: 6px 13px;
    }

    table tr td {
        border: 1px solid #dfe2e5;
        margin: 0;
        padding: 6px 13px;
    }

    table tr th:first-child,
    table tr td:first-child {
        margin-top: 0;
    }

    table tr th:last-child,
    table tr td:last-child {
        margin-bottom: 0;
    }

    .CodeMirror-lines {
        padding-left: 4px;
    }

    .code-tooltip {
        box-shadow: 0 1px 1px 0 rgba(0, 28, 36, .3);
        border-top: 1px solid #eef2f2;
    }

    .md-fences,
    code,
    tt {
        border: 1px solid #e7eaed;
        background-color: #f8f8f8;
        border-radius: 3px;
        padding: 0;
        padding: 2px 4px 0px 4px;
        font-size: 0.9em;
    }

    code {
        background-color: #f3f4f4;
        padding: 0 2px 0 2px;
    }

    .md-fences {
        margin-bottom: 15px;
        margin-top: 15px;
        padding-top: 8px;
        padding-bottom: 6px;
    }


    .md-task-list-item > input {
        margin-left: -1.3em;
    }

    @media print {
        html {
            font-size: 13px;
        }

        table,
        pre {
            page-break-inside: avoid;
        }

        pre {
            word-wrap: break-word;
        }
    }

    .md-fences {
        background-color: #f8f8f8;
    }

    #write pre.md-meta-block {
        padding: 1rem;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f7f7f7;
        border: 0;
        border-radius: 3px;
        color: #777777;
        margin-top: 0 !important;
    }

    .mathjax-block > .code-tooltip {
        bottom: .375rem;
    }

    .md-mathjax-midline {
        background: #fafafa;
    }

    #write > h3.md-focus:before {
        left: -1.5625rem;
        top: .375rem;
    }

    #write > h4.md-focus:before {
        left: -1.5625rem;
        top: .285714286rem;
    }

    #write > h5.md-focus:before {
        left: -1.5625rem;
        top: .285714286rem;
    }

    #write > h6.md-focus:before {
        left: -1.5625rem;
        top: .285714286rem;
    }

    .md-image > .md-meta {
        /*border: 1px solid #ddd;*/
        border-radius: 3px;
        padding: 2px 0px 0px 4px;
        font-size: 0.9em;
        color: inherit;
    }

    .md-tag {
        color: #a7a7a7;
        opacity: 1;
    }

    .md-toc {
        margin-top: 20px;
        padding-bottom: 20px;
    }

    .sidebar-tabs {
        border-bottom: none;
    }

    #typora-quick-open {
        border: 1px solid #ddd;
        background-color: #f8f8f8;
    }

    #typora-quick-open-item {
        background-color: #FAFAFA;
        border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;
        border-style: solid;
        border-width: 1px;
    }

    /** focus mode */
    .on-focus-mode blockquote {
        border-left-color: rgba(85, 85, 85, 0.12);
    }

    header, .context-menu, .megamenu-content, footer {
        font-family: "Segoe UI", "Arial", sans-serif;
    }

    .file-node-content:hover .file-node-icon,
    .file-node-content:hover .file-node-open-state {
        visibility: visible;
    }

    .mac-seamless-mode #typora-sidebar {
        background-color: #fafafa;
        background-color: var(--side-bar-bg-color);
    }

    .md-lang {
        color: #b4654d;
    }

    .html-for-mac .context-menu {
        --item-hover-bg-color: #E6F0FE;
    }

    #md-notification .btn {
        border: 0;
    }

    .dropdown-menu .divider {
        border-color: #e5e5e5;
    }

    .ty-preferences .window-content {
        background-color: #fafafa;
    }

    .ty-preferences .nav-group-item.active {
        color: white;
        background: #999;
    }


    </style>
</head>
<body class='typora-export os-windows'>
<div id='write' class=''><p><a name="paths"></a></p>
    <h2><a name="paths" class="md-header-anchor"></a><span>Paths</span></h2>
    <p><a name="modestagepostusingpost"></a></p>
    <h3><a name="指定相位" class="md-header-anchor"></a><span>指定相位</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /controlSignal/modeStage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>chara</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>chara</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>mode</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>mode</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageLen</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageLen</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageNo</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageNo</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>control-signal-controller</span></li>
    </ul>
    <p><a name="modestageusingget"></a></p>
    <h3><a name="modestage" class="md-header-anchor"></a><span>modeStage</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /controlSignal/modeStage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n85" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>chara</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>chara</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>mode</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>mode</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageLen</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageLen</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageNo</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageNo</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n118" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n141" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n146" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>control-signal-controller</span></li>
    </ul>
    <p><a name="addusingpost"></a></p>
    <h3><a name="设置信号机标准相位配置" class="md-header-anchor"></a><span>设置信号机标准相位配置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /defaultStageConfig/{brandId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n154" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>brandId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>brandId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>defaultStageConfigs</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>defaultStageConfigs</span></td>
                <td><span>&lt; </span><a
                        href='#defaultstageconfig'><span>DefaultStageConfig</span></a><span> &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n172" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n199" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n204" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n209" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>default-stage-config-controller</span></li>
    </ul>
    <p><a name="getusingget"></a></p>
    <h3><a name="查询信号机标准相位配置" class="md-header-anchor"></a><span>查询信号机标准相位配置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /defaultStageConfig/{brandId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n217" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>brandId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>brandId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n230" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n253" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n258" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>default-stage-config-controller</span></li>
    </ul>
    <p><a name="loaddatausingget"></a></p>
    <h3><a name="调看系统参数以及路口参数" class="md-header-anchor"></a><span>调看系统参数以及路口参数</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /ht/loadData/{type}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n266" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>enum (LS, HT, LP)</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n279" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n302" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n307" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="loaddatausingget_1"></a></p>
    <h3><a name="路口参数" class="md-header-anchor"></a><span>路口参数</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /ht/loadSignalData/{lessignalControlerID}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n315" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>lessignalControlerID</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>lessignalControlerID</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n328" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n351" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n356" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="loadsignaldatausingget"></a></p>
    <h3><a name="路口参数-n361" class="md-header-anchor"></a><span>路口参数</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /ht/loadSignalData/{lessignalControlerID}/{type}/{no}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n364" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>lessignalControlerID</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>lessignalControlerID</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>no</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>no</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n387" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n410" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n415" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="modestageusingpost"></a></p>
    <h3><a name="指定相位-n420" class="md-header-anchor"></a><span>指定相位</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/modeStage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n423" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>lock</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>lock</span></td>
                <td><span>boolean</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageLen</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageLen</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>stageNo</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageNo</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n451" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n478" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n483" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n488" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="plansetusingpost"></a></p>
    <h3><a name="方案参数设置" class="md-header-anchor"></a><span>方案参数设置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/plan</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n496" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>crossSchedule</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>crossSchedule</span></td>
                <td><a href='#planparam'><span>PlanParam</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n514" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n541" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n546" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n551" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="prioritycyclerequestusingpost"></a></p>
    <h3><a name="公交优先周期优化" class="md-header-anchor"></a><span>公交优先周期优化</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/priorityCycle</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n559" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>priorityCycleRequest</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>priorityCycleRequest</span></td>
                <td><a href='#prioritycyclerequest'><span>PriorityCycleRequest</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n577" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n604" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n609" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n614" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="prioritystagerequestusingpost"></a></p>
    <h3><a name="公交优先阶段优化" class="md-header-anchor"></a><span>公交优先阶段优化</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/priorityStage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n622" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>priorityStageRequest</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>priorityStageRequest</span></td>
                <td><a href='#prioritystagerequest'><span>PriorityStageRequest</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n640" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n667" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n672" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n677" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="schedulesetusingpost"></a></p>
    <h3><a name="调度参数设置" class="md-header-anchor"></a><span>调度参数设置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/schedule</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n685" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>crossSchedule</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>crossSchedule</span></td>
                <td><a href='#crossschedule'><span>CrossSchedule</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n703" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n730" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n735" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n740" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="stagesetusingpost"></a></p>
    <h3><a name="阶段参数设置" class="md-header-anchor"></a><span>阶段参数设置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /ht/stage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n748" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>stageParams</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageParams</span></td>
                <td><span>&lt; </span><a href='#stageparam'><span>StageParam</span></a><span> &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n766" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n793" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n798" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n803" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="signalstatususingget"></a></p>
    <h3><a name="路口状态" class="md-header-anchor"></a><span>路口状态</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /ht/status/{lesSignalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n811" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>lesSignalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>lesSignalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n824" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n847" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n852" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>ht-signal-controller</span></li>
    </ul>
    <p><a name="loadsignalsegmentusingpost"></a></p>
    <h3><a name="loadsignalsegment" class="md-header-anchor"></a><span>loadSignalSegment</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /load/segment/{signalId}/{type}/{iden}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n860" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>iden</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>iden</span></td>
                <td><span>boolean</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>segments</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>segments</span></td>
                <td><span>&lt; </span><a href='#segment'><span>Segment</span></a><span> &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n888" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n915" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n920" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n925" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-controller</span></li>
    </ul>
    <p><a name="looklimitgreenusingpost"></a></p>
    <h3><a name="looklimitgreen" class="md-header-anchor"></a><span>lookLimitGreen</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /loadSignal/limitGreen/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n933" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>greenLimitMap</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>greenLimitMap</span></td>
                <td><span>&lt; string, </span><a href='#greenlimit'><span>GreenLimit</span></a><span> &gt; map</span>
                </td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n951" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n978" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n983" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n988" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-signal-controller</span></li>
    </ul>
    <p><a name="loadplanusingpost"></a></p>
    <h3><a name="loadplan" class="md-header-anchor"></a><span>loadPlan</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /loadSignal/plans/{signalId}/{noPlan}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n996" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>noPlan</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>noPlan</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>plan</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>plan</span></td>
                <td><a href='#plan'><span>Plan</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1019" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n1046" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n1051" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1056" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-signal-controller</span></li>
    </ul>
    <p><a name="loadsignalsegmentusingpost_1"></a></p>
    <h3><a name="loadsignalsegment-n1061" class="md-header-anchor"></a><span>loadSignalSegment</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /loadSignal/segment/{signalId}/{type}/{iden}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1064" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>iden</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>iden</span></td>
                <td><span>boolean</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>segments</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>segments</span></td>
                <td><span>&lt; </span><a href='#segment'><span>Segment</span></a><span> &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1092" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n1119" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n1124" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1129" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-signal-controller</span></li>
    </ul>
    <p><a name="stagelampusingpost"></a></p>
    <h3><a name="stagelamp" class="md-header-anchor"></a><span>stageLamp</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /loadSignal/stageLamp/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1137" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>stageLampMap</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageLampMap</span></td>
                <td><span>&lt; string, </span><a href='#stagelampstagelampstagelamp'><span>StageLamp</span></a><span> &gt; map</span>
                </td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1155" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n1182" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n1187" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1192" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-signal-controller</span></li>
    </ul>
    <p><a name="looktmplightusingpost"></a></p>
    <h3><a name="looktmplight" class="md-header-anchor"></a><span>lookTmpLight</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /loadSignal/tmpLight/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1200" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>tmpLights</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>tmpLights</span></td>
                <td><span>&lt; string, </span><a href='#tmplight'><span>TmpLight</span></a><span> &gt; map</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1218" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n1245" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n1250" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1255" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>load-signal-controller</span></li>
    </ul>
    <p><a name="loaddatausingget_2"></a></p>
    <h3><a name="路口参数-n1260" class="md-header-anchor"></a><span>路口参数</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /loadSignalData/{lessignalControlerID}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1263" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>lessignalControlerID</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>lessignalControlerID</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1276" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1299" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1304" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>reload-signal-controller</span></li>
    </ul>
    <p><a name="lampstatususingget"></a></p>
    <h3><a name="lampstatus" class="md-header-anchor"></a><span>lampStatus</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/lampStatus/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1312" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1325" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1348" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1353" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="looklimitgreenusingget"></a></p>
    <h3><a name="looklimitgreen-n1358" class="md-header-anchor"></a><span>lookLimitGreen</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/limitGreen/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1361" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1374" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1397" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1402" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="lookplanusingget_1"></a></p>
    <h3><a name="lookplan" class="md-header-anchor"></a><span>lookPlan</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/plans/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1410" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1423" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1446" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1451" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="lookplanusingget"></a></p>
    <h3><a name="lookplan-n1456" class="md-header-anchor"></a><span>lookPlan</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/plans/{signalId}/{noPlan}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1459" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>noPlan</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>noPlan</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1477" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1500" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1505" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="dayusingget"></a></p>
    <h3><a name="day" class="md-header-anchor"></a><span>day</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/segment/{signalId}/day</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1513" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1526" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1549" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1554" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="looksegmentusingget"></a></p>
    <h3><a name="looksegment" class="md-header-anchor"></a><span>lookSegment</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/segment/{signalId}/{type}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1562" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1580" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1603" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1608" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="deleteusingdelete"></a></p>
    <h3><a name="delete" class="md-header-anchor"></a><span>delete</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">DELETE /look/segment/{signalId}/{type}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1616" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1634" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>204</span></strong></td>
                <td><span>No Content</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1657" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1662" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="stagelampusingget"></a></p>
    <h3><a name="stagelamp-n1667" class="md-header-anchor"></a><span>stageLamp</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/stageLamp/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1670" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1683" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1706" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1711" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="looktmplightusingget"></a></p>
    <h3><a name="looktmplight-n1716" class="md-header-anchor"></a><span>lookTmpLight</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /look/tmpLight/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1719" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1732" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1755" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1760" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-controller</span></li>
    </ul>
    <p><a name="looklimitgreenusingget_1"></a></p>
    <h3><a name="looklimitgreen-n1765" class="md-header-anchor"></a><span>lookLimitGreen</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/limitGreen/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1768" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1781" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1804" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1809" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="lookplanusingget_3"></a></p>
    <h3><a name="lookplan-n1814" class="md-header-anchor"></a><span>lookPlan</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/plans/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1817" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1830" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1853" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1858" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="lookplanusingget_2"></a></p>
    <h3><a name="lookplan-n1863" class="md-header-anchor"></a><span>lookPlan</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/plans/{signalId}/{noPlan}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1866" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>noPlan</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>noPlan</span></td>
                <td><span>integer (int32)</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1884" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1907" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1912" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="looksegmentusingget_1"></a></p>
    <h3><a name="looksegment-n1917" class="md-header-anchor"></a><span>lookSegment</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/segment/{signalId}/{type}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1920" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>type</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>type</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1938" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n1961" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n1966" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="stagelampusingget_1"></a></p>
    <h3><a name="stagelamp-n1971" class="md-header-anchor"></a><span>stageLamp</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/stageLamp/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n1974" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n1987" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2010" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2015" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="looktimeusingget"></a></p>
    <h3><a name="looktime" class="md-header-anchor"></a><span>lookTime</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/time/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2023" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2036" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2059" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2064" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="looktmplightusingget_1"></a></p>
    <h3><a name="looktmplight-n2069" class="md-header-anchor"></a><span>lookTmpLight</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /lookSignal/tmpLight/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2072" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2085" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2108" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2113" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>look-signal-controller</span></li>
    </ul>
    <p><a name="addusingpost_1"></a></p>
    <h3><a name="添加信号机" class="md-header-anchor"></a><span>添加信号机</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /signal/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2121" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2134" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2161" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2166" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2171" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-controller</span></li>
    </ul>
    <p><a name="updateusingput"></a></p>
    <h3><a name="修改信号机" class="md-header-anchor"></a><span>修改信号机</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">PUT /signal/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2179" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2192" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2219" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2224" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2229" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-controller</span></li>
    </ul>
    <p><a name="delusingdelete"></a></p>
    <h3><a name="删除信号机" class="md-header-anchor"></a><span>删除信号机</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">DELETE /signal/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2237" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2250" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>204</span></strong></td>
                <td><span>No Content</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2273" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2278" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-controller</span></li>
    </ul>
    <p><a name="signalstatususingpost"></a></p>
    <h3><a name="查看指定信号机状态" class="md-header-anchor"></a><span>查看指定信号机状态</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /signalStatus</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2286" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>signalIds</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalIds</span></td>
                <td><span>&lt; string &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2299" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2326" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2331" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2336" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-status-controller</span></li>
    </ul>
    <p><a name="allsignalstatususingget"></a></p>
    <h3><a name="查看所有信号机状态" class="md-header-anchor"></a><span>查看所有信号机状态</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /signalStatus</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="responses-n2344" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2367" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2372" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-status-controller</span></li>
    </ul>
    <p><a name="signalstatususingget_1"></a></p>
    <h3><a name="查看指定信号机状态-n2377" class="md-header-anchor"></a><span>查看指定信号机状态</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /signalStatus/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2380" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2393" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2416" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2421" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>signal-status-controller</span></li>
    </ul>
    <p><a name="addusingpost_2"></a></p>
    <h3><a name="设置信号机相位配置" class="md-header-anchor"></a><span>设置信号机相位配置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /stageConfig/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2429" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>stageConfigs</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>stageConfigs</span></td>
                <td><span>&lt; </span><a href='#stageconfig'><span>StageConfig</span></a><span> &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2447" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2474" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2479" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2484" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>stage-config-controller</span></li>
    </ul>
    <p><a name="getusingget_1"></a></p>
    <h3><a name="查询信号机相位配置" class="md-header-anchor"></a><span>查询信号机相位配置</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /stageConfig/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2492" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2505" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2528" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2533" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>stage-config-controller</span></li>
    </ul>
    <p><a name="getdatausingget"></a></p>
    <h3><a name="getdata" class="md-header-anchor"></a><span>getData</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /utc/data/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2541" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2554" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2577" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2582" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="getpatternusingget"></a></p>
    <h3><a name="getpattern" class="md-header-anchor"></a><span>getPattern</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /utc/patterns/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2590" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2603" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2626" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2631" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="getphasesusingget"></a></p>
    <h3><a name="getphases" class="md-header-anchor"></a><span>getPhases</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /utc/phases/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2639" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2652" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2675" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2680" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="prioritycyclerequestusingpost_1"></a></p>
    <h3><a name="公交优先周期优化-n2685" class="md-header-anchor"></a><span>公交优先周期优化</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /utc/priorityCycle</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2688" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>priorityCycleRequest</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>priorityCycleRequest</span></td>
                <td><a href='#prioritycyclerequest'><span>PriorityCycleRequest</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2706" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2733" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2738" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2743" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="prioritystagerequestusingpost_2"></a></p>
    <h3><a name="公交优先阶段优化-n2748" class="md-header-anchor"></a><span>公交优先阶段优化</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /utc/priorityStage</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2751" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>priorityStageRequest</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>priorityStageRequest</span></td>
                <td><a href='#prioritystagerequest'><span>PriorityStageRequest</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2769" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2796" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2801" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2806" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="prioritystagerequestusingpost_1"></a></p>
    <h3><a name="公交优先阶段优化-n2811" class="md-header-anchor"></a><span>公交优先阶段优化</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /utc/priorityStageMulti</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2814" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>priorityStageRequests</span></strong><span>  </span><br><em><span>required</span></em>
                </td>
                <td><span>priorityStageRequests</span></td>
                <td><span>&lt; </span><a href='#prioritystagerequest'><span>PriorityStageRequest</span></a><span> &gt; array</span>
                </td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2832" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n2859" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n2864" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2869" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="getscheduleusingget"></a></p>
    <h3><a name="getschedule" class="md-header-anchor"></a><span>getSchedule</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /utc/schedules/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2877" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2890" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2913" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2918" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="getstagesusingget"></a></p>
    <h3><a name="getstages" class="md-header-anchor"></a><span>getStages</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /utc/stages/{signalId}</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2926" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Path</span></strong></td>
                <td><strong><span>signalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signalId</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2939" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#3625c36a8ea9cf7285b4f7df3f9f33df'><span>JsonResult«object»</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n2962" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n2967" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>utc-controller</span></li>
    </ul>
    <p><a name="addwaveusingpost"></a></p>
    <h3><a name="新建绿波基础数据项" class="md-header-anchor"></a><span>新建绿波基础数据项</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /wave</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n2975" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>waveBean</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>waveBean</span></td>
                <td><a href='#wavebean'><span>WaveBean</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n2988" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n3015" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n3020" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3025" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-controller</span></li>
    </ul>
    <p><a name="getwaveusingget"></a></p>
    <h3><a name="获取所有绿波基础数据项" class="md-header-anchor"></a><span>获取所有绿波基础数据项</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /wave</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="responses-n3033" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n3056" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3061" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-controller</span></li>
    </ul>
    <p><a name="delwaveusingdelete"></a></p>
    <h3><a name="删除绿波数据项" class="md-header-anchor"></a><span>删除绿波数据项</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">DELETE /wave</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3069" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>waveId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>绿波编号</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3082" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>204</span></strong></td>
                <td><span>No Content</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n3105" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3110" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-controller</span></li>
    </ul>
    <p><a name="saveusingpost"></a></p>
    <h3><a name="保存绿波方案" class="md-header-anchor"></a><span>保存绿波方案</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /waveInfo</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3118" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>wave</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>wave</span></td>
                <td><a href='#wave'><span>Wave</span></a></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3131" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n3158" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n3163" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3168" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-detail-controller</span></li>
    </ul>
    <p><a name="getoneusingget"></a></p>
    <h3><a name="读取绿波方案数据项" class="md-header-anchor"></a><span>读取绿波方案数据项</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">GET /waveInfo</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3176" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>waveId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>绿波编号</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3189" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="produces-n3212" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3217" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-detail-controller</span></li>
    </ul>
    <p><a name="copywaveusingpost"></a></p>
    <h3><a name="复制绿波数据项" class="md-header-anchor"></a><span>复制绿波数据项</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /waveInfo/copy</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3225" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>waveId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>绿波编号</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3238" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n3265" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n3270" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3275" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-detail-controller</span></li>
    </ul>
    <p><a name="load2signalusingpost"></a></p>
    <h3><a name="根据传递的绿波id以及信号机id列表"
           class="md-header-anchor"></a><span>根据传递的绿波id以及信号机id列表</span></h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /waveInfo/loadSignal</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3283" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>waveId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>绿波编号</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Body</span></strong></td>
                <td><strong><span>signals</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>signals</span></td>
                <td><span>&lt; string &gt; array</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3301" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n3328" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n3333" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3338" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-detail-controller</span></li>
    </ul>
    <p><a name="synckeywaveusingpost"></a></p>
    <h3><a name="从信号机一般时段同步数据同步所有的方案以关键路口的为时段数据项" class="md-header-anchor"></a><span>从信号机一般时段同步数据,同步所有的方案,以关键路口的为时段数据项</span>
    </h3>
    <pre spellcheck="false" class="md-fences md-end-block ty-contain-cm modeLoaded" lang=""><div
            class="CodeMirror cm-s-inner CodeMirror-wrap" lang=""><div
            style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 0px; left: 8px;"><textarea
            autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
            style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div
            class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                                 cm-not-content="true"></div><div
            class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                         style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
            style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div
            role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div
            class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div
            class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div
            class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
            class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
            class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">POST /waveInfo/syncKey</span></pre></div></div></div></div></div></div><div
            style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div
            class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre>
    <h4><a name="parameters-n3346" class="md-header-anchor"></a><span>Parameters</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>Type</span></th>
                <th><span>Name</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>keySignalId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>keySignalId</span></td>
                <td><span>string</span></td>
            </tr>
            <tr>
                <td><strong><span>Query</span></strong></td>
                <td><strong><span>waveId</span></strong><span>  </span><br><em><span>required</span></em></td>
                <td><span>绿波编号</span></td>
                <td><span>string</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="responses-n3364" class="md-header-anchor"></a><span>Responses</span></h4>
    <figure>
        <table>
            <thead>
            <tr>
                <th><span>HTTP Code</span></th>
                <th><span>Description</span></th>
                <th><span>Schema</span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><strong><span>200</span></strong></td>
                <td><span>OK</span></td>
                <td><a href='#jsonresult'><span>JsonResult</span></a></td>
            </tr>
            <tr>
                <td><strong><span>201</span></strong></td>
                <td><span>Created</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>401</span></strong></td>
                <td><span>Unauthorized</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>403</span></strong></td>
                <td><span>Forbidden</span></td>
                <td><span>No Content</span></td>
            </tr>
            <tr>
                <td><strong><span>404</span></strong></td>
                <td><span>Not Found</span></td>
                <td><span>No Content</span></td>
            </tr>
            </tbody>
        </table>
    </figure>
    <h4><a name="consumes-n3391" class="md-header-anchor"></a><span>Consumes</span></h4>
    <ul>
        <li><code>application/json</code></li>
    </ul>
    <h4><a name="produces-n3396" class="md-header-anchor"></a><span>Produces</span></h4>
    <ul>
        <li><code>*/*</code></li>
    </ul>
    <h4><a name="tags-n3401" class="md-header-anchor"></a><span>Tags</span></h4>
    <ul>
        <li><span>wave-detail-controller</span></li>
    </ul>
    <p>&nbsp;</p>
    <p>&nbsp;</p></div>
</body>
</html>