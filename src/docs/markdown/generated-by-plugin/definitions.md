<a name="definitions"></a>

## Definitions

<a name="crossschedule"></a>

### CrossSchedule

|Name|Schema|
|---|---|
|**crossID**  <br>*optional*|string|
|**scheduleList**  <br>*optional*|[ScheduleList](#schedulelist)|

<a name="defaultstageconfig"></a>

### DefaultStageConfig

|Name|Schema|
|---|---|
|**brand**  <br>*optional*|string|
|**id**  <br>*optional*|string|
|**stageName**  <br>*optional*|string|
|**stageNo**  <br>*optional*|string|

<a name="greenlimit"></a>

### GreenLimit

|Name|Schema|
|---|---|
|**maxGreen**  <br>*optional*|integer (int32)|
|**minGreen**  <br>*optional*|integer (int32)|

<a name="jsonresult"></a>

### JsonResult

|Name|Schema|
|---|---|
|**data**  <br>*optional*|object|
|**errorCode**  <br>*optional*|string|
|**message**  <br>*optional*|string|
|**success**  <br>*optional*|boolean|

<a name="3625c36a8ea9cf7285b4f7df3f9f33df"></a>

### JsonResult«object»

|Name|Schema|
|---|---|
|**data**  <br>*optional*|object|
|**errorCode**  <br>*optional*|string|
|**message**  <br>*optional*|string|
|**success**  <br>*optional*|boolean|

<a name="phasenolist"></a>

### PhaseNoList

|Name|Schema|
|---|---|
|**phaseNo**  <br>*optional*|< string > array|

<a name="plan"></a>

### Plan

|Name|Schema|
|---|---|
|**cityCode**  <br>*optional*|string|
|**id**  <br>*optional*|string|
|**lenStage**  <br>*optional*|string|
|**lenStageList**  <br>*optional*|< string > array|
|**noArea**  <br>*optional*|integer (int32)|
|**noJunc**  <br>*optional*|integer (int32)|
|**noOffset**  <br>*optional*|integer (int32)|
|**noPlan**  <br>*optional*|integer (int32)|
|**noStage**  <br>*optional*|string|
|**signalId**  <br>*optional*|string|
|**stage**  <br>*optional*|string|
|**stageList**  <br>*optional*|< string > array|
|**stageNames**  <br>*optional*|< string > array|

<a name="planparam"></a>

### PlanParam

|Name|Schema|
|---|---|
|**coordPhaseNo**  <br>*optional*|string|
|**crossID**  <br>*optional*|string|
|**cycleLen**  <br>*optional*|string|
|**offSet**  <br>*optional*|string|
|**planNo**  <br>*optional*|string|
|**stageNoList**  <br>*optional*|[StageNoList](#stagenolist)|

<a name="prioritycyclerequest"></a>

### PriorityCycleRequest

|Name|Schema|
|---|---|
|**crossID**  <br>*optional*|string|
|**mode**  <br>*optional*|string|
|**sequenceNo**  <br>*optional*|string|
|**stages**  <br>*optional*|< [Stage](#stage) > array|

<a name="prioritystagerequest"></a>

### PriorityStageRequest

|Name|Schema|
|---|---|
|**crossID**  <br>*optional*|string|
|**lenStage**  <br>*optional*|integer (int32)|
|**mode**  <br>*optional*|string|
|**opType**  <br>*optional*|integer (int32)|
|**sequenceNo**  <br>*optional*|string|
|**stageNo**  <br>*optional*|string|

<a name="schedule"></a>

### Schedule

|Name|Schema|
|---|---|
|**crossControlMode**  <br>*optional*|string|
|**planNo**  <br>*optional*|string|
|**startTime**  <br>*optional*|string|

<a name="schedulelist"></a>

### ScheduleList

|Name|Schema|
|---|---|
|**scheduleList**  <br>*optional*|< [Schedule](#schedule) > array|

<a name="segment"></a>

### Segment

|Name|Schema|
|---|---|
|**id**  <br>*optional*|string|
|**iden**  <br>*optional*|boolean|
|**mode**  <br>*optional*|string|
|**name**  <br>*optional*|string|
|**noOffset**  <br>*optional*|integer (int32)|
|**noPlan**  <br>*optional*|integer (int32)|
|**signalId**  <br>*optional*|string|
|**time**  <br>*optional*|integer (int32)|
|**type**  <br>*optional*|string|

<a name="signalbase"></a>

### SignalBase

绿波信号机参数

|Name|Description|Schema|
|---|---|---|
|**latitude**  <br>*optional*|信号机纬度|number (double)|
|**longitude**  <br>*optional*|信号机经度|number (double)|
|**name**  <br>*optional*|信号机名称|string|
|**signalId**  <br>*optional*|信号机编号|string|

<a name="stage"></a>

### Stage

|Name|Schema|
|---|---|
|**lenStage**  <br>*optional*|integer (int32)|
|**stageNo**  <br>*optional*|string|

<a name="stageconfig"></a>

### StageConfig

|Name|Schema|
|---|---|
|**id**  <br>*optional*|string|
|**signalId**  <br>*optional*|string|
|**stageName**  <br>*optional*|string|
|**stageNo**  <br>*optional*|string|

<a name="stagelamp"></a>

### StageLamp

|Name|Schema|
|---|---|
|**lamps**  <br>*optional*|< integer (int32) > array|

<a name="stagenolist"></a>

### StageNoList

|Name|Schema|
|---|---|
|**stageNo**  <br>*optional*|< string > array|

<a name="stageparam"></a>

### StageParam

|Name|Schema|
|---|---|
|**allRed**  <br>*optional*|string|
|**attribute**  <br>*optional*|string|
|**crossID**  <br>*optional*|string|
|**green**  <br>*optional*|string|
|**phaseNoList**  <br>*optional*|[PhaseNoList](#phasenolist)|
|**redYellow**  <br>*optional*|string|
|**stageName**  <br>*optional*|string|
|**stageNo**  <br>*optional*|string|
|**yellow**  <br>*optional*|string|

<a name="tmplight"></a>

### TmpLight

|Name|Schema|
|---|---|
|**lenClearRed**  <br>*optional*|integer (int32)|
|**lenPGreenFlesh**  <br>*optional*|integer (int32)|
|**lenPRedFlesh**  <br>*optional*|integer (int32)|
|**lenVGreenFlesh**  <br>*optional*|integer (int32)|
|**lenVRedFlesh**  <br>*optional*|integer (int32)|
|**lenYellow**  <br>*optional*|integer (int32)|
|**stageNo**  <br>*optional*|integer (int32)|

<a name="wave"></a>

### Wave

绿波详细数据参数

|Name|Description|Schema|
|---|---|---|
|**id**  <br>*optional*|绿波编号|string|
|**keySignalId**  <br>*optional*|关键路口信号机id|string|
|**name**  <br>*optional*|绿波名称|string|
|**period**  <br>*optional*|绿波周期时长|integer (int32)|
|**plans**  <br>*optional*|方案列表|< string, < [绿波方案数据](#624d65bb69b71a419763e9b4be01aa33) > array > map|
|**segments**  <br>*optional*|时段列表|< string, < [WaveSegment](#wavesegment) > array > map|
|**signalList**  <br>*optional*|信号机数据项列表|< [SignalBase](#signalbase) > array|
|**status**  <br>*optional*|绿波状态，0 未执行1 执行中|integer (int32)|
|**type**  <br>*optional*|绿波类型,0-7 一般，星期一、二、...日|string|

<a name="wavebean"></a>

### WaveBean

绿波基础参数

|Name|Description|Schema|
|---|---|---|
|**id**  <br>*optional*|绿波id|string|
|**name**  <br>*optional*|绿波名称|string|
|**signalIds**  <br>*optional*|绿波信号机列表|< string > array|

<a name="wavesegment"></a>

### WaveSegment

绿波时段数据

|Name|Description|Schema|
|---|---|---|
|**id**  <br>*optional*|时段编号|string|
|**mode**  <br>*optional*|时段控制方式|integer (int32)|
|**noPlan**  <br>*optional*|时段使用方案号|integer (int32)|
|**signalId**  <br>*optional*|时段信号机编号|string|
|**time**  <br>*optional*|时段开始时间|integer (int32)|
|**waveId**  <br>*optional*|时段关联绿波Id|string|

<a name="624d65bb69b71a419763e9b4be01aa33"></a>

### 绿波方案数据

|Name|Description|Schema|
|---|---|---|
|**id**  <br>*optional*|方案编号|string|
|**lenStageList**  <br>*optional*||< string > array|
|**noOffset**  <br>*optional*|相位差|integer (int32)|
|**noPlan**  <br>*optional*|方案ID，0-19|integer (int32)|
|**noStage**  <br>*optional*|协调相位|string|
|**signalId**  <br>*optional*|方案列表|string|
|**stageList**  <br>*optional*||< string > array|
|**stageNames**  <br>*optional*||< string > array|
|**waveId**  <br>*optional*|绿波编号|string|



