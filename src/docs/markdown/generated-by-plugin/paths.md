<a name="paths"></a>

## Paths

<a name="modestagepostusingpost"></a>

### 指定相位

```
POST /controlSignal/modeStage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**chara**  <br>*required*|chara|integer (int32)|
|**Query**|**mode**  <br>*required*|mode|integer (int32)|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Query**|**stageLen**  <br>*required*|stageLen|integer (int32)|
|**Query**|**stageNo**  <br>*required*|stageNo|integer (int32)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* control-signal-controller

<a name="modestageusingget"></a>

### modeStage

```
GET /controlSignal/modeStage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**chara**  <br>*required*|chara|integer (int32)|
|**Query**|**mode**  <br>*required*|mode|integer (int32)|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Query**|**stageLen**  <br>*required*|stageLen|integer (int32)|
|**Query**|**stageNo**  <br>*required*|stageNo|integer (int32)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* control-signal-controller

<a name="addusingpost"></a>

### 设置信号机标准相位配置

```
POST /defaultStageConfig/{brandId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**brandId**  <br>*required*|brandId|string|
|**Body**|**defaultStageConfigs**  <br>*required*|defaultStageConfigs|< [DefaultStageConfig](#defaultstageconfig) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* default-stage-config-controller

<a name="getusingget"></a>

### 查询信号机标准相位配置

```
GET /defaultStageConfig/{brandId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**brandId**  <br>*required*|brandId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* default-stage-config-controller

<a name="loaddatausingget"></a>

### 调看系统参数以及路口参数

```
GET /ht/loadData/{type}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**type**  <br>*required*|type|enum (LS, HT, LP)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="loaddatausingget_1"></a>

### 路口参数

```
GET /ht/loadSignalData/{lessignalControlerID}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**lessignalControlerID**  <br>*required*|lessignalControlerID|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="loadsignaldatausingget"></a>

### 路口参数

```
GET /ht/loadSignalData/{lessignalControlerID}/{type}/{no}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**lessignalControlerID**  <br>*required*|lessignalControlerID|string|
|**Path**|**no**  <br>*required*|no|string|
|**Path**|**type**  <br>*required*|type|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="modestageusingpost"></a>

### 指定相位

```
POST /ht/modeStage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**lock**  <br>*required*|lock|boolean|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Query**|**stageLen**  <br>*required*|stageLen|integer (int32)|
|**Query**|**stageNo**  <br>*required*|stageNo|integer (int32)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="plansetusingpost"></a>

### 方案参数设置

```
POST /ht/plan
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**crossSchedule**  <br>*required*|crossSchedule|[PlanParam](#planparam)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="prioritycyclerequestusingpost"></a>

### 公交优先周期优化

```
POST /ht/priorityCycle
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**priorityCycleRequest**  <br>*required*|priorityCycleRequest|[PriorityCycleRequest](#prioritycyclerequest)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="prioritystagerequestusingpost"></a>

### 公交优先阶段优化

```
POST /ht/priorityStage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**priorityStageRequest**  <br>*required*|priorityStageRequest|[PriorityStageRequest](#prioritystagerequest)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="schedulesetusingpost"></a>

### 调度参数设置

```
POST /ht/schedule
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**crossSchedule**  <br>*required*|crossSchedule|[CrossSchedule](#crossschedule)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="stagesetusingpost"></a>

### 阶段参数设置

```
POST /ht/stage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**stageParams**  <br>*required*|stageParams|< [StageParam](#stageparam) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="signalstatususingget"></a>

### 路口状态

```
GET /ht/status/{lesSignalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**lesSignalId**  <br>*required*|lesSignalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* ht-signal-controller

<a name="loadsignalsegmentusingpost"></a>

### loadSignalSegment

```
POST /load/segment/{signalId}/{type}/{iden}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**iden**  <br>*required*|iden|boolean|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Path**|**type**  <br>*required*|type|string|
|**Body**|**segments**  <br>*required*|segments|< [Segment](#segment) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-controller

<a name="looklimitgreenusingpost"></a>

### lookLimitGreen

```
POST /loadSignal/limitGreen/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Body**|**greenLimitMap**  <br>*required*|greenLimitMap|< string, [GreenLimit](#greenlimit) > map|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-signal-controller

<a name="loadplanusingpost"></a>

### loadPlan

```
POST /loadSignal/plans/{signalId}/{noPlan}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**noPlan**  <br>*required*|noPlan|integer (int32)|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Body**|**plan**  <br>*required*|plan|[Plan](#plan)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-signal-controller

<a name="loadsignalsegmentusingpost_1"></a>

### loadSignalSegment

```
POST /loadSignal/segment/{signalId}/{type}/{iden}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**iden**  <br>*required*|iden|boolean|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Path**|**type**  <br>*required*|type|string|
|**Body**|**segments**  <br>*required*|segments|< [Segment](#segment) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-signal-controller

<a name="stagelampusingpost"></a>

### stageLamp

```
POST /loadSignal/stageLamp/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Body**|**stageLampMap**  <br>*required*|stageLampMap|< string, [StageLamp](#stagelamp) > map|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-signal-controller

<a name="looktmplightusingpost"></a>

### lookTmpLight

```
POST /loadSignal/tmpLight/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Body**|**tmpLights**  <br>*required*|tmpLights|< string, [TmpLight](#tmplight) > map|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* load-signal-controller

<a name="loaddatausingget_2"></a>

### 路口参数

```
GET /loadSignalData/{lessignalControlerID}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**lessignalControlerID**  <br>*required*|lessignalControlerID|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* reload-signal-controller

<a name="lampstatususingget"></a>

### lampStatus

```
GET /look/lampStatus/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="looklimitgreenusingget"></a>

### lookLimitGreen

```
GET /look/limitGreen/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="lookplanusingget_1"></a>

### lookPlan

```
GET /look/plans/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="lookplanusingget"></a>

### lookPlan

```
GET /look/plans/{signalId}/{noPlan}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**noPlan**  <br>*required*|noPlan|integer (int32)|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="dayusingget"></a>

### day

```
GET /look/segment/{signalId}/day
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="looksegmentusingget"></a>

### lookSegment

```
GET /look/segment/{signalId}/{type}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Path**|**type**  <br>*required*|type|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="deleteusingdelete"></a>

### delete

```
DELETE /look/segment/{signalId}/{type}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Path**|**type**  <br>*required*|type|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**204**|No Content|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="stagelampusingget"></a>

### stageLamp

```
GET /look/stageLamp/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="looktmplightusingget"></a>

### lookTmpLight

```
GET /look/tmpLight/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-controller

<a name="looklimitgreenusingget_1"></a>

### lookLimitGreen

```
GET /lookSignal/limitGreen/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="lookplanusingget_3"></a>

### lookPlan

```
GET /lookSignal/plans/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="lookplanusingget_2"></a>

### lookPlan

```
GET /lookSignal/plans/{signalId}/{noPlan}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**noPlan**  <br>*required*|noPlan|integer (int32)|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="looksegmentusingget_1"></a>

### lookSegment

```
GET /lookSignal/segment/{signalId}/{type}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Path**|**type**  <br>*required*|type|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="stagelampusingget_1"></a>

### stageLamp

```
GET /lookSignal/stageLamp/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="looktimeusingget"></a>

### lookTime

```
GET /lookSignal/time/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="looktmplightusingget_1"></a>

### lookTmpLight

```
GET /lookSignal/tmpLight/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* look-signal-controller

<a name="addusingpost_1"></a>

### 添加信号机

```
POST /signal/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* signal-controller

<a name="updateusingput"></a>

### 修改信号机

```
PUT /signal/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* signal-controller

<a name="delusingdelete"></a>

### 删除信号机

```
DELETE /signal/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**204**|No Content|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|

#### Produces

* `*/*`

#### Tags

* signal-controller

<a name="signalstatususingpost"></a>

### 查看指定信号机状态

```
POST /signalStatus
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Body**|**signalIds**  <br>*required*|signalIds|< string > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* signal-status-controller

<a name="allsignalstatususingget"></a>

### 查看所有信号机状态

```
GET /signalStatus
```

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* signal-status-controller

<a name="signalstatususingget_1"></a>

### 查看指定信号机状态

```
GET /signalStatus/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* signal-status-controller

<a name="addusingpost_2"></a>

### 设置信号机相位配置

```
POST /stageConfig/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|
|**Body**|**stageConfigs**  <br>*required*|stageConfigs|< [StageConfig](#stageconfig) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* stage-config-controller

<a name="getusingget_1"></a>

### 查询信号机相位配置

```
GET /stageConfig/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* stage-config-controller

<a name="getdatausingget"></a>

### getData

```
GET /utc/data/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="getpatternusingget"></a>

### getPattern

```
GET /utc/patterns/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="getphasesusingget"></a>

### getPhases

```
GET /utc/phases/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="prioritycyclerequestusingpost_1"></a>

### 公交优先周期优化

```
POST /utc/priorityCycle
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**priorityCycleRequest**  <br>*required*|priorityCycleRequest|[PriorityCycleRequest](#prioritycyclerequest)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="prioritystagerequestusingpost_2"></a>

### 公交优先阶段优化

```
POST /utc/priorityStage
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**priorityStageRequest**  <br>*required*|priorityStageRequest|[PriorityStageRequest](#prioritystagerequest)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="prioritystagerequestusingpost_1"></a>

### 公交优先阶段优化

```
POST /utc/priorityStageMulti
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**signalId**  <br>*required*|signalId|string|
|**Body**|**priorityStageRequests**  <br>*required*|priorityStageRequests|< [PriorityStageRequest](#prioritystagerequest) > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="getscheduleusingget"></a>

### getSchedule

```
GET /utc/schedules/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="getstagesusingget"></a>

### getStages

```
GET /utc/stages/{signalId}
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Path**|**signalId**  <br>*required*|signalId|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult«object»](#3625c36a8ea9cf7285b4f7df3f9f33df)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* utc-controller

<a name="addwaveusingpost"></a>

### 新建绿波基础数据项

```
POST /wave
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Body**|**waveBean**  <br>*required*|waveBean|[WaveBean](#wavebean)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* wave-controller

<a name="getwaveusingget"></a>

### 获取所有绿波基础数据项

```
GET /wave
```

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* wave-controller

<a name="delwaveusingdelete"></a>

### 删除绿波数据项

```
DELETE /wave
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**waveId**  <br>*required*|绿波编号|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**204**|No Content|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|

#### Produces

* `*/*`

#### Tags

* wave-controller

<a name="saveusingpost"></a>

### 保存绿波方案

```
POST /waveInfo
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Body**|**wave**  <br>*required*|wave|[Wave](#wave)|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* wave-detail-controller

<a name="getoneusingget"></a>

### 读取绿波方案数据项

```
GET /waveInfo
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**waveId**  <br>*required*|绿波编号|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Produces

* `*/*`

#### Tags

* wave-detail-controller

<a name="copywaveusingpost"></a>

### 复制绿波数据项

```
POST /waveInfo/copy
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**waveId**  <br>*required*|绿波编号|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* wave-detail-controller

<a name="load2signalusingpost"></a>

### 根据传递的绿波id以及信号机id列表

```
POST /waveInfo/loadSignal
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**waveId**  <br>*required*|绿波编号|string|
|**Body**|**signals**  <br>*required*|signals|< string > array|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* wave-detail-controller

<a name="synckeywaveusingpost"></a>

### 从信号机一般时段同步数据,同步所有的方案,以关键路口的为时段数据项

```
POST /waveInfo/syncKey
```

#### Parameters

|Type|Name|Description|Schema|
|---|---|---|---|
|**Query**|**keySignalId**  <br>*required*|keySignalId|string|
|**Query**|**waveId**  <br>*required*|绿波编号|string|

#### Responses

|HTTP Code|Description|Schema|
|---|---|---|
|**200**|OK|[JsonResult](#jsonresult)|
|**201**|Created|No Content|
|**401**|Unauthorized|No Content|
|**403**|Forbidden|No Content|
|**404**|Not Found|No Content|

#### Consumes

* `application/json`

#### Produces

* `*/*`

#### Tags

* wave-detail-controller



