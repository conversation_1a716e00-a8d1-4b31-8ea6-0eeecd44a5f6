package com.myweb.daa.areasignal.htbussiness.service;

import com.les.ads.ds.enums.LaneMovementType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam;
import com.myweb.daa.areasignal.protocol.p1049.util.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class Ht1049SignalCacheServiceTest {


    @Test
    public void testSort3() {
        List<Integer> data = new ArrayList<>();

        String dataStr = "230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 10, 11, 12, 13, 14, 15, 16, 17, 18, 1, 2, 200, 3, 201, 4, 202, 5, 203, 6, 204, 7, 205, 8, 206, 9, 207, 208, 209, 20, 22, 23, 24, 26, 27, 28, 29, 210, 211, 212, 213, 214, 215, 216, 218, 219, 30, 32, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229";

        String[] split = dataStr.split(",");
        if (split != null && split.length > 0) {
            for (int i = 0; i < split.length; i++) {
                data.add(Integer.valueOf(split[i].trim()));
            }
        }

        log.error("排序前数据项-{}", data);

        data.sort(
                Comparator.comparingInt(Integer::intValue)
        );

        log.error("排序后数据项-{}", data);
    }




    @Test
    public void testSort2() {
        List<Integer> data = new ArrayList<>();
        IntStream.rangeClosed(1, 19).forEach(
                index -> {
                    data.add(100 - index);
                }
        );

        log.error("排序前数据项-{}", data);

        data.sort(
                Comparator.comparingInt(Integer::intValue)
        );

        log.error("排序后数据项-{}", data);
    }


    @Test
    public void testXml() {
        String dataSt = "<PlanParam>    <CrossID>32010000300030</CrossID>    <PlanNo>3</PlanNo>    <CycleLen>138</CycleLen>    <CoordPhaseNo>301</CoordPhaseNo>    <OffSet>64</OffSet>    <StageNoList>     <StageNo>301</StageNo>     <StageNo>302</StageNo>     <StageNo>303</StageNo>     <StageNo>304</StageNo>    </StageNoList>   </PlanParam>";
        Optional<PlanParam> baseMessage1049 = XmlUtils.xml2Object(dataSt, PlanParam.class);
        log.debug("生成数据项-{}", baseMessage1049);
    }

    @Test
    public void printLaneMovementType() {
        log.debug(Arrays.stream(LaneMovementType.values()).sorted().map(laneMovementType -> laneMovementType.getDescription()).collect(Collectors.joining(", ")));
    }


    @Test
    public void testSort() {
        ArrayList<StageParam> stageParams = new ArrayList<>();

        IntStream.range(1, 2).forEach(
                index -> stageParams.add(StageParam.builder().StageNo(String.valueOf(index)).build())
        );

        stageParams.sort(
                Comparator.comparingInt(stageParam -> Integer.parseInt(stageParam.getStageNo()))
        );

        log.debug("stageParams={}", stageParams);

    }

    @Test
    public void change1049CrossToLesSignalId() {
        List<String> crossId1049s = new ArrayList();
        crossId1049s.add("12345600000147");
        crossId1049s.add("12345600100213");
        crossId1049s.add("12345600100297");

        crossId1049s.stream().forEach(
                crossId1049 -> {
                    genTest(crossId1049);
                }
        );
    }

    private static void genTest(String crossId1049) {
        //非标准1049长度的路口数据项
        if (crossId1049.length() != 14) {
            return;
        }
        //总区域个数
        int regionNumber = 2;
        //初始的区域编号
        int beginJuncNo = 24;
        //获取区域编号
        int regionId = Integer.parseInt(crossId1049.substring(6, 9));
        int juncNo = Integer.parseInt(crossId1049.substring(9));

        //初始化区域编号 + 原始区域编号 + 路口偏移区域数
        int lesAreaNo = beginJuncNo + regionId + (regionNumber) * (juncNo / 250);
        int lesJuncNo = juncNo % 250;

        //前9为区域编号,后5位路口编号
        String lesSignalId = String.format("320100YL%02d%03d", lesAreaNo, lesJuncNo);
        ;
        log.error("{}生成的莱斯信号机ID是{}", crossId1049, lesSignalId);
    }
}