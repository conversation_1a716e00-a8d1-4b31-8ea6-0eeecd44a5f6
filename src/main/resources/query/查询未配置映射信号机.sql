select *
from `micro-1049`.cross_id_map cim2
where cim2.les_cross_id not in (select crossid
                                from (select concat(sim.les_signal_id, "1") as crossid, sim.signal_id1049
                                      from `micro-1049`.signal_id_map sim) as db1
                                         left join
                                     `micro-1049`.cross_id_map cim on db1.crossid = cim.les_cross_id)
 
 
 
 