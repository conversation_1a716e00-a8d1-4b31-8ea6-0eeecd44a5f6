
content_type   text/plain
content_encoding    UTF-8
delivery_mode  2

{
  "noTab" : 49,
  "noStage" : [56,57,58,59],
  "lenStage" : [100,200,300,400],
  "noArea": 0,
  "noJunc": 10
}

{
  "noTab" : 50,
  "noStage" : 20,
  "lenStage" : 20,
  "noArea": 0,
  "noJunc": 10
}

{
  "noTab" : 49,
  "noStage" : [11,12],
  "lenStage" : [20,30],
  "noArea": 0,
  "noJunc": 19
}



{
"errorCode":"0",
"errorInfo":"",
"mapKey":1,
"objectId":"3006",
"objectList":
[
{"mode":2,"crossingSeqNo":1,"stageNoList":[9, 10, 11 ,12], "signalControllerID":"320100HT00019","greenList": [15, 15, 15, 15]}],
"operator":1,"sequenceCode":1,"signalControllerID":"320100HT00019","timeStamp":0,
"token":"d8e80669968049b8a3f4d690c5d3d869","type":1,"version":"1.0"}


{
"errorCode":"0",
"errorInfo":"",
"mapKey":1,
"objectId":"3001",
"objectList":
[
{"mode":20,"crossingSeqNo":1,, "signalControllerID":"320100HT00019","iden":5 }],
"operator":1,"sequenceCode":1,"signalControllerID":"320100HT00019","timeStamp":0,
"token":"d8e80669968049b8a3f4d690c5d3d869","type":1,"version":"1.0"}



##模拟发送路口参数书
127.0.0.1:16000/ht/simu/{crossId}/{type}


127.0.0.1:16000/ht/simu/320100LS29001/REQUEST


--- 一般命令

信号机id 320100000000929001
路口id 32010002900001

<Operation order="1" name="Get">
<TSCCmd>
<ObjName>ScheduleParam</ObjName>
<ID>32010002900001</ID>
<No>1</No>
</TSCCmd>
</Operation>

--- 控制命令

