spring:
  application:
    name: adatper1049
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8849
      config:
        server-addr: nacos:8849
        file-extension: yaml
        ext-config: #配置公共配置列表
          - dataId: default.yaml
            group: DEFAULT_GROUP
            refresh: true
    inetutils:
      ignored-interfaces:
        - Ethernet1
      preferredNetworks:
        - 172.29.232.195
