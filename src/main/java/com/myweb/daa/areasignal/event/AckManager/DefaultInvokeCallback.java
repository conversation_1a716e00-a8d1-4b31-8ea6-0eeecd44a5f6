package com.myweb.daa.areasignal.event.AckManager;

import java.util.concurrent.Executor;

/**
 * @ClassName: DefaultInvokeCallback
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:51
 */
public class DefaultInvokeCallback implements InvokeCallback {
    @Override
    public void onResponse(Object result) {

    }

    @Override
    public void onException(Throwable e) {

    }

    @Override
    public Executor getExecutor() {
        return null;
    }
}
