package com.myweb.daa.areasignal.event;

import com.myweb.daa.areasignal.utils.ConstValue;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName: AbstractSummary
 * @Description: 统计功能抽象类
 * @Author: king
 * @CreateDate: 2018/12/20 14:44
 */
@Slf4j
@Data
public abstract class AbstractSummary {
    public static final long FIX_CHECK_MESSAGE_TIME_INTERVAL_MS = 5000;

    @Getter
    private AtomicLong atomicSuccessLong = new AtomicLong(0);

    @Getter
    private AtomicLong atomicErrorLong = new AtomicLong(0);

    /**
     * 每秒正确处理报文个数
     */
    @Getter
    private double successMessageSecond = 0;
    private long lastSuccesslong = 0;

    /**
     * 每秒失败处理报文个数
     */
    @Getter
    private double failedMessageSecond = 0;
    private long lastFailedlong = 0;

    /**
     * 开始统计数据的时间
     */
    private long startTime = 0;

    public String getStartTime() {
        SimpleDateFormat timeFormat = new SimpleDateFormat(ConstValue.DATE_FORMAT);
        return timeFormat.format(new Date(startTime * 1000));
    }

    /**
     * 计算每秒处理报文参数
     */
    public void calculate() {
        successMessageSecond = ((double) atomicSuccessLong.get() - lastSuccesslong) / (FIX_CHECK_MESSAGE_TIME_INTERVAL_MS / 1000);
        failedMessageSecond = ((double) atomicErrorLong.get() - lastFailedlong) / (FIX_CHECK_MESSAGE_TIME_INTERVAL_MS / 1000);

        lastSuccesslong = atomicSuccessLong.get();
        lastFailedlong = atomicErrorLong.get();

        double tps = 0;
        if (0 == startTime) {
            startTime = System.currentTimeMillis() / 1000;
        } else {
            if ((System.currentTimeMillis() / 1000 - startTime) != 0) {
                tps = atomicSuccessLong.get() / (System.currentTimeMillis() / 1000 - startTime);
            }
        }

        /*
        log.debug("calculate message processRequest info success=[{}/s] error=[{}/s]  tps=[{}/s] startTime=[{}] in {}",
                successMessageSecond,
                failedMessageSecond,
                tps, getStartTime(), this.getClass().getName());
        */
    }


    /**
     * 重置处理正确以及处理异常的报文个数
     */
    public void reset() {
        atomicSuccessLong.set(0);
        atomicErrorLong.set(0);
        successMessageSecond = 0;
        failedMessageSecond = 0;
    }


    @Scheduled(initialDelay = FIX_CHECK_MESSAGE_TIME_INTERVAL_MS, fixedRate = FIX_CHECK_MESSAGE_TIME_INTERVAL_MS)
    public void summary() {
        calculate();
    }

}
