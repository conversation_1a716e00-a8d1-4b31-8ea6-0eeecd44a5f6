package com.myweb.daa.areasignal.event.AckManager;


import com.myweb.daa.areasignal.event.AckManager.response.ResponseMessage;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import io.netty.util.Timeout;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @ClassName: DefaultInvokeFuture
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:07
 */
@Data
@Slf4j
public class DefaultInvokeFuture implements InvokeFuture {

    private String invokeId;

    private InvokeCallbackListener callbackListener;

    private InvokeCallback callback;

    private volatile ResponseMessage responseMessage;

    private final CountDownLatch countDownLatch = new CountDownLatch(1);

    private final AtomicBoolean executeCallbackOnlyOnce = new AtomicBoolean(false);

    private Timeout timeout;

    private Throwable cause;

    private InterProtocol interProtocol;


    /**
     * Constructor.
     *
     * @param invokeId         invoke id
     * @param callbackListener callback listener
     * @param callback         callback
     */
    public DefaultInvokeFuture(String invokeId, InvokeCallbackListener callbackListener,
                               InvokeCallback callback, InterProtocol interProtocol) {
        this.invokeId = invokeId;
        this.callbackListener = callbackListener;
        this.callback = callback;
        this.interProtocol = interProtocol;
    }


    @Override
    public ResponseMessage waitResponse(long timeoutMillis) throws InterruptedException {
        this.countDownLatch.await(timeoutMillis, TimeUnit.MILLISECONDS);
        return this.responseMessage;
    }

    @Override
    public ResponseMessage waitResponse() throws InterruptedException {
        this.countDownLatch.await();
        return this.responseMessage;
    }

    /**
     *
     */
    @Override
    public void putResponse(ResponseMessage response) {
        this.responseMessage = response;
        this.countDownLatch.countDown();
    }

    @Override
    public ResponseMessage getResponse() {
        return this.responseMessage;
    }

    /**
     * @see
     */
    @Override
    public boolean isDone() {
        return this.countDownLatch.getCount() <= 0;
    }

    /**
     * @see
     */
    @Override
    public String invokeId() {
        return this.invokeId;
    }

    @Override
    public InterProtocol getRequest() {
        return this.interProtocol;
    }

    @Override
    public void executeInvokeCallback() {
        if (callbackListener != null) {
            if (this.executeCallbackOnlyOnce.compareAndSet(false, true)) {
                callbackListener.onResponse(this);
            }
        }
    }

    /**
     *
     */
    @Override
    public InvokeCallback getInvokeCallback() {
        return this.callback;
    }


    /**
     * @param timeout
     */
    @Override
    public void addTimeout(Timeout timeout) {
        this.timeout = timeout;
    }


    /**
     *
     */
    @Override
    public void cancelTimeout() {
        if (this.timeout != null) {
            this.timeout.cancel();
        }
    }


    @Override
    public void setCause(Throwable cause) {
        this.cause = cause;
    }

    @Override
    public Throwable getCause() {
        return this.cause;
    }
}
