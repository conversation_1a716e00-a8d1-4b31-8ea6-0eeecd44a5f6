package com.myweb.daa.areasignal.event;

import com.myweb.commons.utils.GUID;
import com.myweb.commons.utils.StringUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.response.CommandFactory;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.utils.TimerHolder;
import io.netty.util.Timeout;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: MyApplicationEventPublisher
 * @Description: 使用ApplicationEventPublisher进行事件的发布
 * @Author: king
 * @CreateDate: 2018/11/26 13:35
 */
@Component
@Slf4j
@Data
public class MessageOuterPublisher extends AbstractSummary {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageAckManager messageAckManager;


    /**
     * 向外部系统发送数据报文
     *
     * @param interProtocol
     */
    public Optional<InvokeFuture> sendMessageOuter(InterProtocol interProtocol) {

        int DEFAULT_TIMEOUT_MESSAGE = GlobalConfigure.timeOutSecond;
        int DEFAULT_MAX_TIMEOUT_MESSAGE = GlobalConfigure.timeOutMaxSecond;

        Optional<InvokeFuture> invokeFutureOptional = Optional.empty();
        /**统一设置uuid\时间戳*/
        interProtocol.setOuterUuid(GUID.generate());
        interProtocol.setOuterTimeStamp(System.currentTimeMillis());

        /**判定是否是需要应答类报文*/
        if (interProtocol.isNeedAck()) {
            if (interProtocol.getTimeOutSeconds() <= 0) {
                interProtocol.setTimeOutSeconds(DEFAULT_TIMEOUT_MESSAGE);
            } else if (interProtocol.getTimeOutSeconds() > 30) {
                log.warn("{} 设置的超时时间太长了，设置为默认的最大值{}", interProtocol, DEFAULT_MAX_TIMEOUT_MESSAGE);
                interProtocol.setTimeOutSeconds(DEFAULT_MAX_TIMEOUT_MESSAGE);
            }

            if (StringUtils.isBlank(interProtocol.getAckKey())) {
                log.warn("请注意！报文-{}需要应答，但是没有设置应答关键字,应答可能会出现异常", interProtocol);
            }

            {
                final InvokeFuture invokeFuture = messageAckManager.addInterProtocol(interProtocol);
                invokeFutureOptional = Optional.of(invokeFuture);
                try {
                    /**新建超时设置*/
                    Timeout timer = TimerHolder.getTimer().newTimeout(
                            timeout -> {
                                invokeFuture.putResponse(CommandFactory.createMessageResponse(ResponseStatus.TIMEOUT, invokeFuture.getRequest()));
                                messagePublisher.publishMessage(invokeFuture);
                            }, interProtocol.getTimeOutSeconds(), TimeUnit.SECONDS);
                    invokeFuture.addTimeout(timer);
                } catch (Exception e) {
                    /**出现异常，取消超时设置*/
                    invokeFuture.cancelTimeout();
                    invokeFuture.putResponse(CommandFactory.createMessageResponse(ResponseStatus.SERVER_EXCEPTION, invokeFuture.getRequest()));
                    messagePublisher.publishMessage(invokeFuture);
                    log.error("Exception caught when sending invocation. The message is {}", interProtocol, e);
                }
            }
        }

        /**发送报文到数据总线上*/
        messagePublisher.publishMessage(interProtocol);
        return invokeFutureOptional;
    }
}
