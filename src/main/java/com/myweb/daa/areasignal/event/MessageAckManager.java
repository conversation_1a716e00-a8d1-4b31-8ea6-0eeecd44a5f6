package com.myweb.daa.areasignal.event;

import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.AckManager.AckAble;
import com.myweb.daa.areasignal.event.AckManager.DefaultInvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.response.CommandFactory;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseMessage;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: MessageAckManager
 * @Description: 需要应答的报文处理类
 * @Author: king
 * @CreateDate: 2019/6/17 13:26
 */
@Component
@Slf4j
public class MessageAckManager {

    /**
     * 需要应答类报文管理器
     */
    private final ConcurrentHashMap<String, InvokeFuture> needAckMessageMap = new ConcurrentHashMap<>(4);

    @Autowired
    private MessagePublisher messagePublisher;

    @EventListener
    @Async(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR)
    public void responseMessage(ResponseMessage responseMessage) {
        log.trace("收到了应答-{}", responseMessage);
        /**判定是否是需要应答的报文的应答*/
        InvokeFuture invokeFuture = needAckMessageMap.get(responseMessage.getRequestUuid());
        if (invokeFuture != null) {
            /**异常的数据发送状态*/
            if (responseMessage.getResponseStatus() != ResponseStatus.SUCCESS_SEND) {
                invokeFuture.cancelTimeout();
                invokeFuture.putResponse(CommandFactory.createMessageResponse(responseMessage.getResponseStatus(), invokeFuture.getRequest()));
                messagePublisher.publishMessage(invokeFuture);
            }
        }
    }

    /**
     * 通知处理完毕
     *
     * @param invokeFuture
     */
    @EventListener
    @Async(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR)
    public void messageInvokeResult(InvokeFuture invokeFuture) {
        log.debug("收到应答报文响应数据项-{}", invokeFuture);

        /**从内存中删除数据项*/
        rmInterProtocol(invokeFuture.invokeId());

        log.debug("当前等待应答数据项个数是-{}", needAckMessageMap.size());
    }


    /**
     * 收到了应答
     *
     * @param ackAble
     */
    @EventListener
    @Async(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR)
    public void recvAckBack(AckAble ackAble) {
        String ackBackKey = ackAble.getAckBackKey();

        //处理其他非应答类型
        if (ackAble instanceof BaseMessage1049) {
            BaseMessage1049 baseMessage1049 = (BaseMessage1049) ackAble;
            if ((!baseMessage1049.getType().equals(MessageType.RESPONSE.name()))
                    && (!baseMessage1049.getType().equals(MessageType.ERROR.name()))) {
                return;
            }
        }

        /**根据ack关键字查找原先报文数据项*/
        Optional<InvokeFuture> optionalInvokeFuture = needAckMessageMap.values().stream().filter(
                invokeFuture -> invokeFuture.getRequest().getAckKey().equalsIgnoreCase(ackBackKey)
        ).reduce((InvokeFuture invokeFuture1, InvokeFuture invokeFuture2) ->
                invokeFuture1.getRequest().getOuterTimeStamp() < invokeFuture1.getRequest().getOuterTimeStamp()
                        ? invokeFuture1 : invokeFuture2);

        if (optionalInvokeFuture.isPresent()) {
            ResponseMessage messageResponse = CommandFactory.createMessageResponse(ResponseStatus.SUCCESS, optionalInvokeFuture.get().getRequest());
            messageResponse.setResponseObject(ackAble);
            optionalInvokeFuture.get().putResponse(messageResponse);
            optionalInvokeFuture.get().cancelTimeout();
            messagePublisher.publishMessage(optionalInvokeFuture.get());
        } else {
            log.warn("没有找到原始报文,但是收到了关键字key-{}的应答报文", ackBackKey, ackAble);
        }
    }

    /**
     * @param interProtocol
     * @return
     */
    protected InvokeFuture createInvokeFuture(InterProtocol interProtocol) {
        return new DefaultInvokeFuture(interProtocol.getOuterUuid(), null, null, interProtocol);
    }

    /**
     * 添加等待应答的报文数据
     *
     * @param interProtocol
     * @return
     */
    public InvokeFuture addInterProtocol(InterProtocol interProtocol) {
        InvokeFuture invokeFuture = createInvokeFuture(interProtocol);
        this.needAckMessageMap.putIfAbsent(interProtocol.getOuterUuid(), invokeFuture);
        return invokeFuture;
    }

    /**
     * 删除等待应答的报文数据
     *
     * @param uuid
     * @return
     */
    public InvokeFuture rmInterProtocol(String uuid) {
        return this.needAckMessageMap.remove(uuid);
    }

    /**
     * 获取等待应答的报文数据
     *
     * @param uuid
     * @return
     */
    public InvokeFuture getInterProtocol(String uuid) {
        return this.needAckMessageMap.get(uuid);
    }
}
