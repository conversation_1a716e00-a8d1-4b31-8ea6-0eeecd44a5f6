package com.myweb.daa.areasignal.event.AckManager;

import com.myweb.daa.areasignal.event.AckManager.response.ResponseMessage;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import io.netty.util.Timeout;

/**
 * @ClassName: InvokeFuture
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:04
 */
public interface InvokeFuture {

    /**
     * Wait response with timeout.
     *
     * @param timeoutMillis time out in millisecond
     * @return remoting command
     * @throws InterruptedException if interrupted
     */
    ResponseMessage waitResponse(final long timeoutMillis) throws InterruptedException;

    /**
     * Wait response with unlimit timeout
     *
     * @return remoting command
     * @throws InterruptedException if interrupted
     */
    ResponseMessage waitResponse() throws InterruptedException;


    /**
     * Put the response to the future.
     *
     * @param response remoting command
     */
    void putResponse(final ResponseMessage response);

    /**
     * 获取应答数据项
     */
    ResponseMessage getResponse();

    /**
     * Get the id of the invocation.
     *
     * @return invoke id
     */
    String invokeId();

    /**
     * @return
     */
    InterProtocol getRequest();

    /**
     * Execute the callback.
     */
    void executeInvokeCallback();


    /**
     * Set the cause if exception caught.
     */
    void setCause(Throwable cause);

    /**
     * Get the cause of exception of the future.
     *
     * @return the cause
     */
    Throwable getCause();

    /**
     * Get the application callback of the future.
     *
     * @return get invoke callback
     */
    InvokeCallback getInvokeCallback();

    /**
     * Add timeout for the future.
     */
    void addTimeout(Timeout timeout);

    /**
     * Cancel the timeout.
     */
    void cancelTimeout();

    /**
     * Whether the future is done.
     *
     * @return true if the future is done
     */
    boolean isDone();


}
