package com.myweb.daa.areasignal.event;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.cloud.stream.messaging.Sink;
import org.springframework.context.annotation.Profile;

@EnableBinding({Sink.class})
@Slf4j
@Profile("test")
public class OpsMessageProcess {

    @StreamListener(Sink.INPUT)
    private void receiver(JSONObject message) {
        log.info("ops-topic:statusChangeInput:" + message.toJSONString());
    }
}
