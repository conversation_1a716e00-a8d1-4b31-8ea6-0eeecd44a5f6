package com.myweb.daa.areasignal.event.AckManager.response;

/**
 * @ClassName: ResponseStatus
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:24
 */

/**
 * Status of the response.
 *
 * <AUTHOR>
 * @version $Id: ResponseStatus.java, v 0.1 2015-9-28 PM3:08:12 tao Exp $
 */
public enum ResponseStatus {
    SUCCESS(0x0001, "报文应答成功"),
    SERVER_EXCEPTION(0x0002, "服务器处理出现异常"),
    ERROR_COMM(0x0003, "链路异常"),
    TIMEOUT(0x0004, "报文等待应答超时"),
    ERROR_PROTOCL_TYPE(0x0005, "没有指定允许的协议"),
    ERROR_ENCODE_MESSAGE(0x0006, "报文编码出现异常"),
    NOT_AVALIABLE_LINK(0x0007, "没有可用的链路"),
    SUCCESS_SEND(0x0008, "报文发送成功"),
    FAILED_SEND(0x0009, "报文发送失败");

    private int value;
    private String des;

    ResponseStatus(int value, String des) {
        this.value = value;
        this.des = des;
    }

    public int value() {
        return this.value;
    }

    public String des() {
        return this.des;
    }

}
