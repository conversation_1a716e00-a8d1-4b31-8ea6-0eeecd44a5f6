package com.myweb.daa.areasignal.event;

import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.protocol.common.message.AbstractProtocolMessage;
import com.myweb.daa.areasignal.protocol.common.utils.MessageProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: AsynMessageProcess
 * @Description: 总线数据处理，接收报文发送的数据
 * @Author: king
 * @CreateDate: 2018/12/3 19:25
 */
@Component
@Slf4j
public class AsynMessageProcess extends AbstractSummary {

    @Autowired
    private MessageProcessUtils messageProcessUtils;

    /**
     * 接收netty分包之后的数据包，进行报文的转换
     *
     * @param protocolMessage
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void consumeMessage(AbstractProtocolMessage protocolMessage) {

        boolean result = messageProcessUtils.processMessage(protocolMessage);

        if (result) {
            Long dataLong = getAtomicSuccessLong().incrementAndGet();
            log.debug("success decode message [" + dataLong + "] AbstractProtocolMessage " + Thread.currentThread().getName());
        } else {
            Long errorLong = getAtomicErrorLong().incrementAndGet();
            log.error("AbstractProtocolMessage decode error, already error/total decoded [{}/{}]", errorLong, errorLong + getAtomicSuccessLong().get());
        }
    }


}
