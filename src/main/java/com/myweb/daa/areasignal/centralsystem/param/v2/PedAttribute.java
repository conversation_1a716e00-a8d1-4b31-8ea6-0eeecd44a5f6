package com.myweb.daa.areasignal.centralsystem.param.v2;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description:
 * @date: 2024/7/21
 * @version: 1.0
 * @modifyHistory:
 */
public enum PedAttribute {

    /**
     * 一次过街
     */
    ONCE(1),

    /**
     * 二次过街-路口进口
     */
    TWICE_IN(21),

    /**
     * 二次过街-路口出口
     */
    TWICE_OUT(22);

    private final int code;

    PedAttribute(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
