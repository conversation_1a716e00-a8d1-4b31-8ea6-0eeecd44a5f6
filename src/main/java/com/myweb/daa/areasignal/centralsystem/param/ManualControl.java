package com.myweb.daa.areasignal.centralsystem.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/24 10:12
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManualControl {
    public static final String MqObjectId = "3005";

    private String signalControllerID;
    private int reserved;
    private int iden;
    private int crossingSeqNo;

    public String genMapKey() {
        return signalControllerID + "###" + crossingSeqNo;
    }
}
