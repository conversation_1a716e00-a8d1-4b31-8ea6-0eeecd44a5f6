package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.StageManualConfig;
import com.myweb.daa.areasignal.centralsystem.service.AcsCharaService;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class StageManualConfigHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Autowired
    private AcsCharaService acsCharaService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return StageManualConfig.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return StageManualConfig.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return StageManualConfig.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    StageManualConfig stageManualConfig = jsonObject.toJavaObject(StageManualConfig.class);

                    log.error("收到阶段人工设置命令{}", stageManualConfig);

                    //模拟发送数据，并非正在发送
                    TSCCmd tscCmd = TSCCmd.builder()
                            .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName())
                            .ID(crossingInfoOp.get().getCrossingId1049())
                            .No("")
                            .build();
                    List<TSCCmd> tscCmds = new ArrayList<>();
                    tscCmds.add(tscCmd);
                    P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                            .messageType(MessageType.REQUEST)
                            .operationName(OperationName.Get)
                            .object(tscCmds)
                            .signalBrandPort(signalBrandPort)
                            .address(crossingInfoOp.get().getAddress1049Ip())
                            .clazz(TSCCmd.class)
                            .simuSend(true).build();
                    requestMsgs.add(p1049CentralSystemMsg);

                }
        );
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送阶段手动设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }


    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<StageManualConfig> stageManualConfigs = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        StageManualConfig stageManualConfig = jsonObject.toJavaObject(StageManualConfig.class);
                        stageManualConfigs.add(stageManualConfig);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }


        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
