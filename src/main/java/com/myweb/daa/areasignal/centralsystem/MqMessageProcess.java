package com.myweb.daa.areasignal.centralsystem;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSaveBean;
import com.myweb.daa.areasignal.centralsystem.handler.MqMsgBaseHandler;
import com.myweb.daa.areasignal.centralsystem.mq.*;
import com.myweb.daa.areasignal.centralsystem.param.SgpTransAble;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.AcsCharaService;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:18
 */
@Service
@Slf4j
public class MqMessageProcess implements ApplicationContextAware {

    private ApplicationContext ac;

    /**
     * 存储默认数据请求处理对象
     * SignalBrandPort.ALL 类型下的 object
     */
    private final Map<String, MqMsgBaseHandler> mqMsgBaseHandlerMap = new ConcurrentHashMap<>();

    /**
     * 针对各个厂商的数据处理对象
     */
    private final Map<SignalBrandPort, Map<String, MqMsgBaseHandler>>
            signalBrandMqMsgBaseHandlerMap = new ConcurrentHashMap<>();

    /**
     * 用于记录当前正在处理的报文数据对象
     */
    private final  Map<Long, MqProcessingMsg> mqProcessingMsgMap = new ConcurrentHashMap<>();

    @Autowired
    public MessagePublisher messagePublisher;

    @Autowired
    public ControllerService controllerService;

    @Autowired
    public P1049Sender p1049Sender;

    @Autowired
    public MessageSender messageSender;

    @Autowired
    public AcsCharaService acsCharaService;

    @PostConstruct
    public void setDataProcessMap() {
        Map<String, MqMsgBaseHandler> beansOfType = ac.getBeansOfType(MqMsgBaseHandler.class);
        beansOfType.values().stream().filter(
                mqMsgBaseHandler -> mqMsgBaseHandler.getProcessBrand() == SignalBrandPort.ALL
        ).forEach(
                mqMsgBaseHandler -> {
                    mqMsgBaseHandlerMap.put(mqMsgBaseHandler.getObjectId(), mqMsgBaseHandler);
                }
        );

        Arrays.stream(SignalBrandPort.values()).forEach(
                signalBrandPort -> {
                    if(signalBrandPort == SignalBrandPort.ALL) {
                        return;
                    }

                    //针对各个厂商,默认使用ALL参数
                    Map<String, MqMsgBaseHandler> handlerMap
                            = signalBrandMqMsgBaseHandlerMap.get(signalBrandPort);
                    if(handlerMap == null) {
                        handlerMap = new ConcurrentHashMap<>();
                        signalBrandMqMsgBaseHandlerMap.put(signalBrandPort, handlerMap);

                        //初始化添加所有的处理对象
                        {
                            final Map<String, MqMsgBaseHandler> _handlerMap = handlerMap;
                            mqMsgBaseHandlerMap.keySet().forEach(
                                    key -> _handlerMap.put(key, mqMsgBaseHandlerMap.get(key)));

                        }
                    }

                    //覆盖配置项
                    {
                        final Map<String, MqMsgBaseHandler> _handlerMap = handlerMap;
                        beansOfType.values().stream().filter(
                                mqMsgBaseHandler -> mqMsgBaseHandler.getProcessBrand() == signalBrandPort
                        ).forEach(
                                mqMsgBaseHandler -> {
                                    _handlerMap.put(mqMsgBaseHandler.getObjectId(), mqMsgBaseHandler);
                                }
                        );
                    }
                }
        );

        log.error("厂商mq消息处理初始化完毕,包含-{}个", signalBrandMqMsgBaseHandlerMap.size());

    }

    @RabbitListener(queues = {"${global.mq.exchange.scatsCmdQueue}"})
    public void consumer(String cmd) {
        try {
            MqMessage cmdObject = JSONObject.parseObject(cmd, MqMessage.class);
            cmdObject.setTimeStamp(System.currentTimeMillis());
            log.debug("#####收到命令-{}", cmdObject);
            messagePublisher.publishMessage(cmdObject);
        } catch (Exception e) {
            log.error("解析MQ报文出现异常", e);
        }
    }

    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    @EventListener
    public void mqMessageProcess(MqMessage mqMessage) {

        log.error("#####收到MQ命令数据项-{}", mqMessage);

        if (mqMessage == null || mqMessage.getObjectId() == null) {
            log.error("异常的mq数据请求-{}", mqMessage);
            return;
        }

        if (mqMessage.getSignalControllerID() == null) {
            log.error("异常的mq数据请求,信号机编号异常-{}", mqMessage);
            return;
        }

        if (!controllerService.processMqMsg(mqMessage.getSignalControllerID())) {
            log.error("信号机编号不处理-{}", mqMessage.getSignalControllerID());
            return;
        }


        //查看信号机是否正常
        if (mqMessage.getSignalControllerID() == null) {
            log.error("异常的信号机编号-{}", mqMessage);
            return;
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(mqMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            log.error("没有找到信号机-{}", mqMessage);
            sendErrorResponse(mqMessage, "9001", "没有找到这个信号机");
            return;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(mqMessage.getSignalControllerID());
        Map<String, MqMsgBaseHandler> baseHandlerMap = signalBrandMqMsgBaseHandlerMap.get(signalBrandPort);
        if(baseHandlerMap == null) {
            log.error("没有找到信号机品牌handler-{}", mqMessage);
            sendErrorResponse(mqMessage, "9001", "没有找到这个信号机品牌handler");
            return;
        }

        MqMsgBaseHandler mqMsgBaseHandler = baseHandlerMap.get(mqMessage.getObjectId());
        if (mqMsgBaseHandler == null) {
            log.error("还不支持数据请求-{}", mqMessage.getObjectId());
            sendErrorResponse(mqMessage, "9000", "还不支持数据请求");
            return;
        }

        //获取请求类型
        Optional<MqMsgType> mqMsgType = MqMsgType.getType(mqMessage.getType());
        if (!mqMsgType.isPresent()) {
            log.error("未知的mq消息类型，无法处理-{}", mqMessage);
            sendErrorResponse(mqMessage, "9002", "消息类型异常");
            return;
        }
        //获取操作类型
        Optional<MqMsgOperator> mqMsgOperator = MqMsgOperator.getType(mqMessage.getOperator());
        if (!mqMsgOperator.isPresent()) {
            log.error("未知的mq操作类型，无法处理-{}", mqMessage);
            sendErrorResponse(mqMessage, "9003", "消息操作类型异常");
            return;
        }

        //请求-查询
        if (mqMsgType.get().value() == MqMsgType.MqMsgType_Request.value()
                && mqMsgOperator.get().value() == MqMsgOperator.MqMsgOperator_Query.value()) {
            List<Integer> integers = new ArrayList<>();
            try {
                List<Object> objectList = mqMessage.getObjectList();
                objectList.forEach(
                        objectId -> {
                            integers.add((Integer) objectId);
                        }
                );
            } catch (Exception e) {
                log.error("解析参数出现异常", e);
            }
            Optional<List<P1049CentralSystemMsg>> requestMsgsOp = mqMsgBaseHandler.getRequestMsg(mqMessage.getSignalControllerID(), integers);
            if (requestMsgsOp.isPresent()) {
                JsonResult<?> jsonResult = p1049Sender.sendMsgAsync(requestMsgsOp.get());
                if (jsonResult.isSuccess()) {
                    List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                    MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                            .mqMessage(mqMessage)
                            .invokeFutures(invokeFutures)
                            .mqMsgBaseHandler(mqMsgBaseHandler)
                            .localDateTime(LocalDateTime.now())
                            .processed(new AtomicBoolean(false)).build();
                    mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);
                } else {
                    log.error("query发送请求异常-{}", jsonResult);
                    sendErrorResponse(mqMessage, "9004", "query发送请求异常");
                }
            } else {
                log.error("query数据处理异常-{}", mqMessage);
                sendErrorResponse(mqMessage, "9004", "发送数据异常");
            }
        }
        //请求-设置
        else if (mqMsgType.get().value() == MqMsgType.MqMsgType_Request.value()
                && mqMsgOperator.get().value() == MqMsgOperator.MqMsgOperator_Set.value()) {

            //添加set数据项
            acsCharaService.addSetMqMessage(signalInfoOp.get(), mqMessage, mqMsgType.get(), mqMsgOperator.get());

            //查看是否需要立即应答
            Optional<MqMessage> mqAckMessageOptional = mqMsgBaseHandler.ackMqMessage(mqMessage, 1);
            if (mqAckMessageOptional.isPresent()) {
                messageSender.send(SystemControlAck.class.getSimpleName().toLowerCase(), mqAckMessageOptional.get());
            }

            Optional<List<P1049CentralSystemMsg>> requestMsgsOp = mqMsgBaseHandler.getConfigRequestMsg(mqMessage.getSignalControllerID(),
                    mqMessage.getObjectList());
            if (requestMsgsOp.isPresent()) {

                if (mqMsgBaseHandler.needSendConfigTogether(mqMessage.getSignalControllerID())) {
                    //攸亮数据项需要一起发送20230502
                    JsonResult<?> jsonResult = p1049Sender.sendMsgAsync(requestMsgsOp.get());
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .messageBaseList(requestMsgsOp.get()).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);
                    } else {
                        log.error("set-111-发送数据异常-{}", mqMessage);
                        sendErrorResponse(mqMessage, "9004", "set发送数据异常");
                        acsCharaService.rmSetMqMessage(mqMessage);
                    }
                } else {
                    //针对设置数据项，是有优先级进行
                    JsonResult<?> jsonResult = p1049Sender.sendMsgAsync(requestMsgsOp.get(), 0);
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .messageBaseList(requestMsgsOp.get()).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);
                    } else {
                        log.error("set发送数据异常-{}", mqMessage);
                        sendErrorResponse(mqMessage, "9004", "set发送数据异常");
                        acsCharaService.rmSetMqMessage(mqMessage);
                    }
                }
            } else {
                log.error("set数据处理异常-{}", mqMessage);
                sendErrorResponse(mqMessage, "9004", "发送数据异常");
                acsCharaService.rmSetMqMessage(mqMessage);
            }
        }
    }


    /**
     * 获取配置返回的原始数据项
     *
     * @param datas
     * @param clazz
     * @param signalId
     * @return
     */
    public Optional<List<Object>> getConfigRequestObject(List<Object> datas, Class clazz, String signalId) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }
        List<Object> objects = new ArrayList<>();
        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(signalId);
        try {
            datas.stream().forEach(
                    data -> {
                        JSONObject jsonObject = (JSONObject) data;
                        Object object = jsonObject.toJavaObject(clazz);
                        objects.add(object);

                        if (signalInfoOp.isPresent() && (object instanceof SgpTransAble)) {
                            //通过反射，设置区域号以及路口号
                            try {
                                Field noAreaField = object.getClass().getDeclaredField("noArea");
                                noAreaField.setAccessible(true);
                                noAreaField.set(object, signalInfoOp.get().getNoArea());
                                Field noJuncField = object.getClass().getDeclaredField("noJunc");
                                noJuncField.setAccessible(true);
                                noJuncField.set(object, signalInfoOp.get().getNoJunc());
                            } catch (NoSuchFieldException e) {
                                log.error("exception", e);
                            } catch (IllegalAccessException e) {
                                log.error("exception", e);
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("exception", e);
        }
        return Optional.of(objects);
    }


    /**
     * 通知处理完毕
     *
     * @param invokeFuture
     */
    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void messageInvokeResult(InvokeFuture invokeFuture) {
        Optional<MqProcessingMsg> mqProcessingMsgOp = mqProcessingMsgMap.values().stream().filter(
                mqProcessingMsg -> {
                    if (mqProcessingMsg.getInvokeFutures() == null
                            || mqProcessingMsg.getInvokeFutures().isEmpty()) {
                        return false;
                    }
                    return mqProcessingMsg.getInvokeFutures().stream().anyMatch(
                            invokeFuture1 -> invokeFuture1.invokeId().equals(invokeFuture.invokeId())
                    );
                }
        ).findAny();
        if (!mqProcessingMsgOp.isPresent()) {
            return;
        }
        if (!mqProcessingMsgOp.get().isReady()) {
            return;
        }

        //控制多个线程对数据同时处理
        boolean compareAndSet = mqProcessingMsgOp.get().getProcessed().compareAndSet(false, true);
        if (compareAndSet) {

            log.debug("准备解析数据项-{}", mqProcessingMsgOp.get().getMqMessage());

            MqProcessingMsg mqProcessingMsg = mqProcessingMsgOp.get();

            Optional<MqMessage> responseMsg = Optional.empty();
            //请求-查询
            if (mqProcessingMsg.getMqMessage().getType() == MqMsgType.MqMsgType_Request.value()
                    && mqProcessingMsg.getMqMessage().getOperator() == MqMsgOperator.MqMsgOperator_Query.value()) {
                responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getResponseMsg(mqProcessingMsg.getMqMessage(), mqProcessingMsg.getInvokeFutures());
                //查询成功进行数据存储
                if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                    messagePublisher.publishMessage(DataSaveBean.builder()
                            .objectList(responseMsg.get().getObjectList()).build());
                }
            }
            //请求-设置
            else if (mqProcessingMsg.getMqMessage().getType() == MqMsgType.MqMsgType_Request.value()
                    && mqProcessingMsg.getMqMessage().getOperator() == MqMsgOperator.MqMsgOperator_Set.value()) {
                if(mqProcessingMsg.getInvokeFutures().size() != mqProcessingMsg.getMessageBaseList().size()){
                    //设置数据项尚未发送完毕
                    boolean failed = mqProcessingMsg.getInvokeFutures().stream().anyMatch(
                            invokeFuture1 -> invokeFuture1.getResponse().getResponseStatus().value()
                                    != ResponseStatus.SUCCESS.value()
                    );
                    if(!failed){

                        //继续发送数据项
                        JsonResult<?> jsonResult = p1049Sender.sendMsgAsync(mqProcessingMsg.getMessageBaseList(), mqProcessingMsg.getInvokeFutures().size());
                        if (jsonResult.isSuccess()) {
                            List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                            mqProcessingMsg.getInvokeFutures().addAll(invokeFutures);
                        } else {
                            log.error("set---2---发送数据异常-{}-index-{}", mqProcessingMsg.getMessageBaseList(), mqProcessingMsg.getInvokeFutures().size());
                        }
                        //设置为false，未处理数据项
                        mqProcessingMsgOp.get().getProcessed().compareAndSet(true, false);
                        return;
                    }
                }else{
                    responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getConfigResponseMsg(mqProcessingMsg.getMqMessage(), mqProcessingMsg.getInvokeFutures());
                    //设置成功进行数据存储
                    if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                        Optional<List<Object>> requestObjects = getConfigRequestObject(mqProcessingMsg.getMqMessage().getObjectList(),
                                mqProcessingMsg.getMqMsgBaseHandler().dataType(),
                                mqProcessingMsg.getMqMessage().getSignalControllerID());
                        if (requestObjects.isPresent()) {
                            messagePublisher.publishMessage(DataSaveBean.builder()
                                    .objectList(requestObjects.get()).build());

                            //判定是否需要发送特征参数加载
                            acsCharaService.sendAcsCharOut(mqProcessingMsg.getMqMessage());
                        }
                    }
                }
            }

            //针对非模拟数据项进行中心机应答
            if (!mqProcessingMsg.getMqMessage().isSimu()) {
                if (responseMsg.isPresent()) {
                    //准备发送应答报文
                    log.debug("准备发送-{}", responseMsg.get());
                    messageSender.send(mqProcessingMsg.getMqMsgBaseHandler().getRoutingKey(), responseMsg.get());
                } else {
                    log.debug("准备发送异常应答-{}", mqProcessingMsg.getMqMessage());
                    sendErrorResponse(mqProcessingMsg.getMqMessage(), "9004", "消息处理异常");
                }
            }else{
                log.debug("模拟数据返回-{}-{}", responseMsg.isPresent() ? "成功" : "失败"
                        , responseMsg.isPresent() ? responseMsg.get() : mqProcessingMsg.getMqMessage());
            }

            mqProcessingMsgMap.remove(mqProcessingMsg.getMqMessage().getMapKey());
            acsCharaService.rmSetMqMessage(mqProcessingMsg.getMqMessage());
        }
    }

    /**
     * 发送异常应答报文
     *
     * @param request
     * @param errCode
     * @param msg
     */
    public void sendErrorResponse(MqMessage request, String errCode, String msg) {
        MqMessage mqMessage = P1049HelpUtils.buildErrorMqResponseMsg(request, errCode, msg);
        messageSender.send("error", mqMessage);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ac = applicationContext;
    }
}
