package com.myweb.daa.areasignal.centralsystem.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.les.ads.ds.gis.arm.ArmSignalControllerDTO;
import com.les.ads.ds.gis.arm.CrossingDTO;
import com.myweb.daa.areasignal.business.entity.QSignalIdMap;
import com.myweb.daa.areasignal.business.entity.SignalIdMap;
import com.myweb.daa.areasignal.centralsystem.utils.SgpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.ControllerNotifyMsg;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossParam;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/12 14:03
 */
@Service
@Slf4j
public class ControllerService {

    /**
     * key为莱斯信号机id
     */
    @Getter
    private Map<String, SignalBaseInfo> signalBaseInfoMap = new ConcurrentHashMap<>();

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private MessagePublisher messagePublisher;

    @Data
    public static class SignalBaseInfo {
        private String signalId;
        private int noArea;
        private int noJunc;
        private String ip;
        private int port;

        private String name;
        private AtomicInteger atomicInteger = new AtomicInteger(0);

        /**
         * 标记1049信号机编号
         */
        private String signalId1049;

        /**
         * 信号机所属的信号机IP地址
         */
        private String address1049Ip;
    }

    /**
     * 生成默认信号机1049ID
     *
     * @param signalBaseInfo
     * @return
     */
    public static String genDefault1049SignalId(SignalBaseInfo signalBaseInfo) {
        return String.format("%s%02d%03d",
                GlobalConfigure.departmentCode, signalBaseInfo.getNoArea(), signalBaseInfo.getNoJunc());
    }

    @PostConstruct
    public void initData() {
        {
            if (testSignalConfigure.isUseTest()) {
                SignalBaseInfo signalBaseInfo = new SignalBaseInfo();
                signalBaseInfo.setSignalId(testSignalConfigure.getSignalId());
                signalBaseInfo.setNoArea(testSignalConfigure.getNoArea());
                signalBaseInfo.setNoJunc(testSignalConfigure.getNoJunc());
                signalBaseInfo.setIp(testSignalConfigure.getIp());
                signalBaseInfo.setPort(testSignalConfigure.getPort());
                signalBaseInfo.setSignalId1049(testSignalConfigure.getSignalId_1049());
                signalBaseInfo.setAddress1049Ip(testSignalConfigure.getSystemIp());
                signalBaseInfo.setName("测试信号机");
                signalBaseInfoMap.put(signalBaseInfo.getSignalId(), signalBaseInfo);
            }
        }

    }

    /**
     * 获取信号机基本数据项
     *
     * @param noArea
     * @param noJunc
     * @return
     */
    public Optional<SignalBaseInfo> getSignalInfo(int noArea, int noJunc) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo ->
                        signalBaseInfo.getNoArea() == noArea
                                && signalBaseInfo.getNoJunc() == noJunc

        ).findFirst();
    }


    /**
     * 获取信号机基本数据项
     *
     * @param ip
     * @param port
     * @return
     */
    public Optional<SignalBaseInfo> getSignalInfoByIpAndPort(String ip, int port) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo -> signalBaseInfo.getIp().equals(ip)
                        && signalBaseInfo.getPort() == port

        ).findFirst();
    }

    /**
     * 根据信号机ip获取信号机基本数据项
     *
     * @param ip
     * @return
     */
    public Optional<SignalBaseInfo> getSignalInfoByIp(String ip) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo ->
                        signalBaseInfo.getIp().compareToIgnoreCase(ip) == 0

        ).findFirst();
    }

    /**
     * 获取信号机基本数据项
     *
     * @param signalId
     * @return
     */
    public Optional<SignalBaseInfo> getSignalInfo(String signalId) {
        SignalBaseInfo signalBaseInfo = signalBaseInfoMap.get(signalId);
        return Optional.ofNullable(signalBaseInfo);
    }

    /**
     * 根据1049信号机id信号机基本数据项
     *
     * @param signalId1049
     * @return
     */
    public Optional<SignalBaseInfo> getSignalInfoBy1049(String signalId1049) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo -> signalId1049.compareToIgnoreCase(signalBaseInfo.getSignalId1049()) == 0
        ).findAny();
    }


    /**
     * 判定是否处理这个信号机的调看命令
     *
     * @param signalId
     * @return
     */
    public boolean processMqMsg(String signalId) {
        return signalBaseInfoMap.containsKey(signalId);
    }

    /**
     * 判定是否是Telvent的信号机
     *
     * @param signalController
     * @return
     */
    private boolean isGB1049Controller(ArmSignalControllerDTO signalController) {
        return true;
    }

    /**
     * 判定是否需要根据品牌进行数据过滤
     *
     * @param signalController
     * @return
     */
    private boolean isBrandProcess(ArmSignalControllerDTO signalController) {
        if (signalController.getSignalControllerId() == null) {
            return false;
        }

        if (GlobalConfigure.brandList == null
                || GlobalConfigure.brandList.isEmpty()) {
            return true;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalController.getSignalControllerId());
        if (signalBrandPort != null && signalBrandPort.name() != null) {
            return GlobalConfigure.brandList.contains(signalBrandPort.name());
        }

        return false;
    }

    /**
     * 判定区域时候进行处理
     *
     * @param signalController
     * @return
     */
    private boolean isAreaProcess(ArmSignalControllerDTO signalController) {
        if (GlobalConfigure.areaList == null
                || GlobalConfigure.areaList.isEmpty()) {
            return true;
        }

        if (signalController.getNoArea() == null) {
            return false;
        }

        return GlobalConfigure.areaList.contains(String.valueOf(signalController.getNoArea()));

    }


    /**
     * 由于当前没有通知机制，只有重新调取数据项进行数据比对，修改本地信号机数据项
     */
    public boolean updateControllerFromSgp() {
        log.info("比对数据项，向sgp-url-{}请求信号机参数", GlobalConfigure.signalParamIp);
        AtomicBoolean dataChange = new AtomicBoolean(false);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/signalController";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取信号机数据返回异常");
                return dataChange.get();
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回信号机的数据项个数是-{}", jsonArray.size());

            ConcurrentHashMap<String, ArmSignalControllerDTO> currentSignalData = new ConcurrentHashMap<>();
            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        ArmSignalControllerDTO signalController = jsonArray.getObject(index, ArmSignalControllerDTO.class);

                        if (signalController.getSignalControllerId() == null) {
                            return;
                        }

                        //针对非处理的telvent信号机进行过滤
                        if (!isGB1049Controller(signalController) || !isBrandProcess(signalController)) {
                            return;
                        }

                        //判定区域是否处理
                        if (!isAreaProcess(signalController)) {
                            log.error("处理区域-{} 信号机数据-{} 区域不匹配,忽略-{}", GlobalConfigure.areaList,
                                    signalController.getSignalControllerId(), signalController);
                            return;
                        }

                        //判定ip是否异常
                        if (signalController.getIp() == null || signalController.getIp().isEmpty()) {
                            log.debug("信号机数据-{} ip为null或为空", signalController.getSignalControllerId());
                            signalController.setIp("");
                        }

                        //判定端口是否异常
                        if (signalController.getPort() == null) {
                            log.debug("信号机数据-{} port为null或为空", signalController.getSignalControllerId());
                            signalController.setPort(0);
                        }


                        currentSignalData.put(signalController.getSignalControllerId(), signalController);
                    }
            );

            //根据当前的信号机数据项进行数据更新或者插入
            //1、遍历本地数据项，移除原先的信号机数据项
            signalBaseInfoMap.keySet().stream().forEach(
                    signalId -> {
                        if (!currentSignalData.containsKey(signalId)) {
                            log.error("数据更新，移除信号机-{}", signalId);
                            dataChange.set(true);
                            signalBaseInfoMap.remove(signalId);
                        }
                    }
            );
            //2、遍历新的信号数据项，进行更新或者插入
            currentSignalData.keySet().stream().forEach(
                    signalId -> {
                        ArmSignalControllerDTO signalController = currentSignalData.get(signalId);
                        SignalBaseInfo signalBaseInfo = signalBaseInfoMap.get(signalId);
                        if (signalBaseInfo != null) {
                            if ((!signalBaseInfo.getIp().equals(signalController.getIp()))
                                    || (signalBaseInfo.getPort() != signalController.getPort().intValue())) {
                                log.error("更新信号机数据项-原始数据{}", signalBaseInfo);
                                signalBaseInfo.setIp(signalController.getIp());
                                signalBaseInfo.setPort(signalController.getPort());
                                log.error("更新信号机数据项-更新后数据{}", signalBaseInfo);
                            } else {
                                log.warn("信号参数未发生变化-{}", signalBaseInfo);
                            }
                        } else {
                            signalBaseInfo = new SignalBaseInfo();
                            signalBaseInfo.setSignalId(signalController.getSignalControllerId());
                            signalBaseInfo.setNoArea(signalController.getNoArea());
                            signalBaseInfo.setNoJunc(signalController.getNoJunc());
                            signalBaseInfo.setIp(signalController.getIp() != null ? signalController.getIp() : "");
                            signalBaseInfo.setPort(signalController.getPort() != null ? signalController.getPort() : 0);
                            signalBaseInfo.setSignalId1049(genDefault1049SignalId(signalBaseInfo));
                            signalBaseInfo.setName(signalController.getName());
                            signalBaseInfo.setAddress1049Ip("***************"); //初始化设置异常IP地址
                            dataChange.set(true);
                            log.error("添加信号机数据项-{}", signalBaseInfo);
                            signalBaseInfoMap.put(signalBaseInfo.getSignalId(), signalBaseInfo);

                            //通知信号机增加
                            messagePublisher.publishMessage(ControllerNotifyMsg.builder().controllerId(signalBaseInfo.getSignalId()).build());

                        }
                    }
            );
        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }

        return dataChange.get();
    }

    /**
     * 从sgp读取信号机基本参数
     */
    public void getControllerFromSgp() {
        log.info("向sgp-url-{}请求信号机参数", GlobalConfigure.signalParamIp);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/signalController";

            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);


            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取信号机数据返回异常");
                return;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回信号机的数据项个数是-{}", jsonArray.size());

            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {

                        try {

                            ArmSignalControllerDTO signalController = jsonArray.getObject(index, ArmSignalControllerDTO.class);

                            //针对非处理的scats信号机进行过滤
                            if (!isGB1049Controller(signalController) || !isBrandProcess(signalController)) {
                                return;
                            }

                            //判定区域是否处理
                            if (!isAreaProcess(signalController)) {
                                log.error("处理区域-{} 信号机数据-{} 区域不匹配,忽略-{}", GlobalConfigure.areaList,
                                        signalController.getSignalControllerId(), signalController);
                                return;
                            }

                            //判定ip是否异常
                            if (signalController.getIp() == null || signalController.getIp().isEmpty()) {
                                log.error("信号机数据-{} ip为null或为空", signalController.getSignalControllerId());
                                signalController.setIp("");
                            }

                            //判定端口是否异常
                            if (signalController.getPort() == null) {
                                log.error("信号机数据-{} 端口为null", signalController.getSignalControllerId());
                                signalController.setPort(0);
                            }

                            //构建数据项存入本地
                            {
                                SignalBaseInfo signalBaseInfo = new SignalBaseInfo();
                                signalBaseInfo.setSignalId(signalController.getSignalControllerId());
                                signalBaseInfo.setNoArea(signalController.getNoArea());
                                signalBaseInfo.setNoJunc(signalController.getNoJunc());
                                signalBaseInfo.setIp(signalController.getIp() != null ? signalController.getIp() : "");
                                signalBaseInfo.setPort(signalController.getPort() != null ? signalController.getPort() : 0);
                                signalBaseInfo.setSignalId1049(genDefault1049SignalId(signalBaseInfo));
                                signalBaseInfo.setName(signalController.getName());
                                signalBaseInfo.setAddress1049Ip("***************"); //初始化设置异常IP地址
                                log.error("添加信号机数据项-{}", signalBaseInfo);
                                signalBaseInfoMap.put(signalBaseInfo.getSignalId(), signalBaseInfo);
                            }
                        } catch (Exception e) {
                            log.error("请求路口基础数据的时候出现异常.", e);
                            log.error("请求路口基础数据的时候出现异常-{}-\n原始数据-{}", e.getCause(), jsonArray.get(index));
                        }
                    }
            );

        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }
    }

    /**
     * 从本地数据库中读取映射，莱斯信号机ID映射到1049信号机ID
     */
    public void loadSignalIdMap() {
        //读取数据项
        List<SignalIdMap> signalIdMaps = queryFactory.selectFrom(QSignalIdMap.signalIdMap).fetch();

        //适配更新设置的1049信号机ID
        signalIdMaps.stream().forEach(
                signalIdMap -> {
                    String lesSignalId = signalIdMap.getLesSignalId();
                    SignalBaseInfo signalBaseInfo = signalBaseInfoMap.get(lesSignalId);
                    if (signalBaseInfo != null) {
                        signalBaseInfo.setSignalId1049(signalIdMap.getSignalId1049());
                        log.error("设置莱斯信号机-{}映射1049信号机-{}", lesSignalId, signalBaseInfo.getSignalId1049());
                    }
                }
        );
    }

    /**
     * 根据1049数据项保存信号机数据项
     *
     * @param crossParam
     * @param lesCrossId
     */
    public ArmSignalControllerDTO genController(CrossParam crossParam,
                                                String lesCrossId,
                                                String lesSignalId,
                                                String officeId,
                                                String manufacturer,
                                                String signalType,
                                                CrossingDTO crossingDTO) {
        ArmSignalControllerDTO signalControllerDTO = new ArmSignalControllerDTO();
        signalControllerDTO.setSignalControllerId(lesSignalId);
        signalControllerDTO.setManufacturer(manufacturer);
        signalControllerDTO.setName(crossParam.getCrossName());
        signalControllerDTO.setNoArea(SgpUtils.getNoAreaFromStandSignalId(lesSignalId));
        signalControllerDTO.setNoJunc(SgpUtils.getNoJuncFromStandSignalId(lesSignalId));
        signalControllerDTO.setNoSubArea(0);
        signalControllerDTO.setPort(0);
        signalControllerDTO.setType(signalType);
        signalControllerDTO.setSimulate(false);
        signalControllerDTO.setCmmType(1);
        signalControllerDTO.setSerialPort("");
        signalControllerDTO.setIp(crossParam.getIP() != null ? crossParam.getIP() : "");
        signalControllerDTO.setOfficeId(officeId);
        List<CrossingDTO> crossingDTOS = new ArrayList<>();
        crossingDTOS.add(crossingDTO);
        signalControllerDTO.setCrossings(crossingDTOS);
        return signalControllerDTO;
    }

}
