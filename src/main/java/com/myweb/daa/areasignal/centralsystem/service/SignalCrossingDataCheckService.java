package com.myweb.daa.areasignal.centralsystem.service;


import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/7/17 15:12
 */
@Service
@Slf4j
public class SignalCrossingDataCheckService {
    @Autowired
    private CrossingService crossingService;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private SystemIpService systemIpService;

    @Scheduled(initialDelay = 120000, fixedRate = 180000)
    public void queryStatus() {
        if (!GlobalConfigure.enableCheckSignalAndCrossingData) {
            return;
        }
        if (testSignalConfigure.isUseTest()) {
            return;
        }
        boolean controllerChange = controllerService.updateControllerFromSgp();
        boolean crossingChange = crossingService.updateCrossingFromSgp();

        //更新设备20230521 出现从sgp读取信号机为空后，找不到映射数据项
        if (controllerChange || crossingChange) {
            try {
                crossingService.loadCrossIdMap();
            } catch (Exception e) {
                log.error("更新路口映射数据异常-{}", e);
            }

            try {
                systemIpService.updateSystemIp();
            } catch (Exception e) {
                log.error("更新ip映射数据异常-{}", e);
            }

            try {
                controllerService.loadSignalIdMap();
            } catch (Exception e) {
                log.error("更新信号机映射数据异常-{}", e);
            }
        }
    }
}
