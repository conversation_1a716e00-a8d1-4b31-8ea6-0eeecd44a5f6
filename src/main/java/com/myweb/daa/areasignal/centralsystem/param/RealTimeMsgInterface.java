package com.myweb.daa.areasignal.centralsystem.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/21 13:40
 */
public interface RealTimeMsgInterface {
    String generateDbUrl();

    @JsonIgnore
    @JSONField(serialize = false)
    String getSgpUrl();

    Object transPushData();
}
