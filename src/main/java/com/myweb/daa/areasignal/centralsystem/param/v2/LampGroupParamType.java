package com.myweb.daa.areasignal.centralsystem.param.v2;

import com.les.ads.ds.enums.LaneAttributeType;
import com.les.ads.ds.enums.LaneFeatureType;
import com.les.ads.ds.enums.LaneMovementType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LampGroupParam;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description: 依据GB/T 39900-2021 A.18.6
 * @date: 2024/7/20
 * @version: 1.0
 * @modifyHistory:
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
public enum LampGroupParamType implements Serializable {

    LM_MOTOR_ROUND("10", "机动车主灯(圆盘灯)"),
    LM_MOTOR_STRAIGHT("21", "机动车方向指示信号灯-直行"),
    LM_MOTOR_LEFT("22", "机动车方向指示信号灯-左转"),
    LM_MOTOR_RIGHT("23", "机动车方向指示信号灯-右转"),
    LM_BICYCLE("30", "非机动车信号灯"),
    LM_BICYCLE_LEFT("32", "非机动车信号灯-左转"),
    LM_PED("40", "人行横道信号灯"),
    LM_PED_IN("41", "人行横道信号灯-进口"),
    LM_PED_OUT("42", "人行横道信号灯-出口"),
    LM_LANE("50", "车道信号灯"),
    LM_STREET_CAR_STRAIGHT("61", "有轨电车信号灯-直行"),
    LM_STREET_CAR_LEFT("62", "有轨电车信号灯-左转"),
    LM_STREET_CAR_RIGHT("63", "有轨电车信号灯-右转"),
    LM_BUS("70", "公交信号灯"),
    LM_TURNROUND("80", "调头信号灯"),
    LM_OTHER("99", "其他");

    private final String code;
    private final String desc;

    LampGroupParamType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isPed() {
        return this == LM_PED || this == LM_PED_IN || this == LM_PED_OUT;
    }

    public boolean isMotor() {
        return this == LM_MOTOR_STRAIGHT || this == LM_MOTOR_LEFT || this == LM_MOTOR_RIGHT
                || this == LM_MOTOR_ROUND || this == LM_TURNROUND  || this == LM_LANE;
    }

    public boolean isBicycle() {
        return this == LM_BICYCLE || this == LM_BICYCLE_LEFT;
    }

    public boolean isBus(){
        return this == LM_BUS;
    }

    public boolean isStreetCar(){
        return this == LM_STREET_CAR_STRAIGHT || this == LM_STREET_CAR_LEFT || this == LM_STREET_CAR_RIGHT;
    }

    public static LampGroupParamType transLesCode(Integer code) {
        LampGroupParamType type = LM_OTHER;
        if (code != null) {
            switch (code) {
                case 10:
                    type = LM_MOTOR_ROUND;
                    break;
                case 11:
                    type = LM_MOTOR_STRAIGHT;
                    break;
                case 12:
                    type = LM_MOTOR_LEFT;
                    break;
                case 13:
                    type = LM_MOTOR_RIGHT;
                    break;
                case 14:
                    type = LM_TURNROUND;
                    break;
                case 21:
                case 22:
                    type = LM_BICYCLE;
                    break;
                case 23:
                    type = LM_BICYCLE_LEFT;
                    break;
                case 31:
                    type = LM_PED;
                    break;
                case 32:
                    type = LM_PED_IN;
                    break;
                case 33:
                    type = LM_PED_OUT;
                    break;
                default:
                    break;
            }
        }
        return type;
    }

    public static LampGroupParamType parseCode(String code) {
        if (code != null) {
            for (LampGroupParamType e : LampGroupParamType.values()) {
                if (code.equals(e.code)) {
                    return e;
                }
            }
        }
        return LM_OTHER;
    }

    //获取灯组放行的车道特征
    public LaneFeatureType getLaneFeatureType(){
        LampGroupParamType lampGroupParamType = this;
        if(lampGroupParamType ==  LampGroupParamType.LM_OTHER){
            return LaneFeatureType.OTHER;
        }else if (lampGroupParamType.isMotor() ){
            return LaneFeatureType.MOTORWAY;
        } else if (lampGroupParamType.isBicycle() ){
            return LaneFeatureType.BICYCLE_LANE;
        } else if (lampGroupParamType.isPed() ){
            return LaneFeatureType.ZEBRA_CROSSING;
        }  else if (lampGroupParamType.isBus() ){
            return LaneFeatureType.BUS_LANE;
        } else if (lampGroupParamType.isStreetCar() ){
            return LaneFeatureType.STREETCAR_LANE;
        } else {
            return LaneFeatureType.OTHER;
        }
    }

    //获取灯组放行的车道属性
    public LaneAttributeType getLaneAttributeType(){
        LampGroupParamType lampGroupParamType = this;
       if(lampGroupParamType == LampGroupParamType.LM_OTHER){
            return LaneAttributeType.OTHER;
        }else if (lampGroupParamType.isMotor() || lampGroupParamType.isBicycle()
                  || lampGroupParamType.isBus() || lampGroupParamType.isStreetCar()){
            return LaneAttributeType.ENTER_PORT;
        } else if(lampGroupParamType.isPed()){
            if(lampGroupParamType == LampGroupParamType.LM_PED){
                return LaneAttributeType.OTHER;
            }else if(lampGroupParamType == LampGroupParamType.LM_PED_IN
                    || lampGroupParamType == LampGroupParamType.LM_PED_OUT) {
                return LaneAttributeType.TWICE_CROSSING;
            }
        }

        return LaneAttributeType.OTHER;
    }

    //获取灯组放行的车道流向
    public LaneMovementType getLaneMovementType(Map<String, LampGroupParam> lampGroupParamMap, int direction) {
        LampGroupParamType lampGroupParamType = this;
        if(lampGroupParamType == LampGroupParamType.LM_OTHER){
            return  LaneMovementType.RF_OTHER;
        }else if (lampGroupParamType == LM_MOTOR_ROUND){

            //查看相同方向是否包含其他放行流向的灯组
            AtomicBoolean left = new AtomicBoolean(false);
            AtomicBoolean right = new AtomicBoolean(false);
            lampGroupParamMap.values().stream().filter(
                    lampGroupParam -> {
                        if (lampGroupParam.getDirection() == null) {
                            return false;
                        } else {
                            return lampGroupParam.getDirection().intValue() == (direction);
                        }
                    }
            ).forEach(
                    lampGroupParam -> {
                        if(lampGroupParam.getType().equals(LampGroupParamType.LM_MOTOR_LEFT.getCode())){
                            left.set(true);
                        }else if(lampGroupParam.getType().equals(LampGroupParamType.LM_MOTOR_RIGHT.getCode())){
                            right.set(true);
                        }
                    }
            );

            //同时包含左右
            if(left.get() && right.get()){
                return LaneMovementType.RF_STRAIGHT;
            }else if(left.get() && !right.get()){
                return LaneMovementType.RF_STRAIGHT_RIGHT;
            }else if(!left.get() && right.get()){
                return LaneMovementType.RF_STRAIGHT_LEFT;
            } else {
                return LaneMovementType.RF_STRAIGHT_LEFT_RIGHT;
            }
        } else if (lampGroupParamType == LM_MOTOR_STRAIGHT){
            return  LaneMovementType.RF_STRAIGHT;
        } else if (lampGroupParamType == LM_MOTOR_LEFT){
            return  LaneMovementType.RF_LEFT;
        } else if (lampGroupParamType == LM_MOTOR_RIGHT){
            return  LaneMovementType.RF_RIGHT;
        } else if (lampGroupParamType == LM_BICYCLE){
            return  LaneMovementType.RF_STRAIGHT_LEFT_RIGHT;
        } else if (lampGroupParamType == LM_BICYCLE_LEFT){
            return  LaneMovementType.RF_LEFT;
        } else if (lampGroupParamType == LM_PED){
            return  LaneMovementType.RF_OTHER;
        } else if (lampGroupParamType == LM_PED_IN) {
            return LaneMovementType.RF_OTHER;
        } else if (lampGroupParamType == LM_PED_OUT) {
            return LaneMovementType.RF_OTHER;
        } else if (lampGroupParamType == LM_LANE) {
            return LaneMovementType.RF_STRAIGHT_LEFT_RIGHT;
        } else if (lampGroupParamType == LM_STREET_CAR_STRAIGHT) {
            return LaneMovementType.RF_STRAIGHT;
        } else if (lampGroupParamType == LM_STREET_CAR_LEFT) {
            return LaneMovementType.RF_LEFT;
        } else if (lampGroupParamType == LM_STREET_CAR_RIGHT) {
            return LaneMovementType.RF_RIGHT;
        } else if (lampGroupParamType == LM_BUS) {
            return LaneMovementType.RF_STRAIGHT_LEFT_RIGHT;
        } else if (lampGroupParamType == LM_TURNROUND) {
            return LaneMovementType.RF_TURNROUND;
        }
       return  LaneMovementType.RF_OTHER;
    }
}
