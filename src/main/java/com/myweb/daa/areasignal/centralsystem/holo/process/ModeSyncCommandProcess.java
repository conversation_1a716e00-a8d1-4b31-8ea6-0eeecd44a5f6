package com.myweb.daa.areasignal.centralsystem.holo.process;

import com.myweb.daa.areasignal.centralsystem.holo.CentralCmdType;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.ModeSyncCommand;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossControlModePublish;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ModeSyncCommandProcess {


    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private CrossControlModePublish crossControlModePublish;

    @Value("#{'${global.enableModeSync:false}'}")
    private boolean enableModeSync;

    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void juncModeDtoProcess(ModeSyncCommand modeSyncCommand) {

        if (!enableModeSync) {
            return;
        }

        log.trace("控制方式同步-{}", modeSyncCommand);

        //参数判定
        if (modeSyncCommand == null || modeSyncCommand.getNoTab() == null || modeSyncCommand.getNoArea() == null
                || modeSyncCommand.getJuncMode() == null || modeSyncCommand.getJuncMode().isEmpty()) {
            return;
        }

        /**
         *     ModeSyncFirstPart(45, "控制方式检验表-前125个路口"),
         *     ModeSyncSecondPart(46, "控制方式检验表-后125个路口"),
         *     ModeSyncThirdPart(146, "控制方式检验表-后250个路口");
         */

        if ((modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncFirstPart.getValue())
                || ((modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncSecondPart.getValue()))) {
            if (modeSyncCommand.getJuncMode().size() != 125) {
                return;
            }
        } else if (modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncThirdPart.getValue()) {
            if (modeSyncCommand.getJuncMode().size() != 250) {
                return;
            }
        }


        controllerService.getSignalBaseInfoMap().values()
                .forEach(
                        signalBaseInfo -> {
                            int systemCurMode = 0;
                            if (modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncFirstPart.getValue()) {
                                if (signalBaseInfo.getNoJunc() >= 125) {
                                    return;
                                }

                                systemCurMode = modeSyncCommand.getJuncMode().get(signalBaseInfo.getNoJunc());
                            } else if (modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncSecondPart.getValue()) {
                                if (signalBaseInfo.getNoJunc() < 125 || signalBaseInfo.getNoJunc() >= 250) {
                                    return;
                                }

                                systemCurMode = modeSyncCommand.getJuncMode().get(signalBaseInfo.getNoJunc() - 125);
                            } else if (modeSyncCommand.getNoTab() == CentralCmdType.ModeSyncThirdPart.getValue()) {
                                if (signalBaseInfo.getNoJunc() < 250 || signalBaseInfo.getNoJunc() >= 500) {
                                    return;
                                }

                                systemCurMode = modeSyncCommand.getJuncMode().get(signalBaseInfo.getNoJunc() - 250);
                            } else {
                                return;
                            }


                            final int curModeToCheck = systemCurMode;

                            //获取当前路口控制方式,上位系统不知道当前 控制方式，不处理
                            if (255 == curModeToCheck) {
                                return;
                            }

                            {
                                Optional<List<CrossingService.CrossingBaseInfo>> crossingInfos = crossingService.getCrossingInfos(signalBaseInfo.getSignalId());
                                if (crossingInfos.isPresent()) {
                                    crossingInfos.get().stream().forEach(
                                            crossingBaseInfo -> {
                                                Optional<Integer> currentLesControlModeOp = crossControlModePublish.getCurrentLesControlMode(crossingBaseInfo.getCrossingId());
                                                if (currentLesControlModeOp.isPresent() &&
                                                        (currentLesControlModeOp.get().intValue() != curModeToCheck)) {
                                                    log.debug("上级系统路口{}控制方式{}不一致，向上级系统发送控制方式{}", crossingBaseInfo.getCrossingId(), curModeToCheck, currentLesControlModeOp.get());
                                                    //上级系统与当前控制方式不匹配，发送当前路口控制方式数据
                                                    crossControlModePublish.notifyCrossMode(crossingBaseInfo, String.valueOf(currentLesControlModeOp.get()), false);
                                                }
                                            }
                                    );
                                }
                            }
                        }
                );

    }

}
