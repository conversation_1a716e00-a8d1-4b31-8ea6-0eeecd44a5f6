package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.enums.DirectionType;

/**
 * 方向
 */
public enum LesManualControl {
    START(30, "北"),
    STEP(31, "东北"),
    STOP(32, "东");
    private final int code;
    private final String desp;
    LesManualControl(int code, String desp) {
        this.code = code;
        this.desp = desp;
    }
    public int getCode() {
        return code;
    }
    public String getDesp() {
        return desp;
    }
    @Override
    public String toString() {
        return desp;
    }
}
