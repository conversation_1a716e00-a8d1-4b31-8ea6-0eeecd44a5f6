package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.TimeParam;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.TimeSync;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class TimeParamHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return TimeParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return TimeParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return TimeParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        AtomicInteger errorCount = new AtomicInteger(0);
        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    TimeParam timeParam =
                            jsonObject.toJavaObject(TimeParam.class);

                    log.error("收到时间设置命令{}", timeParam);

                    long globalTime = timeParam.getGlobalTime();

                    LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(globalTime), ZoneOffset.of("+8"));

                    //待修改的1049阶段数据项
                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.TimeSync
                            timeSync = new TimeSync();
                    timeSync.setYear(localDateTime.getYear());
                    timeSync.setMonth(localDateTime.getMonthValue());
                    timeSync.setDay(localDateTime.getDayOfMonth());
                    timeSync.setHour(localDateTime.getHour());
                    timeSync.setMinute(localDateTime.getMinute());
                    timeSync.setSecond(localDateTime.getSecond());

                    //待修改的阶段数据
                    P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                            .messageType(MessageType.REQUEST)
                            .operationName(OperationName.Set)
                            .object(timeSync)
                            .signalBrandPort(signalBrandPort)
                            .address(crossingInfoOp.get().getAddress1049Ip())
                            .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.TimeSync.class).build();
                    requestMsgs.add(p1049CentralSystemMsg);

                });

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送时间设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<TimeParam> timeParams = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        TimeParam timeParam = jsonObject.toJavaObject(TimeParam.class);
                        timeParams.add(timeParam);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        timeParams.stream().forEach(
                timeParam -> {

                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(timeParam.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(timeParam.getSignalControllerID());

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }


                                //如果非阶段数据项
                                if (!(firstMsgData.get() instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.TimeSync)) {
                                    return;
                                }

                                {
                                    log.error("加载{}时间参数返回{}", crossingInfoOp.get().getControllerId(), firstMsgData.get());
                                }

                            }
                    );
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
