package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.Pattern;
import com.les.ads.ds.signal.Ring;
import com.les.ads.ds.signal.Sequence;
import com.les.ads.ds.signal.Split;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 16:03
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TimeParam {
    public static final String MqObjectId = "1011";

    private String signalControllerID;
    private int noArea;
    private int noJunc;

    private long globalTime;

    private int crossingSeqNo;

}
