package com.myweb.daa.areasignal.centralsystem.service;

import com.les.ads.ds.enums.DefaultPhase;
import com.les.ads.ds.enums.DefaultStagePhase;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/11/5 13:56
 */
@Service
@Slf4j
public class DefaultStageService {

    private final static List<StageParam> defaultStageList = Collections.synchronizedList(new ArrayList<>());

    static {


        //单流向
        {
            List<DefaultPhase> defaultPhases = new ArrayList<>();
            defaultPhases.add(DefaultPhase.NORTH_LEFT);
            defaultPhases.add(DefaultPhase.NORTH_STRAIGHT);
            defaultPhases.add(DefaultPhase.NORTH_RIGHT);
            defaultPhases.add(DefaultPhase.EAST_LEFT);
            defaultPhases.add(DefaultPhase.EAST_STRAIGHT);
            defaultPhases.add(DefaultPhase.EAST_RIGHT);
            defaultPhases.add(DefaultPhase.EAST_LEFT);
            defaultPhases.add(DefaultPhase.EAST_STRAIGHT);
            defaultPhases.add(DefaultPhase.EAST_RIGHT);
            defaultPhases.add(DefaultPhase.EAST_LEFT);
            defaultPhases.add(DefaultPhase.EAST_STRAIGHT);
            defaultPhases.add(DefaultPhase.EAST_RIGHT);
            defaultPhases.stream().forEach(
                    defaultPhase -> {
                        List<Integer> phaseNos = new ArrayList();
                        phaseNos.add(defaultPhase.getCode());
                        StageParam stageParam = StageParam.builder().stageName(defaultPhase.getDesc()).phaseNoList(phaseNos).build();
                        defaultStageList.add(stageParam);
                    }
            );
        }

        //单边通行
        {
            //北向通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("北向通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东向通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东向通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //南向通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南向通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //西向通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("西向通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //单边左直
        {
            //北向左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("北向左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东向左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东向左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //南向左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南向左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //西向左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("西向左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //单边直右
        {
            //北向直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("北向直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东向直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东向直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //南向直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南向直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //西向直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("西向直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //单边左右
        {
            //北向左右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("北向左右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东向左右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东向左右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //南向左右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南向左右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //西向左右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("西向左右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边通行
        {
            //南北通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西通行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西通行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边直左
        {
            //南北直左
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北直左").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西直左
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西直左").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边直右
        {
            //南北直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西直右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西直右").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边左直
        {
            //南北左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西左直
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西左直").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }


        //双边左转
        {
            //南北左转
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_LEFT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_LEFT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北左转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西左转
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_LEFT.getCode());
                phaseNos.add(DefaultPhase.WEST_LEFT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西左转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边直行
        {
            //南北直行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北直行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西直行
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西直行").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //双边右转
        {
            //南北右转
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南北右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东西右转
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东西右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }

        //单边右转
        {
            //北右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.NORTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("北右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //东右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("东右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //南右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.SOUTH_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("南右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }

            //西右
            {
                List<Integer> phaseNos = new ArrayList();
                phaseNos.add(DefaultPhase.WEST_RIGHT.getCode());
                StageParam stageParam = StageParam.builder().stageName("西右转").phaseNoList(phaseNos).build();
                defaultStageList.add(stageParam);
            }
        }


        //莱斯32相位数据转换
        {
            Arrays.stream(DefaultStagePhase.values()).forEach(
                    defaultStagePhase -> {
                        List<DefaultPhase> defaultPhases = defaultStagePhase.getDefaultPhases();
                        List<Integer> integers = defaultPhases.stream().map(defaultPhase -> defaultPhase.getCode()).collect(Collectors.toList());
                        StageParam stageParam = StageParam.builder().stageName(defaultStagePhase.getDesc())
                                .phaseNoList(integers).build();
                        defaultStageList.add(stageParam);
                    }
            );
        }

    }


    /**
     * 根据传递的数据项查询获取阶段数据项定义
     * 默认相位包含则可以筛选出来，取个数最小的阶段名称
     *
     * @param defaultPhaseNos
     * @return
     */
    public static Optional<StageParam> getStageParam(List<Integer> defaultPhaseNos) {
        if (defaultPhaseNos == null || defaultPhaseNos.isEmpty()) {
            return Optional.empty();
        }


        List<StageParam> stageParams = defaultStageList.stream().filter(
                defaultStage -> {
                    List<Integer> phaseNoList = defaultStage.getPhaseNoList();
                    if (phaseNoList == null || phaseNoList.isEmpty()) {
                        return false;
                    }

                    //查找不包含的数据项
                    Optional<Integer> notContained = defaultPhaseNos.stream().filter(
                            defaultPhaseNo ->
                            {
                                //不考虑人行道数据
                                if (defaultPhaseNo >= DefaultPhase.NORTH_PED.getCode()) {
                                    return false;
                                }

                                return !phaseNoList.contains(defaultPhaseNo);
                            }
                    ).findAny();

                    if (notContained.isPresent()) {
                        return false;
                    } else {
                        return true;
                    }
                }
        ).collect(Collectors.toList());

        if (stageParams == null || stageParams.isEmpty()) {
            return Optional.empty();
        }

        //按照相位个数进行排序
        stageParams.sort(
                Comparator.comparingInt(stageParam -> stageParam.getPhaseNoList().size())
        );

        return Optional.of(stageParams.get(0));
    }

    public static void main(String[] args) {

        {
            List<Integer> defaultPhaseNos = new ArrayList<>();
            defaultPhaseNos.add(DefaultPhase.EAST_LEFT.getCode());

            Optional<StageParam> stageParamOp = getStageParam(defaultPhaseNos);
            if (stageParamOp.isPresent()) {
                log.debug("相位-{}获取的数据项是-{}-{}", defaultPhaseNos, stageParamOp.get().getStageName(), stageParamOp.get().getPhaseNoList());
            }
        }

        {
            List<Integer> defaultPhaseNos = new ArrayList<>();
            defaultPhaseNos.add(DefaultPhase.EAST_LEFT.getCode());
            defaultPhaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
            Optional<StageParam> stageParamOp = getStageParam(defaultPhaseNos);
            if (stageParamOp.isPresent()) {
                log.debug("相位-{}获取的数据项是-{}-{}", defaultPhaseNos, stageParamOp.get().getStageName(), stageParamOp.get().getPhaseNoList());
            }
        }

        {
            List<Integer> defaultPhaseNos = new ArrayList<>();
            defaultPhaseNos.add(DefaultPhase.EAST_LEFT.getCode());
            defaultPhaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
            defaultPhaseNos.add(DefaultPhase.EAST_RIGHT.getCode());
            Optional<StageParam> stageParamOp = getStageParam(defaultPhaseNos);
            if (stageParamOp.isPresent()) {
                log.debug("相位-{}获取的数据项是-{}-{}", defaultPhaseNos, stageParamOp.get().getStageName(), stageParamOp.get().getPhaseNoList());
            }
        }

        {
            List<Integer> defaultPhaseNos = new ArrayList<>();
            defaultPhaseNos.add(DefaultPhase.EAST_LEFT.getCode());
            defaultPhaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());
            defaultPhaseNos.add(DefaultPhase.EAST_RIGHT.getCode());

            defaultPhaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
            Optional<StageParam> stageParamOp = getStageParam(defaultPhaseNos);
            if (stageParamOp.isPresent()) {
                log.debug("相位-{}获取的数据项是-{}-{}", defaultPhaseNos, stageParamOp.get().getStageName(), stageParamOp.get().getPhaseNoList());
            }
        }

        {
            List<Integer> defaultPhaseNos = new ArrayList<>();
            defaultPhaseNos.add(DefaultPhase.EAST_STRAIGHT.getCode());

            defaultPhaseNos.add(DefaultPhase.WEST_STRAIGHT.getCode());
            Optional<StageParam> stageParamOp = getStageParam(defaultPhaseNos);
            if (stageParamOp.isPresent()) {
                log.debug("相位-{}获取的数据项是-{}-{}", defaultPhaseNos, stageParamOp.get().getStageName(), stageParamOp.get().getPhaseNoList());
            }
        }
    }

}
