package com.myweb.daa.areasignal.centralsystem.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/4/18 13:41
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SignalIdMapDto {

    private String id;

    private String lesSignalId;

    private String lesSignalName;

    /**
     * 区域号
     */
    private String signalId1049;

    /**
     * 信号机关联的路口名称
     */
    private String crossName1049;

    /**
     * 是否映射
     */
    private boolean mapped;

}
