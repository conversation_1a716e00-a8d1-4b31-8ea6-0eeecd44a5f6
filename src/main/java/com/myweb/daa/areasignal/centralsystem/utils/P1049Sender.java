package com.myweb.daa.areasignal.centralsystem.utils;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseMessage;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.MessageSendProcess;
import com.myweb.daa.areasignal.protocol.p1049.process.P1049Manager;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseOperation1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.Body1049;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/3/31 10:10
 */
@Service
@Slf4j
@Component
public class P1049Sender {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageOuterPublisher messageOuterPublisher;

    @Autowired
    private MessageSendProcess messageSendProcess;

    @Autowired
    private P1049Manager p1049Manager;

    @Autowired
    private P1049Configure p1049Configure;


    /**
     * 处理数据数据项
     *
     * @param invokeFutures
     * @return
     */
    public JsonResult<?> analyzeData(List<InvokeFuture> invokeFutures) {
        //根据数据项进行同步等待
        final List<JsonResult<?>> results = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger failedCount = new AtomicInteger(0);
        //等待应答
        invokeFutures.parallelStream().forEach(
                future -> {
                    try {
                        LocalDateTime startTime = LocalDateTime.now();
                        log.debug("开始等待-{}应答-{}", future.getRequest().getAckKey(), startTime);
                        ResponseMessage response = future.waitResponse();
                        LocalDateTime endTime = LocalDateTime.now();
                        long millis = Duration.between(endTime, startTime).toMillis();
                        log.trace("{}- 收到了应答-{},花费时间-{},报文中时间-{} 数据应答项-{}", future.getRequest().getAckKey(), endTime, millis,
                                System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
                        /**解析应答数据项*/
                        if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                            JsonResult<?> error = new JsonResult(false, response.getResponseStatus().des(), future.getRequest().getAckKey());
                            log.error("{}", error);
                            results.add(error);
                            failedCount.getAndIncrement();
                        } else {
                            JsonResult<?> result = new JsonResult(true, future.getRequest().getAckKey() + "花费时间" + millis + "ms",
                                    response.getResponseObject());
                            log.debug("{}", result);
                            results.add(result);
                        }
                    } catch (InterruptedException e) {
                        log.error("等待异常", e);
                    }
                }
        );

        return new JsonResult(failedCount.get() == 0 ? true : false, failedCount.get() == 0 ? "数据处理完成" : "存在请求异常的数据项", results);
    }

    /**
     * 发送指定的报文数据项
     *
     * @param messageBases
     */
    public JsonResult<?> sendMsgAsync(List<P1049CentralSystemMsg> messageBases) {
        int timeOutSecond = GlobalConfigure.timeOutSecond;
        if (messageBases == null || messageBases.isEmpty()) {
            return JsonResult.error("发送参数异常-" + messageBases);
        }

        //获取token
        Optional<String> tokenOptional = p1049Manager.getLoginToken(messageBases.get(0).getSignalBrandPort(),
                messageBases.get(0).getAddress());
        if (!tokenOptional.isPresent()) {
            return JsonResult.error("客户端尚未登录，无法发送的数据-" + messageBases);
        }

        //获取地址
        Optional<String> ipOptional = Optional.of(messageBases.get(0).getAddress());

        //根据数据项进行同步等待
        List<InvokeFuture> invokeFutures = new ArrayList<>();
        messageBases.stream().forEach(
                messageBase -> {

                    //获取对象中名称，首字母小写
                    String simpleName = (messageBase.getClazz().getSimpleName()); //toLowerCaseFirstOne

                    //构建生成operation数据项
                    Optional<BaseOperation1049> baseOperation1049Optional = BaseOperation1049.buildBaseOperation1049(simpleName,
                            messageBase.getObject());
                    if (!baseOperation1049Optional.isPresent()) {
                        log.error("尚不支持发送的数据-{}", messageBase);
                        return;
                    }

                    //设置operation Order以及Name
                    BaseOperation1049 operation1049 = baseOperation1049Optional.get();
                    operation1049.setOrder("1");
                    operation1049.setName(messageBase.getOperationName().name());

                    List<BaseOperation1049> operations = new ArrayList<>();
                    operations.add(operation1049);

                    //构造完整报文数据项
                    BaseMessage1049 baseMessage1049 = BaseMessage1049.builder()
                            .body(Body1049.builder()
                                    .operation(operations).build())
                            .build();
                    baseMessage1049.setSignalBrandPortOptional(Optional.of(messageBase.getSignalBrandPort()));

                    String token = tokenOptional.get();
                    //设置头数据项
                    P1049Utils.setBaseHeader1049(baseMessage1049, p1049Configure, token, messageBase.getMessageType()
                            , messageSendProcess.getSeq());
                    //设置发送目的
                    baseMessage1049.setAddress(baseMessage1049.getAddress());
                    //数据发送
                    InterProtocol interProtocol = P1049Utils.buildMessage(ipOptional.get(), InterProtocolType.P1049_MSG
                            , baseMessage1049, true, timeOutSecond, messageBase.isSimuSend());

                    /**发送报文*/
                    Optional<InvokeFuture> invokeFuture = messageOuterPublisher.sendMessageOuter(interProtocol);
                    if (invokeFuture.isPresent()) {
                        InvokeFuture future = invokeFuture.get();
                        invokeFutures.add(future);
                    }
                }
        );
        return new JsonResult(true, "异步发送结果", invokeFutures);
    }



    /**
     * 发送指定的报文数据项
     *
     * @param messageBases
     */
    public JsonResult<?> sendMsgAsync(List<P1049CentralSystemMsg> messageBases, int index) {
        int timeOutSecond = GlobalConfigure.timeOutSecond;
        if (messageBases == null || messageBases.isEmpty() || index < 0 || index >= messageBases.size()) {
            return JsonResult.error("发送参数异常-" + messageBases);
        }

        //获取token
        Optional<String> tokenOptional = p1049Manager.getLoginToken(messageBases.get(0).getSignalBrandPort(),
                messageBases.get(0).getAddress());
        if (!tokenOptional.isPresent()) {
            return JsonResult.error("客户端尚未登录，无法发送的数据-" + messageBases);
        }

        //获取地址
        Optional<String> ipOptional = Optional.of(messageBases.get(0).getAddress());

        //根据数据项进行同步等待
        List<InvokeFuture> invokeFutures = new ArrayList<>();
        P1049CentralSystemMsg messageBase = messageBases.get(index);
        {
            //获取对象中名称，首字母小写
            String simpleName = (messageBase.getClazz().getSimpleName()); //toLowerCaseFirstOne

            //构建生成operation数据项
            Optional<BaseOperation1049> baseOperation1049Optional = BaseOperation1049.buildBaseOperation1049(simpleName,
                    messageBase.getObject());
            if (!baseOperation1049Optional.isPresent()) {
                log.error("尚不支持发送的数据-{}", messageBase);
                return JsonResult.error("构建数据发送项异常，数据索引-{}" + index + ",报文" + messageBases);
            }

            //设置operation Order以及Name
            BaseOperation1049 operation1049 = baseOperation1049Optional.get();
            operation1049.setOrder("1");
            operation1049.setName(messageBase.getOperationName().name());

            List<BaseOperation1049> operations = new ArrayList<>();
            operations.add(operation1049);

            //构造完整报文数据项
            BaseMessage1049 baseMessage1049 = BaseMessage1049.builder()
                    .body(Body1049.builder()
                            .operation(operations).build())
                    .build();
            baseMessage1049.setSignalBrandPortOptional(Optional.of(messageBase.getSignalBrandPort()));

            String token = tokenOptional.get();
            //设置头数据项
            P1049Utils.setBaseHeader1049(baseMessage1049, p1049Configure, token, messageBase.getMessageType()
                    , messageSendProcess.getSeq());
            //设置发送目的
            baseMessage1049.setAddress(baseMessage1049.getAddress());
            //数据发送
            InterProtocol interProtocol = P1049Utils.buildMessage(ipOptional.get(), InterProtocolType.P1049_MSG
                    , baseMessage1049, true, timeOutSecond, messageBase.isSimuSend());

            /**发送报文*/
            Optional<InvokeFuture> invokeFuture = messageOuterPublisher.sendMessageOuter(interProtocol);
            if (invokeFuture.isPresent()) {
                InvokeFuture future = invokeFuture.get();
                invokeFutures.add(future);
            }
        }

        return new JsonResult(true, "异步发送结果", invokeFutures);
    }

}
