package com.myweb.daa.areasignal.centralsystem.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用来记录当前是否在手动控制模式下面
 */
@Service
@Slf4j
public class ManualControlService {

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ManualControlData {
        private LocalDateTime startTime;
    }

    /**
     * 内存记录当前是否在手动控制下面
     */
    private Map<String, ManualControlData> manualControlDataMap = new ConcurrentHashMap<>();


    /**
     * 判定当前路口是否在手动控制下面
     *
     * @param crossingId
     * @return
     */
    public boolean isManualControl(String crossingId) {
        return manualControlDataMap.containsKey(crossingId);
    }

    /**
     * 开始手动控制
     *
     * @param crossingId
     */
    public void putStart(String crossingId) {
        ManualControlData manualControlData = new ManualControlData();
        manualControlData.setStartTime(LocalDateTime.now());
        log.error("添加路口{}手动控制-{}", crossingId, manualControlData);
        manualControlDataMap.put(crossingId, manualControlData);
    }

    /**
     * 移除手动控制
     *
     * @param crossingId
     */
    public void rmManual(String crossingId) {
        log.error("准备移除路口{}手动控制", crossingId);
        ManualControlData manualControlData = manualControlDataMap.get(crossingId);
        if (manualControlData != null) {
            log.error("移除路口{}旧手动控制数据项-{}", crossingId, manualControlData);
        }
        manualControlDataMap.remove(crossingId);
    }

}
