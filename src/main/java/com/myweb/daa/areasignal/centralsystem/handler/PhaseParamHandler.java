package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.les.ads.ds.enums.DefaultPhase;
import com.les.ads.ds.enums.DirectionType;
import com.les.ads.ds.enums.LaneMovementType;
import com.les.ads.ds.gis.Crossing;
import com.les.ads.ds.gis.Lane;
import com.les.ads.ds.gis.Movement;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.dbsave.SgpDbSaveEntityProcess;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.PhaseParam;
import com.myweb.daa.areasignal.centralsystem.param.v2.DirectionType39900;
import com.myweb.daa.areasignal.centralsystem.param.v2.LampGroupParamType;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LampGroupParam;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class PhaseParamHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private SgpDbSaveEntityProcess sgpDbSaveEntityProcess;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return PhaseParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return PhaseParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return PhaseParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {

        log.error("调看信号机{}相位参数数据-{}", controllerId, datas);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();

        {

            {
                //请求信号组参数
                {
                    TSCCmd tscCmd = TSCCmd.builder()
                            .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam.class.getSimpleName())
                            .ID(crossingInfoOp.get().getCrossingId1049())
                            .No("")
                            .build();
                    List<TSCCmd> tscCmds = new ArrayList<>();
                    tscCmds.add(tscCmd);
                    P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                            .messageType(MessageType.REQUEST)
                            .operationName(OperationName.Get)
                            .object(tscCmds)
                            .signalBrandPort(signalBrandPort)
                            .address(crossingInfoOp.get().getAddress1049Ip())
                            .clazz(TSCCmd.class).build();
                    requestMsgs.add(p1049CentralSystemMsg);
                }
            }

            {
                //请求信号灯组参数
                {
                    TSCCmd tscCmd = TSCCmd.builder()
                            .ObjName(LampGroupParam.class.getSimpleName())
                            .ID(crossingInfoOp.get().getCrossingId1049())
                            .No("")
                            .build();
                    List<TSCCmd> tscCmds = new ArrayList<>();
                    tscCmds.add(tscCmd);
                    P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                            .messageType(MessageType.REQUEST)
                            .operationName(OperationName.Get)
                            .object(tscCmds)
                            .signalBrandPort(signalBrandPort)
                            .address(crossingInfoOp.get().getAddress1049Ip())
                            .clazz(TSCCmd.class).build();
                    requestMsgs.add(p1049CentralSystemMsg);
                }
            }
        }

        //判定数据发送是否异常
        if(requestMsgs.isEmpty()){
            return Optional.empty();
        }

        return Optional.of(requestMsgs);
    }


    /**
     * @param laneMovementType1
     * @param laneMovementType2
     * @return
     */
    private boolean isLaneMovementTypeContain(LaneMovementType laneMovementType1, LaneMovementType laneMovementType2) {

        try {
            //转换单流向
            List<LaneMovementType> laneMovementTypes_1 = new ArrayList<>();
            List<LaneMovementType> laneMovementTypes_2 = new ArrayList<>();

            {
                String simpleName = laneMovementType1.getSimpleName();
                String[] simpleNameArr = simpleName.split("_");

                for (String name : simpleNameArr) {
                    LaneMovementType m = LaneMovementType.parseName("RF_" + name);
                    if (!laneMovementTypes_1.contains(m)) {
                        laneMovementTypes_1.add(m);
                    }
                }
            }

            {
                String simpleName = laneMovementType2.getSimpleName();
                String[] simpleNameArr = simpleName.split("_");

                for (String name : simpleNameArr) {
                    LaneMovementType m = LaneMovementType.parseName("RF_" + name);
                    if (!laneMovementTypes_2.contains(m)) {
                        laneMovementTypes_2.add(m);
                    }
                }
            }

            Optional<LaneMovementType> any = laneMovementTypes_1.stream().filter(
                    laneMovementType -> laneMovementTypes_2.contains(laneMovementType)
            ).findAny();
            return any.isPresent();
        } catch (Exception e) {
            log.error("异常", e);
        }

        return false;
    }

    /**
     * 将1049读取的车道转换成莱斯车道
     * @param lampGroupNos
     * @param lampGroupParamMap
     * @param lanes
     * @return
     */
    private List<Integer> changeToSgpLanes(List<String> lampGroupNos,
                                             Map<String, LampGroupParam> lampGroupParamMap,
                                             List<Lane> lanes){
        List<Integer> laneNos = new ArrayList<>();
        if(lampGroupNos == null || lampGroupParamMap == null || lanes == null){
            return laneNos;
        }

        lampGroupNos.stream().forEach(
            lampGroupNo -> {
                LampGroupParam lampGroupParam = lampGroupParamMap.get(lampGroupNo);
                if(lampGroupParam == null){
                    return;
                }

                //获取车道方向
                DirectionType directionType =  DirectionType39900.trans2Les(lampGroupParam.getDirection());
                //灯组类型
                LampGroupParamType lampGroupParamType = LampGroupParamType.parseCode(lampGroupParam.getType());

                log.debug("1049灯组参数{}-{}", lampGroupParam.getLampGroupNo(), lampGroupParam);

                lanes.stream().filter(
                        lane -> {
                            boolean direction =  (lane.getDirection() == directionType);
                            boolean attribute =  (lane.getAttribute() == lampGroupParamType.getLaneAttributeType());
                            boolean movement = isLaneMovementTypeContain(lane.getMovement(), lampGroupParamType.getLaneMovementType(lampGroupParamMap, lampGroupParam.getDirection()));
                            boolean character  = (lane.getCharacter() == lampGroupParamType.getLaneFeatureType());

                            if(direction && attribute && movement && character){
                                return true;
                            }else{
                                return false;
                            }
                        }
                ).forEach(
                    lane -> {
                        if(!laneNos.contains(lane.getLaneNo())){
                            laneNos.add(lane.getLaneNo());
                            log.debug("匹配系统机动车道{}-{}", lane.getLaneNo(), lane);
                        }
                    }
                );

            }
        );

        return laneNos;

    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<Integer> phaseNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        phaseNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());
        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }


        //解析返回数据
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建数据项
        List<Object> phaseParams = new ArrayList<>();

        //是否支持独立相位参数
        AtomicBoolean isEnablePhaseParam = new AtomicBoolean(false);
        P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(signalBrandPort));
        if(sysConfig != null) {;
            isEnablePhaseParam.set(sysConfig.isEnablePhaseParam());
            log.error("信号机{}是否支持独立相位参数{}", requestMessage.getSignalControllerID(), isEnablePhaseParam);
        }

        //是否支持相位
        List<String> notifyNos = new ArrayList<>();
        List<Object> notifyParams = new ArrayList<>();
        if(isEnablePhaseParam.get()) {
            {
                //解析车道数据项
                Map<String, SignalGroupParam> signalGroupParamMap = new ConcurrentHashMap<>();
                Map<String, LampGroupParam> lampGroupParamMap = new ConcurrentHashMap<>();
                List<Integer> phaseNo1049s = new ArrayList<>();
                {
                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                                List<Object> objectList = (List<Object>) (firstMsgData.get());
                                if (objectList == null || objectList.isEmpty()) {
                                    return;
                                }

                                //信号组数据项
                                if ((objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam)) {
                                    List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam> signalGroupParams
                                            = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam>) (firstMsgData.get());
                                    if (signalGroupParams != null && !signalGroupParams.isEmpty()) {
                                        signalGroupParams.stream().forEach(
                                                signalGroupParam -> {
                                                    signalGroupParamMap.put(String.valueOf(signalGroupParam.getSignalGroupNo()), signalGroupParam);
                                                    try {
                                                        if (!phaseNo1049s.contains(signalGroupParam.getSignalGroupNo())) {
                                                            phaseNo1049s.add(signalGroupParam.getSignalGroupNo());
                                                        }
                                                    } catch (Exception e) {
                                                        log.error("相位转换异常{}", signalGroupParam);
                                                    }

                                                    notifyNos.add(String.valueOf(signalGroupParam.getSignalGroupNo()));
                                                    notifyParams.add(signalGroupParam);
                                                }
                                        );

                                        //同步数据存储-批量数据通知
                                        if (!notifyNos.isEmpty()) {
                                            dataInternalNotify.deleteOldInfoSync(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LampGroupParam.class);
                                            dataInternalNotify.datasNotifySync(requestMessage.getSignalControllerID(), subJuncNo,
                                                    notifyNos, notifyParams);
                                        }
                                    }
                                }//灯组数据项
                                else if ((objectList.get(0) instanceof LampGroupParam)) {

                                    notifyNos.clear();
                                    notifyParams.clear();

                                    List<LampGroupParam> lampGroupParams
                                            = (List<LampGroupParam>) firstMsgData.get();
                                    if (lampGroupParams != null && !lampGroupParams.isEmpty()) {
                                        lampGroupParams.forEach(
                                                lampGroupParam -> {
                                                    lampGroupParamMap.put(String.valueOf(lampGroupParam.getLampGroupNo()), lampGroupParam);
                                                    notifyNos.add(String.valueOf(lampGroupParam.getLampGroupNo()));
                                                    notifyParams.add(lampGroupParam);
                                                }
                                        );
                                    }

                                    //同步数据存储-批量数据通知
                                    if (!notifyNos.isEmpty()) {
                                        dataInternalNotify.deleteOldInfoSync(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                                com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LampGroupParam.class);
                                        dataInternalNotify.datasNotifySync(requestMessage.getSignalControllerID(), subJuncNo,
                                                notifyNos, notifyParams);
                                    }

                                }
                            });
                }


                phaseNo1049s.sort(Comparator.comparingInt(Integer::intValue));

                if (!signalGroupParamMap.isEmpty()) {
                    Optional<Crossing> crossingData = crossingService.getCrossingData(crossingInfoOp.get().getCrossingId());
                    if (crossingData.isPresent()) {
                        phaseNo1049s.stream().forEach(
                                phaseNo1049 -> {

                                    log.error("------信号机{}相位{}处理开始-{}", requestMessage.getSignalControllerID(), phaseNo1049, phaseNo1049);
                                    List<Lane> lanes = crossingData.get().getLanes();

                                    SignalGroupParam signalGroupParam
                                            = signalGroupParamMap.get(String.valueOf(phaseNo1049));
                                    if (signalGroupParam == null ||
                                            signalGroupParam.getLampGroupNoList() == null ||
                                            signalGroupParam.getLampGroupNoList().getLampGroupNo() == null ||
                                            signalGroupParam.getLampGroupNoList().getLampGroupNo().isEmpty()) {
                                        return;
                                    }

                                    List<String> lampGroupNos = signalGroupParam.getLampGroupNoList().getLampGroupNo();

                                    //转换为莱斯车道参数
                                    List<Integer> systemLanes = changeToSgpLanes(lampGroupNos, lampGroupParamMap, lanes);

                                    PhaseParam param = PhaseParam.builder()
                                            .signalControllerID(requestMessage.getSignalControllerID())
                                            .noArea(signalInfoOp.isPresent() ? signalInfoOp.get().getNoArea() : 0)
                                            .noJunc(signalInfoOp.isPresent() ? signalInfoOp.get().getNoJunc() : 0)
                                            .phaseNo(phaseNo1049)
                                            .phaseDirection(DefaultPhase.UNKNOWN.getCode())
                                            .phaseName(signalGroupParam.getName())
                                            .minGreen(0)
                                            .maxGreen1(0)
                                            .maxGreen2(0)
                                            .preClearance(0)
                                            .allRed(0)
                                            .walk(0)
                                            .pedestrianClear(0)
                                            .genRed(0)
                                            .genRedFlash(0)
                                            .genRedFastFlash(0)
                                            .redFlash(0)
                                            .redFastFlash(0)
                                            .lampGroupNoList(new ArrayList<>())
                                            .laneNoList(systemLanes)
                                            .laneNos(systemLanes)
                                            .build();
                                    phaseParams.add(param);
                                    log.error("信号机{}读取相位{}", requestMessage.getSignalControllerID(), phaseNo1049);

                                }
                        );
                    }
                }
            }
        }else {
            //生成莱斯默认相位信息返回
            Optional<List<Movement>> crossingMovementOp = crossingService.getCrossingMovementData(crossingInfoOp.get().getCrossingId());
            if (crossingMovementOp.isPresent()) {
                log.error("从sgp读取到路口{}流向数据-{}", crossingInfoOp.get().getCrossingId(),
                        crossingMovementOp.get());
            } else {
                log.error("从sgp读取到路口流向数据失败");
            }

            //获取路口参数数据
            Optional<Crossing> crossingData = crossingService.getCrossingData(crossingInfoOp.get().getCrossingId());

            List<DefaultPhase> defaultPhaseList = DefaultPhase.getDefaultPhaseList();

            defaultPhaseList.stream().forEach(
                    defaultPhase -> {
                        PhaseParam param = PhaseParam.builder()
                                .signalControllerID(requestMessage.getSignalControllerID())
                                .noArea(signalInfoOp.isPresent() ? signalInfoOp.get().getNoArea() : 0)
                                .noJunc(signalInfoOp.isPresent() ? signalInfoOp.get().getNoJunc() : 0)
                                .phaseNo(defaultPhase.getCode())
                                .phaseDirection(defaultPhase.getMovement().getCode())
                                .phaseName(defaultPhase.getDesc())
                                .minGreen(0)
                                .maxGreen1(0)
                                .maxGreen2(0)
                                .preClearance(0)
                                .allRed(0)
                                .walk(0)
                                .pedestrianClear(0)
                                .genRed(0)
                                .genRedFlash(0)
                                .genRedFastFlash(0)
                                .redFlash(0)
                                .redFastFlash(0)
                                .lampGroupNoList(new ArrayList<>())
                                .laneNoList(new ArrayList<>())
                                .build();


                        boolean needResetLanes = true;
                        {
                            //根据相位号查找对应的相位，设置相位关联车道
                            Optional<PhaseParam> phaseParamOldOp = signalCacheService.getData(crossingInfoOp.get().getControllerId(),
                                    param.getPhaseNo(),
                                    PhaseParam.class);
                            if (phaseParamOldOp.isPresent()) {
                                PhaseParam phaseParamOld = phaseParamOldOp.get();
                                if (phaseParamOld != null) {
                                    //增加关联的相位没有发生变化20240923
                                    if (phaseParamOld.getLaneNos() != null) {
                                        //设置阶段数据项
                                        //param.setLaneNoList(phaseParamOld.getLaneNos());

                                        needResetLanes = true;
                                    }
                                }
                            }
                        }

                        {
                            //匹配莱斯车道数据项20240923
                            if (crossingMovementOp.isPresent() && needResetLanes) {
                                try {

                                    if (crossingData.isPresent() && (crossingData.get().getLanes() != null)
                                            && !crossingData.get().getLanes().isEmpty()) {

                                        DirectionType directionType = defaultPhase.getMovement().getDirection();
                                        if (!defaultPhase.getMovement().isPedestrian()) {
                                            LaneMovementType laneMovementType = defaultPhase.getMovement().getMovement();
                                            List<Lane> laneList = crossingData.get().getLanes(directionType, laneMovementType);
                                            if (!CollectionUtils.isEmpty(laneList)) {
                                                List<Integer> laneNos = laneList.stream()
                                                        .map(l -> l.getLaneNo())
                                                        .collect(Collectors.toList());
                                                param.getLaneNoList().addAll(laneNos);
                                            }
                                        } else {
                                            List<Lane> pedLaneList = crossingData.get().getPedLane(directionType);
                                            if (!CollectionUtils.isEmpty(pedLaneList)) {
                                                List<Integer> laneNos_ped = pedLaneList.stream()
                                                        .map(l -> l.getLaneNo())
                                                        .collect(Collectors.toList());
                                                param.getLaneNoList().addAll(laneNos_ped);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("获取默认相位关联车道异常", e);
                                }
                            }
                        }

                        {
                            param.setLaneNos(param.getLaneNoList());
                            param.setLaneNoList(param.getLaneNoList());
                        }

                        phaseParams.add(param);

                    });
        }

        //如果没有查找到对应的数据
        if (phaseParams.isEmpty()) {
            return Optional.empty();
        }

        //删除原先数据项
        try {
            sgpDbSaveEntityProcess.deleteData("phase", requestMessage.getSignalControllerID());
        } catch (Exception e) {
            log.error("删除异常", e);
        }

        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, phaseParams);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        {

            //模拟发送数据，并非真实发送数据
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class)
                    .simuSend(true).build();
            requestMsgs.add(p1049CentralSystemMsg);

        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }


        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<PhaseParam> phaseParams = new ArrayList<>();
        List<Object> phaseParamObjects = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        PhaseParam phaseParam = jsonObject.toJavaObject(PhaseParam.class);
                        phaseParam.setSignalControllerID(requestMessage.getSignalControllerID());
                        phaseParam.setNoArea(signalInfoOp.get().getNoArea());
                        phaseParam.setNoJunc(signalInfoOp.get().getNoJunc());

                        phaseParam.exchange();

                        phaseParams.add(phaseParam);

                        phaseParamObjects.add(JSONObject.toJSON(phaseParam));
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        //设置相位数据项
        requestMessage.setObjectList(phaseParamObjects);

        log.error("信号机{}保存数据项-{}", requestMessage.getSignalControllerID(),
                phaseParams);

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, phaseParamObjects);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
