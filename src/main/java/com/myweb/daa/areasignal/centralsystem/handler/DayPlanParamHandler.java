package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.DayPlanParam;
import com.myweb.daa.areasignal.centralsystem.param.Segment;
import com.myweb.daa.areasignal.centralsystem.service.AcsCharaService;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.utils.Utils;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.Period;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PeriodList;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetDayPlanParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class DayPlanParamHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;

    @Autowired
    private SignalCacheService signalCacheService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private AcsCharaService acsCharaService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return DayPlanParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return DayPlanParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return DayPlanParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {

        log.error("调看信号机{}日计划数据-{}", controllerId, datas);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();

        //批量数据
        if (datas.isEmpty()) {
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);

        } else {
            //请求阶段数据参数
            datas.stream().forEach(
                planNo -> {
                    {
                            TSCCmd tscCmd = TSCCmd.builder()
                                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam.class.getSimpleName())
                                    .ID(crossingInfoOp.get().getCrossingId1049())
                                    .No(String.valueOf(planNo))
                                    .build();
                            List<TSCCmd> tscCmds = new ArrayList<>();
                            tscCmds.add(tscCmd);
                            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Get)
                                    .object(tscCmds)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(TSCCmd.class).build();
                            requestMsgs.add(p1049CentralSystemMsg);
                    }
                }
            );
        }
        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<Integer> dayPlanNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dayPlanNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }
        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());
        final int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        //解析返回数据
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //批量调看数据时，方案编号需要从已有数据中查找
        if (dayPlanNos.isEmpty()) {
            Optional<Map<String, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam>> planParamMapsOp
                    = ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(), subJuncNo, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam.class);
            if (planParamMapsOp.isPresent()) {
                Map<String, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam> stringPlanParamMap = planParamMapsOp.get();
                List<Integer> planNow = stringPlanParamMap.keySet().stream().map(planNo -> Integer.parseInt(planNo)).collect(Collectors.toList());
                dayPlanNos.addAll(planNow);
            } else {
                final int dayPlanNum = 40;
                log.error("没有在内存中找到{}日计划的数据项,使用默认日计划个数-{}", signalInfoOp.get().getSignalId(), dayPlanNum);
                IntStream.rangeClosed(1, dayPlanNum).forEach(
                        dayPlanNo -> dayPlanNos.add(dayPlanNum)
                );
            }
        }

        //按照方案编号进行排序
        dayPlanNos.sort(Comparator.comparingInt(Integer::intValue));

        List<Object> dayPlanParams = new ArrayList<>();
        //待更新的通知数据项
        List<Object> notifyDayPlanParams = new ArrayList<>();
        List<String> notifyNos = new ArrayList<>();
        dayPlanNos.stream().forEach(
                dayPlanNo -> {
                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                                 {

                                    List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam> dayPlanParamList
                                            = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam>) (firstMsgData.get());

                                    if (dayPlanParamList == null || dayPlanParamList.isEmpty()) {
                                        return;
                                    }

                                    Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam> dayPlanParamOptional = dayPlanParamList.stream().filter(
                                            dayPlanParam -> Integer.parseInt(dayPlanParam.getDayPlanNo()) == dayPlanNo
                                    ).findAny();

                                    if (!dayPlanParamOptional.isPresent()) {
                                        log.error("未找到日计划参数-{}", dayPlanParamList);
                                        return;
                                    }

                                    {
                                        com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam dayPlanParamOrg = dayPlanParamOptional.get();

                                        //内部数据项变更通知,存储的数据项是列表
                                        notifyDayPlanParams.add(dayPlanParamOrg);
                                        notifyNos.add(String.valueOf(dayPlanNo));

                                        //日方案数据项
                                        DayPlanParam dayPlanParam = DayPlanParam.builder()
                                                .signalControllerID(requestMessage.getSignalControllerID())
                                                .noArea(signalInfoOp.isPresent() ? signalInfoOp.get().getNoArea() : 0)
                                                .noJunc(signalInfoOp.isPresent() ? signalInfoOp.get().getNoJunc() : 0)
                                                .dayPlanNo(dayPlanNo)
                                                .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                                                .build();
                                        dayPlanParams.add(dayPlanParam);

                                        //组装时段数据项
                                        {
                                            //获取segment数据项
                                            List<Segment> segments = new ArrayList<>();
                                            dayPlanParam.setSegmentList(segments);

                                            PeriodList periodList = dayPlanParamOrg.getPeriodList();
                                            if(periodList != null && periodList.getPeriodList() != null && !periodList.getPeriodList().isEmpty()) {
                                                List<Period> periods = periodList.getPeriodList();
                                                try {
                                                    for (int i = 0; i < periods.size(); i++) {
                                                        Period period = periods.get(i);
                                                        Segment segment = new Segment();
                                                        segments.add(segment);
                                                        segment.setHour(Integer.parseInt(period.getStartTime().split(":")[0]));
                                                        segment.setMinute(Integer.parseInt(period.getStartTime().split(":")[1]));
                                                        segment.setMode(Integer.parseInt(Utils.change2LesMode(crossingInfoOp.get(), period.getCtrlMode())));
                                                        segment.setPlan(Integer.parseInt(period.getPlanNo()));
                                                    }
                                                }catch (Exception e) {
                                                    log.error("时段参数", e);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                    );
                }
        );

        //批量数据通知
        if (!notifyNos.isEmpty()) {
            dataInternalNotify.datasNotify(requestMessage.getSignalControllerID(), subJuncNo,
                    notifyNos, notifyDayPlanParams);
        }

        //如果没有查找到对应的方案数据
        if (dayPlanParams.isEmpty()) {
            return Optional.empty();
        }

        log.error("调看信号机{}日计划-{}，数据返回-{}", crossingInfoOp.get().getControllerId(),
                dayPlanNos,
                dayPlanParams
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, dayPlanParams);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();


        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    DayPlanParam centralDayPlanParam = jsonObject.toJavaObject(DayPlanParam.class);

                    log.error("收到日计划下发-{}", centralDayPlanParam);

                    List<Segment> segmentList = centralDayPlanParam.getSegmentList();
                    if (segmentList == null || segmentList.isEmpty()) {
                        log.error("下发异常的日计划数据-{}", centralDayPlanParam);
                        return;
                    }
                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam dayPlanParam
                            = new com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.DayPlanParam();

                     {
                        PeriodList periodList = new PeriodList();
                        List<Period> periods = new ArrayList<>();
                        periodList.setPeriodList(periods);

                        //设置日计划号
                        dayPlanParam.setDayPlanNo(String.valueOf(centralDayPlanParam.getDayPlanNo()));
                        //设置路口ID
                        dayPlanParam.setCrossID(crossingInfoOp.get().getCrossingId1049());
                        //更新时段数据项
                        dayPlanParam.setPeriodList(periodList);

                        segmentList.forEach(
                                segment -> {
                                    Period period = new Period();
                                    periods.add(period);
                                    period.setStartTime(String.format("%02d", segment.getHour()) + ":" + String.format("%02d", segment.getMinute()));
                                    period.setPlanNo(String.valueOf(segment.getPlan()));
                                    period.setCtrlMode(Utils.change21049Mode(String.valueOf(segment.getMode())));

                                    //特殊控制情况下，设置方案编号为0
                                    {
                                        if (Utils.isSpecialMode(segment.getMode())) {
                                            period.setPlanNo("0");
                                        }
                                    }
                                });


                        //构建数据项
                        SetDayPlanParam setDayPlanParam = SetDayPlanParam.builder()
                                .Oper("2")
                                .DayPlanParam(dayPlanParam).build();

                        {
                            {
                                //原始内存中没有找到这个日计划
                                Optional<com.myweb.daa.areasignal.centralsystem.param.DayPlanParam> optionalDayPlanParam
                                        = signalCacheService.getData(crossingInfoOp.get().getControllerId(),
                                        centralDayPlanParam.getDayPlanNo(),
                                        com.myweb.daa.areasignal.centralsystem.param.DayPlanParam.class);
                                if (!optionalDayPlanParam.isPresent()) {
                                    //新增日计划数据项
                                    setDayPlanParam.setOper("1");
                                }
                            }
                        }

                        List<SetDayPlanParam> setDayPlanParams = new ArrayList<>();
                        setDayPlanParams.add(setDayPlanParam);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(setDayPlanParams)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetDayPlanParam.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    }
                }
        );

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        acsCharaService.addAcsChar(requestMsgs, crossingInfoOp.get(), signalBrandPort);

        log.error("日计划设置发送命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<DayPlanParam> dayPlanParams = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        DayPlanParam dayPlanParam = jsonObject.toJavaObject(DayPlanParam.class);
                        dayPlanParams.add(dayPlanParam);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        AtomicInteger errorCount = new AtomicInteger(0);
        dayPlanParams.stream().forEach(
                dayPlanParam -> {

                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(dayPlanParam.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(dayPlanParam.getSignalControllerID());

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    errorCount.getAndIncrement();
                                    return;
                                }

                                {

                                    List<Object> objectList = (List<Object>) (firstMsgData.get());
                                    if (objectList == null || objectList.isEmpty()) {
                                        return;
                                    }

                                    //如果非阶段数据项
                                    if ((objectList.get(0)
                                            instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetDayPlanParam)) {
                                        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetDayPlanParam> setDayPlanParamList
                                                = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetDayPlanParam>) (firstMsgData.get());
                                        if (setDayPlanParamList == null || setDayPlanParamList.isEmpty()) {
                                            return;
                                        }

                                        setDayPlanParamList.forEach(setDayPlanParam -> {
                                            log.error("加载{}日计划参数返回{}", crossingInfoOp.get().getControllerId(), setDayPlanParam);
                                            //内部数据项变更通知
                                            dataInternalNotify.dataNotify(requestMessage.getSignalControllerID(), subJuncNo,
                                                    String.valueOf(setDayPlanParam.getDayPlanParam().getDayPlanNo()), setDayPlanParam.getDayPlanParam());
                                        });
                                    }
                                }
                            }
                    );
                }
        );

        //存在错误数据，不能进行方案加载
        if (errorCount.get() > 0) {
            log.error("{}方案数据存在异常,加载失败", requestMessage.getSignalControllerID());
            return Optional.empty();
        }

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
