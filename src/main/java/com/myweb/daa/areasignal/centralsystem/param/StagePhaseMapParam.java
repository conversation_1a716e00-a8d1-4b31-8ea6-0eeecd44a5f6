package com.myweb.daa.areasignal.centralsystem.param;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 15:23
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StagePhaseMapParam implements SgpTransAble {

    public static final String MqObjectId = "1000001";

    private String crossingId;

    private List<StageParam> stageParams;

    @Override
    public String getUrl() {
        return "crossing/stageMovement/phase/" + crossingId;
    }

    @Override
    public Object transData() {
        Map<Integer, List<Integer>> stage2PhaseMap = new HashMap<>();
        if (stageParams != null && !stageParams.isEmpty()) {
            stageParams.stream().forEach(
                    stageParam -> {
                        if (stageParam.getPhaseNoList() == null) {
                            return;
                        }
                        stage2PhaseMap.put(stageParam.getStageNo(), stageParam.getPhaseNoList());
                    }
            );
        }
        log.error("准备存储-{}", stage2PhaseMap);
        return stage2PhaseMap;
    }

    @Override
    public String getClazzName() {
        return StagePhaseMapParam.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 0;
    }

    @Override
    public String getSignalId() {
        return crossingId;
    }
}
