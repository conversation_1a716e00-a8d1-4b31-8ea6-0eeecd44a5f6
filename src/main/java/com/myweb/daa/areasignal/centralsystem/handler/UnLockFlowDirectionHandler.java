package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.param.UnLockFlowDirection;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.UnLockFlowDirectionService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 14:40
 */
@Component
@Slf4j
public class UnLockFlowDirectionHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private UnLockFlowDirectionService unLockFlowDirectionService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return UnLockFlowDirection.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return UnLockFlowDirection.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return UnLockFlowDirection.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    UnLockFlowDirection unLockFlowDirection = jsonObject.toJavaObject(UnLockFlowDirection.class);


                    int toUnlockStageNo = unLockFlowDirection.getNoStage();
                    log.error("准备取消指定信号机-{}阶段-{},数据项是-{}", controllerId, unLockFlowDirection.getNoStage(), unLockFlowDirection);

                    String lockStageNo = ht1049SignalCacheService.getLockStageNo(crossingInfoOp.get().getCrossingId());
                    if(!lockStageNo.equalsIgnoreCase(ConstValue.ErrorStageNo)){
                        toUnlockStageNo = Integer.parseInt(lockStageNo);
                        log.error("准备取消指定信号机-{}修复为内存阶段-{}", controllerId, toUnlockStageNo);
                    }

                    {
                        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection> unLockFlowDirections
                                = unLockFlowDirectionService.genUnLockFlowDirectionMsg(crossingInfoOp.get(), toUnlockStageNo);

                             if(!unLockFlowDirections.isEmpty()){

                                P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                        .messageType(MessageType.REQUEST)
                                        .operationName(OperationName.Set)
                                        .object(unLockFlowDirections)
                                        .signalBrandPort(signalBrandPort)
                                        .address(crossingInfoOp.get().getAddress1049Ip())
                                        .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.class).build();
                                requestMsgs.add(p1049CentralSystemMsg);
                            } else {
                                log.error("准备取消指定路口{}阶段-{}转换流向数据异常", crossingInfoOp.get().getCrossingId(), toUnlockStageNo);
                            }
                    }
                }
        );

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送取消锁定阶段设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                objectId -> {
                    JSONObject jsonObject = (JSONObject) objectId;
                    UnLockFlowDirection unLockFlowDirection = jsonObject.toJavaObject(UnLockFlowDirection.class);
                    unLockFlowDirection.setSignalControllerID(requestMessage.getSignalControllerID());
                    unLockFlowDirections.add(unLockFlowDirection);
                }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        AtomicBoolean resultAtomic = new AtomicBoolean(true);
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        unLockFlowDirections.stream().forEach(
                unLockFlowDirection -> {
                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp =
                            crossingService.getCrossingInfo(unLockFlowDirection.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                        itemData -> {
                            BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                            Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                            if (!firstMsgData.isPresent()) {
                                return;
                            }

                            if (firstMsgData.get() instanceof SDO_Error1049) {
                                log.error("路口{}解除锁定异常-{}", crossingInfoOp.get().getCrossingId(), firstMsgData.get());
                                resultAtomic.set(false);
                                return;
                            }

                            ht1049SignalCacheService.rmLockStage(crossingInfoOp.get().getCrossingId());
                            log.error("路口-{}解除锁定成功-应答-{}", crossingInfoOp.get().getCrossingId(),
                                    firstMsgData.get());

                        }
                    );
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());


        //判定是否失败
        if (!resultAtomic.get()) {
            mqMessageResponse = P1049HelpUtils.buildErrorMqResponseMsg(requestMessage, "9001", "信号机响应解锁相位失败",
                    requestMessage.getObjectList());
        }

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        UnLockFlowDirection unLockFlowDirection = jsonObject.toJavaObject(UnLockFlowDirection.class);
                        unLockFlowDirection.setSignalControllerID(requestMessage.getSignalControllerID());

                        //修正子路口数据项
                        if (unLockFlowDirection.getCrossingSeqNo() < 1 || unLockFlowDirection.getCrossingSeqNo() > 4) {
                            unLockFlowDirection.setCrossingSeqNo(1);
                        }

                        unLockFlowDirections.add(unLockFlowDirection);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        unLockFlowDirections.stream().forEach(
                unLockFlowDirection -> {
                    //指定阶段
                    {

                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(unLockFlowDirection.getSignalControllerID())
                                .signalControlerID(requestMessage.getSignalControllerID())
                                .crossingSeqNo(unLockFlowDirection.getCrossingSeqNo())
                                .ack(1)
                                .iden(unLockFlowDirection.getNoStage())
                                .type(5).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }
}
