package com.myweb.daa.areasignal.centralsystem.handler;


import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;

import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
public interface MqMsgBaseHandler {

    /**
     * 获取参数处理key，扩展区分不同厂家
     * @return
     */
    String getMqMsgBaseHandlerKey();


    /**
     * 标记处理厂家
     * @return
     */
    SignalBrandPort getProcessBrand();

    /**
     * 获取处理的objectId对象
     *
     * @return
     */
    String getObjectId();

    /**
     * 数据发送routingKey
     *
     * @return
     */
    String getRoutingKey();

    /**
     * 获取数据对象类型
     *
     * @return
     */
    Class dataType();

    /**
     * 根据信号机编号获取需要发送的数据项
     *
     * @param controllerId
     * @return
     */
    Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas);

    /**
     * 根据返回应答数据项及原始请求报文，生成应答报文
     *
     * @param requestMessage
     * @param invokeFutures
     * @return
     */
    Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures);

    /**
     * 根据信号机编号获取需要发送的数据项
     *
     * @param controllerId
     * @return
     */
    Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas);

    /**
     * 根据返回应答数据项及原始请求报文，生成应答报文
     *
     * @param requestMessage
     * @param invokeFutures
     * @return
     */
    Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures);


    /**
     * 消息是否需要立即应答
     *
     * @param requestMessage
     * @return
     */
    Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack);

    /**
     * 标记配置数据是否需要一起发送
     *
     * @param controllerId
     * @return
     */
    boolean needSendConfigTogether(String controllerId);

}
