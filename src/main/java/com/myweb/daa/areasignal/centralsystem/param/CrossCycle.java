package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.dto.CycleDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 19:06
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossCycle implements RealTimeMsgInterface {
    public static final String MqObjectId = "2004";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int lenCycle;

    private int lastCycleLen;
    private long startTime;
    private int crossingSeqNo;

    @Override
    public String generateDbUrl() {
        return "junccycle";
    }

    @Override
    public String getSgpUrl() {
        return "/cycle/runtime/save";
    }

    @Override
    public Object transPushData() {
        CycleDTO cycleDTO = new CycleDTO();
        cycleDTO.setSignalControllerId(signalControllerID);
        cycleDTO.setCycleLen(lenCycle);
        cycleDTO.setStartTime(LocalDateTime.now());
        cycleDTO.setCrossingSeqNo(crossingSeqNo);
        return cycleDTO;
    }
}
