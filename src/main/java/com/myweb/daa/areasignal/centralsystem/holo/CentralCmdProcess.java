package com.myweb.daa.areasignal.centralsystem.holo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.*;
import com.myweb.daa.areasignal.event.MessagePublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class CentralCmdProcess {

    private static final ConcurrentHashMap<Integer, Class> messageHandleMap = new ConcurrentHashMap<>();

    /**
     * 设置数据报文转换
     */
    static {
        messageHandleMap.put(CentralCmdType.TabCancelGuard.getValue(), JuncModeDto.class);
        messageHandleMap.put(CentralCmdType.TabGuardControl.getValue(), JuncStageDto.class);
        messageHandleMap.put(CentralCmdType.TabHolographicCycle.getValue(), TabHolographicCycleDto.class);
        messageHandleMap.put(CentralCmdType.TabHolographicStage.getValue(), TabHolographicStageDto.class);

        //控制方式同步
        messageHandleMap.put(CentralCmdType.ModeSyncFirstPart.getValue(), ModeSyncCommand.class);
        messageHandleMap.put(CentralCmdType.ModeSyncSecondPart.getValue(), ModeSyncCommand.class);
        messageHandleMap.put(CentralCmdType.ModeSyncThirdPart.getValue(), ModeSyncCommand.class);
    }

    @Autowired
    private MessagePublisher messagePublisher;

    @RabbitListener(queues = {"${global.mq.centralExchange.queue}"})
    public void consumer(String cmd) {
        try {
            JSONObject cmdObject = JSON.parseObject(cmd);

            Integer noTab = cmdObject.getInteger("noTab");
            if (noTab == null) {
                return;
            }

            Class aClass = messageHandleMap.get(noTab);
            if (aClass != null) {
                Object object = JSONObject.parseObject(cmd, aClass);
                messagePublisher.publishMessage(object);
            } else {
                log.trace("收到异常的不支持的控制命令-{}", cmd);
            }
        } catch (Exception e) {
            log.error("解析MQ报文出现异常-{}", e);
        }

    }

}
