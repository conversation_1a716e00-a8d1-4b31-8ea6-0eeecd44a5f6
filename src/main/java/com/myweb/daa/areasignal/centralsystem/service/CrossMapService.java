package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.business.entity.CrossIdMap;
import com.myweb.daa.areasignal.business.entity.QCrossIdMap;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/4/21 11:46
 */
@Service
@Slf4j
public class CrossMapService {


    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private CrossingService crossingService;

    /**
     * @param lesCrossId
     * @param cross1049
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCrossMap(String lesCrossId, String cross1049) {
        //删除原先映射关系
        queryFactory.delete(QCrossIdMap.crossIdMap)
                .where(QCrossIdMap.crossIdMap.crossId1049.eq(cross1049).or(QCrossIdMap.crossIdMap.lesCrossId.eq(lesCrossId))).execute();

        //本地路口绑定关系生成
        CrossIdMap crossIdMap = CrossIdMap.builder().lesCrossId(lesCrossId)
                .crossId1049(cross1049).build();
        CrossIdMap save = repositoryDao.save(crossIdMap, CrossIdMap.class);


    }

}
