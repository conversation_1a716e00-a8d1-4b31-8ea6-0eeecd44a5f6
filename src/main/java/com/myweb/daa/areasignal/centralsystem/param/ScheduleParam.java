package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.Schedule;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 17:20
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScheduleParam implements SgpTransAble {
    public static final String MqObjectId = "1010";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int scheduleNo;
    private int isEnabled;
    private int priority;
    private List<Integer> monthList;
    private List<Integer> weekList;
    private List<Integer> dayList;
    private List<Integer> monthNos;
    private List<Integer> weekNos;
    private List<Integer> dayNos;
    private int dayPlanNo;
    private int crossingSeqNo;

    private int week;
    private long day;
    private int month;

    /**
     * 根据中心机传递的数据项转换到本地使用的monthNos\weekNos\dayNos
     */
    public void exchangeToLocal() {
        monthNos = new ArrayList<>();
        weekNos = new ArrayList<>();
        dayNos = new ArrayList<>();

        if (monthList != null && monthList.size() == 12) {
            for (int i = 0; i < monthList.size(); i++) {
                if (monthList.get(i) == 1) {
                    monthNos.add(i + 1);
                }
            }
        } else {
            log.error("异常的月数据项-{}", monthList);
        }

        if (weekList != null && weekList.size() == 7) {
            for (int i = 0; i < weekList.size(); i++) {
                if (weekList.get(i) == 1) {
                    weekNos.add(i);
                }
            }
        } else {
            log.error("异常的星期数据项-{}", weekList);
        }

        if (dayList != null && dayList.size() == 31) {
            for (int i = 0; i < dayList.size(); i++) {
                if (dayList.get(i) == 1) {
                    dayNos.add(i + 1);
                }
            }
        } else {
            log.error("异常的日数据项-{}", dayList);
        }
    }


    @Override
    public String getUrl() {
        return "/schedule/" + signalControllerID;
    }

    @Override
    public Object transData() {
        Schedule schedule = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, Schedule.class);
        schedule.setEnabled(true);
        return schedule;
    }

    @Override
    public String getClazzName() {
        return ScheduleParam.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return scheduleNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
