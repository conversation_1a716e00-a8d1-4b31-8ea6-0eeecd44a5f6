package com.myweb.daa.areasignal.centralsystem.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/4/26 17:28
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CentralPlanParam {
    public static final String MqObjectId = "3006";

    private String signalControllerID;
    private int mode;
    private List<Integer> stageNoList;
    private List<Integer> greenList;
    private int crossingSeqNo;

    //用于HT临时方案控制
    private int duration;
    private int planNo;
}
