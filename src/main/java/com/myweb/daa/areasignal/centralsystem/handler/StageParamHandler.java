package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSaveBean;
import com.myweb.daa.areasignal.centralsystem.dbsave.SgpDbSaveEntityProcess;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import com.myweb.daa.areasignal.centralsystem.param.StagePhaseMapParam;
import com.myweb.daa.areasignal.centralsystem.param.StagePhaseMapParam1049;
import com.myweb.daa.areasignal.centralsystem.service.AcsCharaService;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.DataLookBackground;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalGroupStatus;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageTiming;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageTimingList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class StageParamHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Autowired
    private AcsCharaService acsCharaService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private SgpDbSaveEntityProcess sgpDbSaveEntityProcess;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return StageParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return StageParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return StageParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {

        log.error("调看信号机{}阶段数据-{}", controllerId, datas);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();

        //批量阶段数据
        if (datas.isEmpty()) {

            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);


            //增加华通后台静默调看数据
            {
                //后台静默调看此路口所有的数据项
                DataLookBackground dataLookBackground = DataLookBackground.builder()
                        .crossId(crossingInfoOp.get().getCrossingId()).build();
                //10-20s之间随机调看所有的数据项
                messagePublisher.publishDelayMessage(P1049HelpUtils.getRandomTime(10, 10), dataLookBackground);
            }

        } else {
            //请求阶段数据参数
            datas.stream().forEach(
                    stageNo -> {
                        {

                            //根据莱斯阶段编号，转换成1049阶段编号
                            Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(controllerId, subJuncNo, String.valueOf(stageNo));
                            if (!p1049StageNoOp.isPresent()) {
                                return;
                            }

                            TSCCmd tscCmd = TSCCmd.builder()
                                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName())
                                    .ID(crossingInfoOp.get().getCrossingId1049())
                                    .No(p1049StageNoOp.get())
                                    .build();
                            List<TSCCmd> tscCmds = new ArrayList<>();
                            tscCmds.add(tscCmd);
                            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Get)
                                    .object(tscCmds)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(TSCCmd.class).build();
                            requestMsgs.add(p1049CentralSystemMsg);
                        }
                    }
            );
        }

        //同时调看方案参数数据项
        {
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);
        }


        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<Integer> stageNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        stageNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());
        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        //是否支持独立相位参数
        AtomicBoolean isEnablePhaseParam = new AtomicBoolean(false);
        P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(signalBrandPort));
        if (sysConfig != null) {
            isEnablePhaseParam.set(sysConfig.isEnablePhaseParam());
            log.error("信号机{}是否支持独立相位参数{}", requestMessage.getSignalControllerID(), isEnablePhaseParam);
        }

        //解析返回数据
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //批量调看数据时，阶段编号需要从当前已经编号的数据中进行查找
        boolean requestAll = false;
        if (stageNos.isEmpty()) {
            requestAll = true;
            Optional<List<Integer>> centralStageNos = ht1049SignalCacheService.getCentralStageNos(requestMessage.getSignalControllerID(), subJuncNo);
            if (centralStageNos.isPresent()) {
                stageNos.addAll(centralStageNos.get());
            }
        }

        List<Object> stageParams = new ArrayList<>();
        List<StageParam> stageParam2PhaseMapList = new ArrayList<>();

        //读取当前内存中阶段参数数据项，用于相位数据是否发生变化处理
        Optional<List<StageParam>> oldStageParams  = signalCacheService.getDatas(crossingInfoOp.get().getControllerId(),
                StageParam.class);

        Optional<Map<String, StageParam>> _oldStageParamMap = Optional.empty();
        if(oldStageParams.isPresent()){
            Map<String, StageParam> stageParamMap = new ConcurrentHashMap<>();
            oldStageParams.get().stream().forEach(
                    stageParam -> {
                        stageParamMap.put(String.valueOf(stageParam.getStageNo()), stageParam);
                    }
            );
            _oldStageParamMap = Optional.of(stageParamMap);
        }

        final Optional<Map<String, StageParam>> oldStageParamMap = _oldStageParamMap;
        Map<Integer, StageTiming> stageTimingMap = new ConcurrentHashMap<>();
        {

            if (requestAll) {
                //调看阶段参数
                //并且同步进行数据存储
                itemDatas.stream().filter(
                        itemData -> itemData.getData() instanceof BaseMessage1049
                ).forEach(
                        itemData -> {
                            BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                            Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                            if (!firstMsgData.isPresent()) {
                                return;
                            }

                            if (firstMsgData.get() instanceof SDO_Error1049) {
                                log.error("请求应答异常-{}", firstMsgData.get());
                                return;
                            }


                            List<Object> objectList = (List<Object>) (firstMsgData.get());
                            if (objectList == null || objectList.isEmpty()) {
                                return;
                            }

                            List<String> notifyNos = new ArrayList<>();
                            List<Object> notifyStageParams = new ArrayList<>();
                            //如果是阶段数据项
                            if ((objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam)) {
                                List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam> stageParamList
                                        = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam>) (firstMsgData.get());
                                stageParamList.stream().forEach(
                                        stageParam -> {
                                            notifyNos.add(stageParam.getStageNo());
                                            notifyStageParams.add(stageParam);
                                        }
                                );

                                //同步数据存储-批量数据通知,必须放在此处，否则会出现获取编码时获取不到转换数据项
                                if (!notifyNos.isEmpty()) {
                                    dataInternalNotify.deleteOldInfoSync(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                            com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class);
                                    dataInternalNotify.datasNotifySync(requestMessage.getSignalControllerID(), subJuncNo,
                                            notifyNos, notifyStageParams);
                                }
                            }
                            //如果是方案参数
                            else if((objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam)){
                                List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> planParamList
                                        = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam>) (firstMsgData.get());
                                planParamList.stream().forEach(
                                    planParam -> {
                                        StageTimingList stageTimingList = planParam.getStageTimingList();
                                        if(stageTimingList != null
                                                && stageTimingList.getStageTiming() != null
                                                && !stageTimingList.getStageTiming().isEmpty()){
                                            List<StageTiming> stageTimings = stageTimingList.getStageTiming();
                                            for (StageTiming stageTiming : stageTimings) {
                                                stageTimingMap.put(Integer.parseInt(stageTiming.getStageNo()), stageTiming);
                                            }
                                        }
                                    }
                                );
                            }
                        });
            }
        }


        //待更新的通知数据项
        List<Object> notifyStageParams = new ArrayList<>();
        List<String> notifyNos = new ArrayList<>();
        stageNos.stream().forEach(
                stageNo -> {

                    //根据莱斯阶段编号，转换成1049阶段编号
                    Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(requestMessage.getSignalControllerID()
                            , subJuncNo, String.valueOf(stageNo));
                    if (!p1049StageNoOp.isPresent()) {
                        return;
                    }

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                                List<Object> objectList = (List<Object>) (firstMsgData.get());
                                if (objectList == null || objectList.isEmpty()) {
                                    return;
                                }

                                //如果非阶段数据项
                                if (!(objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam)) {
                                   return;
                                }


                                List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam> stageParamList
                                        = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam>) (firstMsgData.get());
                                if (stageParamList == null || stageParamList.isEmpty()) {
                                    return;
                                }

                                Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam> returnData = Optional.empty();
                                //返回列表数据项的系统，需要找到对应数据项返回
                                 {
                                    returnData = stageParamList.stream().filter(
                                            stageParam ->
                                                    stageParam.getStageNo().equalsIgnoreCase(p1049StageNoOp.get())

                                    ).findAny();
                                }

                                if (returnData.isPresent()) {
                                    notifyStageParams.add(returnData.get());
                                    notifyNos.add(String.valueOf(p1049StageNoOp.get()));

                                    List<Integer> signalGroupNos = new ArrayList<>();
                                    if (returnData.get().getSignalGroupStatusList() != null
                                            && returnData.get().getSignalGroupStatusList().getSignalGroupStatus() != null
                                            && !returnData.get().getSignalGroupStatusList().getSignalGroupStatus().isEmpty()) {
                                        for (SignalGroupStatus signalGroupStatus : returnData.get().getSignalGroupStatusList().getSignalGroupStatus()) {
                                            if(signalGroupStatus != null && signalGroupStatus.getSignalGroupNo() != null
                                                    && signalGroupStatus.getLampStatus() != null){
                                                try {
                                                    String lampStatus = signalGroupStatus.getLampStatus();
                                                    String greenData = lampStatus.substring(lampStatus.length() - 1);
                                                    log.error("信号机-{}阶段-{}信号组-{}绿灯-{}", requestMessage.getSignalControllerID(),
                                                            stageNo, signalGroupStatus.getSignalGroupNo(), greenData
                                                           );
                                                    if ("2".equalsIgnoreCase(greenData)) {
                                                        signalGroupNos.add(signalGroupStatus.getSignalGroupNo());
                                                    }
                                                }catch (Exception e){
                                                    log.error("转换相位参数异常{}", returnData.get().getSignalGroupStatusList());
                                                }
                                            }
                                        }
                                    }

                                    List<Integer> phaseNos = new ArrayList<>();
                                    if(isEnablePhaseParam.get()){
                                        //是否支持独立阶段参数设置
                                        phaseNos.addAll(signalGroupNos);

                                    }else {
                                        if (returnData.get().getSignalGroupStatusList() != null
                                                && returnData.get().getSignalGroupStatusList().getSignalGroupStatus() != null
                                                && !returnData.get().getSignalGroupStatusList().getSignalGroupStatus().isEmpty()) {
                                            phaseNos = ht1049SignalCacheService.exchange2CentralPhase(signalGroupNos,
                                                    crossingInfoOp.get());
                                        }
                                    }

                                    int yellow = 0;
                                    int allRed = 0;

                                    if(stageTimingMap.containsKey(stageNo)){
                                        try {
                                            StageTiming stageTiming = stageTimingMap.get(stageNo);
                                            if (stageTiming != null) {
                                                yellow = Integer.parseInt(stageTiming.getYellow());
                                                allRed = Integer.parseInt(stageTiming.getAllRed());
                                            }
                                        }catch (Exception e){
                                            log.error("解析异常-", e);
                                        }
                                    }

                                    //生成中心机阶段参数数据项
                                    StageParam stageParamCentral = StageParam.builder()
                                            .signalControllerID(requestMessage.getSignalControllerID())
                                            .stageNo(stageNo)
                                            .noArea(signalInfoOp.get().getNoArea())
                                            .noJunc(signalInfoOp.get().getNoJunc())
                                            .stageName(returnData.get().getStageName() != null
                                                    ? returnData.get().getStageName() : "未知阶段")
                                            .phaseNoList(phaseNos)
                                            .yellow(yellow)
                                            .allRed(allRed)
                                            .maxGreen(80)
                                            .minGreen(12)
                                            .build();

                                    stageParams.add(stageParamCentral);
                                    stageParam2PhaseMapList.add(stageParamCentral);
                                }
                            }
                    );
                }
        );



        //批量数据通知,必须放在此处，否则会出现获取编码时获取不到转换数据项
        if (!notifyNos.isEmpty()) {
            dataInternalNotify.datasNotify(requestMessage.getSignalControllerID(), subJuncNo,
                    notifyNos, notifyStageParams);
        }

        //如果没有查找到对应的方案数据
        if (stageParams.isEmpty()) {
            return Optional.empty();
        }

        log.error("调看信号机{}阶段-{}，数据返回-{}", crossingInfoOp.get().getControllerId(),
                stageNos,
                stageParams
        );

        if (requestAll) {
            //删除原先数据项
            try {
                sgpDbSaveEntityProcess.deleteData("stage", requestMessage.getSignalControllerID());
            } catch (Exception e) {
                log.error("删除stage异常", e);
            }
        }

        //nats3.0调看所有数据返回的时候进行阶段相位数据入库存储
        if (requestAll) {

            if (isEnablePhaseParam.get()) {
                //调用1049特别接口生成阶段关联流向
                StagePhaseMapParam1049 stagePhaseMapParam = StagePhaseMapParam1049.builder()
                        .crossingId(crossingInfoOp.get().getCrossingId())
                        .stageParams(stageParam2PhaseMapList).build();
                List<Object> objects = new ArrayList<>();
                objects.add(stagePhaseMapParam);
                DataSaveBean dataSaveBean = DataSaveBean.builder().objectList(objects).build();
                //通知消息总线进行数据存储
                messagePublisher.publishMessage(dataSaveBean);
            } else {
                //判定这个路口是否手动进行了配置,如果进行了手动配置不进行阶段相位关联关系配置更新，且使用原先的阶段名称
                StagePhaseMapParam stagePhaseMapParam = StagePhaseMapParam.builder()
                        .crossingId(crossingInfoOp.get().getCrossingId())
                        .stageParams(stageParam2PhaseMapList).build();
                List<Object> objects = new ArrayList<>();
                objects.add(stagePhaseMapParam);
                DataSaveBean dataSaveBean = DataSaveBean.builder().objectList(objects).build();
                //通知消息总线进行数据存储
                messagePublisher.publishMessage(dataSaveBean);
            }
        }

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, stageParams);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();

        {

            //模拟发送数据，并非真实发送数据
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class)
                    .simuSend(true).build();
            requestMsgs.add(p1049CentralSystemMsg);

        }

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        acsCharaService.addAcsChar(requestMsgs, crossingInfoOp.get(), signalBrandPort);

        log.error("准备发送阶段设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }


    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<StageParam> stageParams = new ArrayList<>();
        List<Object> stageParamObjects = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        StageParam stageParam = jsonObject.toJavaObject(StageParam.class);
                        stageParam.setSignalControllerID(requestMessage.getSignalControllerID());
                        stageParams.add(stageParam);

                        stageParamObjects.add(JSONObject.toJSON(stageParam));
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(requestMessage.getSignalControllerID(),
                subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        //设置相位数据项
        requestMessage.setObjectList(stageParamObjects);

        //存储阶段关联相位数据项
        {
            if(!stageParams.isEmpty()) {
                Optional<List<CrossingService.CrossingBaseInfo>> crossingInfoOps = crossingService.getCrossingInfos(requestMessage.getSignalControllerID());

                //是否支持独立相位参数
                AtomicBoolean isEnablePhaseParam = new AtomicBoolean(false);
                P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(signalBrandPort));
                if (sysConfig != null) {
                    isEnablePhaseParam.set(sysConfig.isEnablePhaseParam());
                    log.error("信号机{}是否支持独立相位参数{}", requestMessage.getSignalControllerID(), isEnablePhaseParam);
                }

                if(crossingInfoOps.isPresent()){
                    List<CrossingService.CrossingBaseInfo> crossingBaseInfos = crossingInfoOps.get();
                    crossingBaseInfos.forEach(
                            crossingBaseInfo -> {

                                if (isEnablePhaseParam.get()) {

                                    List<StageParam> stageParam2PhaseMapList = stageParams;
                                    StagePhaseMapParam1049 stagePhaseMapParam = StagePhaseMapParam1049.builder()
                                            .crossingId(crossingBaseInfo.getCrossingId())
                                            .stageParams(stageParam2PhaseMapList).build();
                                    List<Object> objects = new ArrayList<>();
                                    objects.add(stagePhaseMapParam);
                                    DataSaveBean dataSaveBean = DataSaveBean.builder().objectList(objects).build();
                                    //通知消息总线进行数据存储
                                    messagePublisher.publishMessage(dataSaveBean);
                                } else {
                                    List<StageParam> stageParam2PhaseMapList = stageParams;
                                    StagePhaseMapParam stagePhaseMapParam = StagePhaseMapParam.builder()
                                            .crossingId(crossingBaseInfo.getCrossingId())
                                            .stageParams(stageParam2PhaseMapList).build();
                                    List<Object> objects = new ArrayList<>();
                                    objects.add(stagePhaseMapParam);
                                    DataSaveBean dataSaveBean = DataSaveBean.builder().objectList(objects).build();
                                    //通知消息总线进行数据存储
                                    messagePublisher.publishMessage(dataSaveBean);
                                }
                            }
                    );
                }
            }
        }

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
