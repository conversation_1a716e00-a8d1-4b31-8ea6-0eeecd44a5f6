package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.param.UnLockFlow;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 14:40
 */
@Component
@Slf4j
public class UnLockFlowHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return UnLockFlow.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return UnLockFlow.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return UnLockFlow.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    UnLockFlow unLockFlow = jsonObject.toJavaObject(UnLockFlow.class);

                    log.error("准备取消指定信号机-{}锁定流向,数据项是-{}", controllerId, unLockFlow);

                    if (signalBrandPort.brandCode() == SignalBrandPort.HK.brandCode()) {
                        //海康
                        com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection unlockFlowDirectionMsg
                                = com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .Type("1")
                                .Entrance("")
                                .Exit("")
                                .build();

                        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
                        unLockFlowDirections.add(unlockFlowDirectionMsg);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(unLockFlowDirections)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    } else{
                        //取消
                        com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection unlockFlowDirectionMsg
                                = com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .Type("1")
                                .Entrance(String.valueOf(0))
                                .Exit(String.valueOf(0))
                                .build();

                        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
                        unLockFlowDirections.add(unlockFlowDirectionMsg);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(unLockFlowDirections)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    }

                }
        );

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送取消锁定流向设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<UnLockFlow> unLockFlows = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        UnLockFlow unLockFlow = jsonObject.toJavaObject(UnLockFlow.class);
                        unLockFlows.add(unLockFlow);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        unLockFlows.stream().forEach(
                unLockFlowDirection -> {
                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(unLockFlowDirection.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(unLockFlowDirection.getSignalControllerID());

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                                log.error("路口-{}解除锁定流向成功", crossingInfoOp.get().getCrossingId());

                            }
                    );
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<UnLockFlow> unLockFlows = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        UnLockFlow unLockFlow = jsonObject.toJavaObject(UnLockFlow.class);
                        unLockFlows.add(unLockFlow);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        unLockFlows.stream().forEach(
                unLockFlow -> {
                    //指定阶段
                    {
                        Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(unLockFlow.getSignalControllerID(),
                                unLockFlow.getCrossingSeqNo());

                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(unLockFlow.getSignalControllerID())
                                .crossingSeqNo(unLockFlow.getCrossingSeqNo())
                                .ack(crossingInfo.isPresent() ? (crossingInfo.get().isOnline() ? 1 : 2) : 2)
                                .iden(0)
                                .type(0)
                                .msg(JSONObject.toJSONString(unLockFlow)).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }
}
