package com.myweb.daa.areasignal.centralsystem.controller;

import com.myweb.commons.persistence.JsonResult;

import com.myweb.daa.areasignal.monitor.ChannelMonitor;
import com.myweb.daa.areasignal.monitor.bean.SocketInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ChannelInfoController
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/15 16:44
 */
@RestController
@RequestMapping("channel")
public class ChannelInfoController {
    @Autowired
    private ChannelMonitor channelMonitor;

    @RequestMapping("info")
    public JsonResult<SocketInfo> info() {
        return new JsonResult<>(true, "0", "请求通道数据正确", channelMonitor.invoke());
    }

}
