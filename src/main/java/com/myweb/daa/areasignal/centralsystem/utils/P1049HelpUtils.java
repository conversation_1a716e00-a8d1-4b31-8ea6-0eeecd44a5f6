package com.myweb.daa.areasignal.centralsystem.utils;

import com.alibaba.fastjson.JSONObject;
import com.les.ads.ds.signal.Phase;
import com.les.ads.ds.signal.Schedule;
import com.les.ads.ds.signal.dto.ControlModeDTO;
import com.les.ads.ds.signal.dto.DeviceStatusDTO;
import com.les.ads.ds.signal.dto.SchedulerTabDTO;
import com.les.ads.ds.signal.dto.StageDTO;
import com.myweb.commons.utils.GUID;
import com.myweb.commons.utils.StringUtils;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgOperator;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgType;
import com.myweb.daa.areasignal.centralsystem.param.*;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.AckManager.NeedAck;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.common.OuterProtocolType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import org.h2.table.Plan;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/2/17 17:09
 */
@Slf4j
public class P1049HelpUtils {

    private P1049HelpUtils() {

    }

    private static AtomicLong atomicLong = new AtomicLong(0);

    /**
     * 重连随机数
     */
    private static Random reconnectRandom = new Random(System.currentTimeMillis());

    @Getter
    private static MapperFactory mapperFactory = new DefaultMapperFactory.Builder()
            .mapNulls(true).build();

    static {
        mapperFactory.classMap(PhaseParam.class, Phase.class)
                .fieldMap("phaseName", "name").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("maxGreen1", "maxGreen").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("allRed", "red").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("pedestrianClear", "pedClear").mapNulls(false).mapNullsInReverse(false).add()
                .byDefault()
                .register();

        mapperFactory.classMap(ScheduleParam.class, Schedule.class)
                .byDefault()
                .register();


        mapperFactory.classMap(CrossStage.class, StageDTO.class)
                .fieldMap("signalControllerID", "signalControllerId").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("noOldStage", "oldStageNo").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("lenOldStage", "oldStageLen").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("noNewStage", "newStageNo").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("lenNewStage", "newStageLen").mapNulls(false).mapNullsInReverse(false).add()
                .byDefault()
                .register();

        mapperFactory.classMap(CrossControlMode.class, ControlModeDTO.class)
                .fieldMap("signalControllerID", "signalControllerId").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("mode", "ctrModeNo").mapNulls(false).mapNullsInReverse(false).add()
                .byDefault()
                .register();

        mapperFactory.classMap(CrossState.class, DeviceStatusDTO.class)
                .fieldMap("signalControllerID", "signalControllerId").mapNulls(false).mapNullsInReverse(false).add()
                .byDefault()
                .register();

        mapperFactory.classMap(CrossPlan.class, SchedulerTabDTO.class)
                .fieldMap("signalControllerID", "signalControllerId").mapNulls(false).mapNullsInReverse(false).add()
                .fieldMap("planNo", "patternNo").mapNulls(false).mapNullsInReverse(false).add()
                .byDefault()
                .register();


        mapperFactory.classMap(Plan.class, Plan.class)
                .byDefault()
                .register();

    }


    /**
     * 构造数据项报文
     *
     * @param interProtocolType
     * @param data
     * @return
     */
    public static InterProtocol buildMessage(InterProtocolType interProtocolType,
                                             Object data, String ip, int port) {
        if (data instanceof NeedAck) {
            NeedAck needAck = (NeedAck) data;
            return buildMessage(interProtocolType, data, true, needAck.getAckKey(), ip, port);
        } else {
            return buildMessage(interProtocolType, data, false, "", ip, port);
        }
    }

    /**
     * @param interProtocolType
     * @param data
     * @param needAck
     * @return
     */
    public static InterProtocol buildMessage(InterProtocolType interProtocolType,
                                             Object data, boolean needAck, String ackKey, String ip, int port) {
        InterProtocol interProtocol = new InterProtocol();
        interProtocol.setInterProtocolType(interProtocolType);

        interProtocol.setNeedAck(needAck);
        interProtocol.setAckKey(ackKey);
        interProtocol.setJsonString(JSONObject.toJSONString(data));
        interProtocol.setOuterUuid(GUID.generate());
        interProtocol.setOuterTimeStamp(System.currentTimeMillis());
        interProtocol.setOuterProtocolType(String.valueOf(OuterProtocolType.P1049_SIGNAL.value()));
        interProtocol.setOuterIp(ip);
        interProtocol.setOuterPort(port);

        int DEFAULT_TIMEOUT_MESSAGE = GlobalConfigure.timeOutSecond;
        int DEFAULT_MAX_TIMEOUT_MESSAGE = GlobalConfigure.timeOutMaxSecond;

        /**判定是否是需要应答类报文*/
        if (interProtocol.isNeedAck()) {
            if (interProtocol.getTimeOutSeconds() <= 0) {
                interProtocol.setTimeOutSeconds(DEFAULT_TIMEOUT_MESSAGE);
            } else if (interProtocol.getTimeOutSeconds() > 30) {
                log.warn("{} 设置的超时时间太长了，设置为默认的最大值{}", interProtocol, DEFAULT_MAX_TIMEOUT_MESSAGE);
                interProtocol.setTimeOutSeconds(DEFAULT_MAX_TIMEOUT_MESSAGE);
            }

            if (StringUtils.isBlank(interProtocol.getAckKey())) {
                log.warn("请注意！报文-{}需要应答，但是没有设置应答关键字,应答可能会出现异常", interProtocol);
            }
        }

        return interProtocol;
    }


    /**
     * 根据原始报文构建成功应答报文
     *
     * @param requestMessage
     * @return
     */
    public static MqMessage buildSuccessMqResponseMsg(MqMessage requestMessage, List<Object> data) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(requestMessage.getObjectId())
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode("0")
                .errorInfo("")
                .objectList(data)
                .build();
        return mqMessageResponse;
    }

    /**
     * 根据原始报文构建错误应答报文
     *
     * @param requestMessage
     * @return
     */
    public static MqMessage buildErrorMqResponseMsg(MqMessage requestMessage, String erroCode, String errorString) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(requestMessage.getObjectId())
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode(erroCode)
                .errorInfo(errorString)
                .objectList(new ArrayList<>())
                .build();
        return mqMessageResponse;
    }

    /**
     * 根据原始报文构建错误应答报文
     *
     * @param requestMessage
     * @return
     */
    public static MqMessage buildErrorMqResponseMsg(MqMessage requestMessage, String erroCode, String errorString, List<Object> data) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(requestMessage.getObjectId())
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode(erroCode)
                .errorInfo(errorString)
                .objectList(data)
                .build();
        return mqMessageResponse;
    }


    /**
     * 根据原始报文构建成功应答报文
     *
     * @param requestMessage
     * @return
     */
    public static MqMessage buildAckMqResponseMsg(MqMessage requestMessage, List<SystemControlAck> systemControlAcks) {
        List<Object> objects = new ArrayList<>();
        systemControlAcks.stream().forEach(
                cmdAck1 -> objects.add(cmdAck1)
        );

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(SystemControlAck.MqObjectId)
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode("0")
                .errorInfo("")
                .objectList(objects)
                .build();
        return mqMessageResponse;
    }

    /**
     * 构建推送报文
     *
     * @param data
     * @return
     */
    public static MqMessage buildPushMqMsg(String signalControlId, String objectId, List<Object> data) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Push.value())
                .operator(3)
                .sequenceCode(0)
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(data)
                .build();
        return mqMessageResponse;
    }


    /**
     * 移除最高位还原原始数据项
     *
     * @param bytesBody
     */
    public static void rmHighestBit(byte[] bytesBody) {
        if (bytesBody == null) {
            return;
        }

        //去除所有头部的标志位1
        for (int i = 0; i < bytesBody.length; i++) {
            bytesBody[i] = (byte) (bytesBody[i] & 0x7f);
        }
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @param objects
     * @return
     */
    public static MqMessage buildSimuQueryMqMsg(String signalControlId, String objectId, List<Object> objects) {
        //将object原始对象，转换为jsonobject对象
        List<Object> objectsJson = objects.stream().map(object -> JSONObject.toJSON(object)).collect(Collectors.toList());

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Query.value())
                .sequenceCode(atomicLong.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(objectsJson)
                .simu(true)
                .build();
        return mqMessageResponse;
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @param objects
     * @return
     */
    public static MqMessage buildSimuSetMqMsg(String signalControlId, String objectId, List<Object> objects) {

        //将object原始对象，转换为jsonobject对象
        List<Object> objectsJson = objects.stream().map(object -> JSONObject.toJSON(object)).collect(Collectors.toList());

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Set.value())
                .sequenceCode(atomicLong.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(objectsJson)
                .simu(true)
                .build();
        return mqMessageResponse;
    }


    /**
     * 防止出现大规模重连
     * 重连间隔在begin - begin+count 随机
     *
     * @return
     */
    public static int getRandomTime(int begin, int count) {
        return (begin + reconnectRandom.nextInt(count)) * 1000;
    }

}
