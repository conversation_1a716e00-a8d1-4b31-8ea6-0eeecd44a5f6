package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.CentralPlanParam;
import com.myweb.daa.areasignal.centralsystem.param.ManualControl;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanDataService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageCtrlList;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageOptList;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 15:01
 */
@Component
@Slf4j
public class CentralPlanParamlHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Autowired
    private TmpPlanService tmpPlanService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return CentralPlanParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return ManualControl.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return ManualControl.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }
        String crossingId = crossingInfoOp.get().getCrossingId();

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    CentralPlanParam centralPlanParam = jsonObject.toJavaObject(CentralPlanParam.class);

                    log.error("收到系统控制方案模式-{}-{}", centralPlanParam.getMode(), centralPlanParam);

                    List<Integer> stageNoList = centralPlanParam.getStageNoList();
                    List<Integer> greenList = centralPlanParam.getGreenList();
                    if ((stageNoList == null) || (greenList == null)
                            || stageNoList.isEmpty() || greenList.isEmpty()
                            || (stageNoList.size() != greenList.size())) {
                        log.error("系统中心方案参数异常-{}", centralPlanParam);
                        return;
                    }

                    //将内部阶段编号转化为1049阶段编号
                    List<String> stageNo1049s = new ArrayList<>();
                    for (int i = 0; i < stageNoList.size(); i++) {
                        //根据莱斯阶段编号，转换成1049阶段编号
                        Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(controllerId, subJuncNo,
                                String.valueOf(stageNoList.get(i)));
                        if (!p1049StageNoOp.isPresent()) {
                            log.error("{}未找到1049阶段编号-中心机阶段编号{}", controllerId, stageNoList.get(i));
                            return;
                        } else {
                            stageNo1049s.add(p1049StageNoOp.get());
                        }
                    }

                    {

                        List<StageOptList> stageOptList = new ArrayList<>();
                        //构建每一个阶段的数据项
                        for (int i = 0; i < stageNo1049s.size(); i++) {
                            stageOptList.add(StageOptList.builder()
                                    .StageNo(stageNo1049s.get(i))
                                    .Green(greenList.get(i)).build());
                        }

                        //数据项周期优化控制命令
                        StageCtrlList stageCtrlList = StageCtrlList.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .StageOptList(stageOptList).build();

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(stageCtrlList)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(StageCtrlList.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);

                    }
                }
        );

        log.error("准备发送数据-{}", requestMsgs);

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<CentralPlanParam> centralPlanParams = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        CentralPlanParam centralPlanParam = jsonObject.toJavaObject(CentralPlanParam.class);
                        centralPlanParams.add(centralPlanParam);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }


        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        log.error("收到优化应答-{}", itemDatas);
        centralPlanParams.stream().forEach(
                centralPlanParam -> {

                    //系统方案
                    int subJuncNo = 1;
                    String signalControllerID = centralPlanParam.getSignalControllerID();
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalControllerID,
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalControllerID);

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                            });

                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
