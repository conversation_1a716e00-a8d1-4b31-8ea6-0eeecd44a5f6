package com.myweb.daa.areasignal.centralsystem.param.v2;


import com.les.ads.ds.enums.LaneAttributeType;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description:
 * @date: 2024/7/20
 * @version: 1.0
 * @modifyHistory:
 */
public enum LaneAttributeV2 {

    /**
     * 路口进口
     */
    ENTER_PORT(0),

    /**
     * 路口出口
     */
    EXIT_PORT(1),

    /**
     * 匝道
     */
    RAMP(2),

    /**
     * 路段车道
     */
    ROAD_SEGMENT(3),

    /**
     * 其他
     */
    OTHER(9);

    private final int code;

    LaneAttributeV2(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static LaneAttributeV2 trans(LaneAttributeType attributeLes) {
        LaneAttributeV2 e = LaneAttributeV2.OTHER;
        if (attributeLes != null) {
            int codeLes = attributeLes.getCode();
            switch (codeLes) {
                case 1:
                    e = ENTER_PORT;
                    break;
                case 2:
                    e = EXIT_PORT;
                    break;
                default:
                    break;
            }
        }
        return e;
    }
}
