package com.myweb.daa.areasignal.centralsystem.mq;


import com.myweb.daa.areasignal.centralsystem.handler.MqMsgBaseHandler;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 10:40
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MqProcessingMsg {
    /**
     * mq请求包
     */
    private MqMessage mqMessage;

    /**
     * mq处理对象类
     */
    private MqMsgBaseHandler mqMsgBaseHandler;

    /**
     * 请求等待应答的数据项
     */
    List<InvokeFuture> invokeFutures;

    /**
     * 报文处理时间
     */
    private LocalDateTime localDateTime;

    /**
     * 判定是否已经有线程对数据进行处理
     */
    private AtomicBoolean processed;

    /**
     * 发送的数据项
     */
    private List<P1049CentralSystemMsg> messageBaseList;

    /**
     * 获取是否已经准备完毕
     *
     * @return
     */
    public boolean isReady() {
        Optional<InvokeFuture> invokeFuture = invokeFutures.stream().filter(
                future -> !future.isDone()
        ).findAny();
        if (invokeFuture.isPresent()) {
            return false;
        } else {
            return true;
        }
    }
}
