package com.myweb.daa.areasignal.centralsystem.param.v2;


import com.les.ads.ds.enums.DirectionType;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description:
 * @date: 2024/7/20
 * @version: 1.0
 * @modifyHistory:
 */
public enum DirectionType39900 {

    NORTH(1, "北"),
    NORTHEAST(2, "东北"),
    EAST(3, "东"),
    SOUTHEAST(4, "东南"),
    SOUTH(5, "南"),
    SOUTHWEST(6, "西南"),
    WEST(7, "西"),
    NORTHWEST(8, "西北"),
    UNKNOWN(9, "未知");

    private final int code;

    private final String desc;

    DirectionType39900(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean contains(Integer code) {
        if (code != null) {
            for (DirectionType39900 dir : DirectionType39900.values()) {
                if (code.equals(dir.getCode()) && UNKNOWN != dir) {
                    return true;
                }
            }
        }
        return false;
    }

    public static DirectionType39900 parseCode(Integer code) {
        if (code != null) {
            for (DirectionType39900 dir : DirectionType39900.values()) {
                if (code.equals(dir.getCode())) {
                    return dir;
                }
            }
        }
        return DirectionType39900.UNKNOWN;
    }

    public static DirectionType39900 trans(DirectionType directionLes) {
        DirectionType39900 dir = DirectionType39900.UNKNOWN;
        if (directionLes != null) {
            int codeLes = directionLes.getCode();
            dir = trans(codeLes);
        }
        return dir;
    }

    public static DirectionType39900 trans(Integer directionLes) {
        DirectionType39900 dir = DirectionType39900.UNKNOWN;
        if (directionLes != null) {
            switch (directionLes) {
                case 0:
                    dir = NORTH;
                    break;
                case 1:
                    dir = EAST;
                    break;
                case 2:
                    dir = SOUTH;
                    break;
                case 3:
                    dir = WEST;
                    break;
                case 4:
                    dir = NORTHEAST;
                    break;
                case 5:
                    dir = SOUTHEAST;
                    break;
                case 6:
                    dir = SOUTHWEST;
                    break;
                case 7:
                    dir = NORTHWEST;
                    break;
                default:
                    break;
            }
        }
        return dir;
    }

    public static DirectionType trans2Les(Integer code1049) {
        DirectionType dirLes = DirectionType.UNKNOWN;
        if (code1049 != null) {
            switch (code1049) {
                case 1:
                    dirLes = DirectionType.NORTH;
                    break;
                case 2:
                    dirLes = DirectionType.NORTHEAST;
                    break;
                case 3:
                    dirLes = DirectionType.EAST;
                    break;
                case 4:
                    dirLes = DirectionType.SOUTHEAST;
                    break;
                case 5:
                    dirLes = DirectionType.SOUTH;
                    break;
                case 6:
                    dirLes = DirectionType.SOUTHWEST;
                    break;
                case 7:
                    dirLes = DirectionType.WEST;
                    break;
                case 8:
                    dirLes = DirectionType.NORTHWEST;
                    break;
                default:
                    break;
            }
        }
        return dirLes;
    }
}
