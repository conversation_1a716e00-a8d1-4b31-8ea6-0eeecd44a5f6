package com.myweb.daa.areasignal.centralsystem.holo.process;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.JuncStageDto;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class JuncStageDtoProcess {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private HtSignalController htSignalController;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void juncModeDtoProcess(JuncStageDto juncStageDto) {
        log.info("收到全息指定阶段-{}", juncStageDto);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(juncStageDto.getNoArea(),
                juncStageDto.getNoJunc());

        if (!signalInfoOp.isPresent()) {
            log.error("没有找到信号机进行全息指定阶段-{}", juncStageDto);
            return;
        }
        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            log.error("没有找到路口进行全息指定阶段-{}", juncStageDto);
            return;
        }


        //根据莱斯阶段编号，转换成1049阶段编号
        Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(signalInfoOp.get().getSignalId(),
                subJuncNo, String.valueOf(juncStageDto.getNoStage()));
        if (!p1049StageNoOp.isPresent()) {
            log.warn("{}未找到1049阶段编号-中心机阶段编号{}", signalInfoOp.get().getSignalId(), juncStageDto.getNoStage());
            return;
        }


        {
            //构建数据项
            JsonResult jsonResult = htSignalController.lockFlow(crossingInfoOp.get().getCrossingId(), subJuncNo, Integer.parseInt(p1049StageNoOp.get()),
                    juncStageDto.getLenStage(), true);
            log.info("锁定阶段结果-{}，原始数据", jsonResult, juncStageDto);
        }
    }
}
