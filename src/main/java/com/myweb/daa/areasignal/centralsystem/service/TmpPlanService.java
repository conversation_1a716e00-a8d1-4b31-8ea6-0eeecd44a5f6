package com.myweb.daa.areasignal.centralsystem.service;


import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.param.PlanCmdCancel;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossControlModePublish;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 用于控制路口临时方案
 * --> 20241115 当前针对太仓华通信号机，进行临时方案时间控制，接口不支持只能系统进行主动自动恢复
 * -->          同时也要考虑其他指定相位、指定控制方式是否相互影响的问题
 */
@Service
@Slf4j
public class TmpPlanService {


    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private CrossingService crossingService;


    @Autowired
    private CrossControlModePublish crossControlModePublish;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;



    /**
     * 临时方案控制成功
     *
     * @param controllerId
     * @param duration
     */
    public void startTmpPlan(String controllerId, String crossId, int planNo, int duration, int mode) {

        //主动取消事件
        PlanCmdCancelEvent planCmdCancelEvent = PlanCmdCancelEvent.builder()
                .controllerId(controllerId)
                .crossId(crossId)
                .enable(new AtomicBoolean(true))
                .startTime(LocalDateTime.now())
                .planNo(planNo)
                .duration(duration).build();


        TmpPlanData tmpPlanData = tmpPlanDataService.getTmpPlanDataMap().get(crossId);
        if (tmpPlanData == null) {
            //这是首次指定临时方案
            tmpPlanData = new TmpPlanData();
            tmpPlanData.setControllerId(controllerId);
            tmpPlanData.setCrossId(crossId);
            tmpPlanData.setPlanCmdCancelEvent(planCmdCancelEvent);
            tmpPlanData.setStart(true);
            tmpPlanData.setMode(mode);

            //内存存储
            tmpPlanDataService.getTmpPlanDataMap().put(crossId, tmpPlanData);
        } else {


            tmpPlanData.setStart(true);
            tmpPlanData.setMode(mode);

            //取消原先的自动解除锁定
            PlanCmdCancelEvent planCmdCancelEventOld = tmpPlanData.getPlanCmdCancelEvent();
            if ((planCmdCancelEventOld != null) && (planCmdCancelEventOld.getEnable() != null)) {
                planCmdCancelEventOld.getEnable().set(false);
            }
            //设置新的自动解除锁定
            tmpPlanData.setPlanCmdCancelEvent(planCmdCancelEvent);
        }

        //准备自动解除锁定
        if (0 != duration) {
            log.error("信号机{}在{}秒后主动取消临时方案", controllerId, duration);
            messagePublisher.publishDelayMessage(duration * 1000, planCmdCancelEvent);
        }

    }

    /**
     * 主动取消临时方案控制
     *
     * @param crossId
     */
    public void stopTmpPlan(String crossId) {

        TmpPlanData tmpPlanData = tmpPlanDataService.getTmpPlanDataMap().get(crossId);
        if (tmpPlanData == null) {
            log.error("主动取消路口{}临时方案时，没有找到临时方案控制数据", crossId);
        } else {

            tmpPlanData.setStart(false);
            PlanCmdCancelEvent planCmdCancelEvent = tmpPlanData.getPlanCmdCancelEvent();
            if ((planCmdCancelEvent != null) && (planCmdCancelEvent.getEnable() != null)) {
                planCmdCancelEvent.getEnable().set(false);
            }
            log.error("主动取消路口{}临时方案", crossId);
        }
    }

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processTmpPlan(PlanCmdCancelEvent planCmdCancelEvent) {
        if (planCmdCancelEvent.getEnable() != null && planCmdCancelEvent.getEnable().get()) {
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(planCmdCancelEvent.getCrossId());
            if (!crossingInfoOp.isPresent()) {
                log.error("{}", "主动取消临时方案时,没有找到路口对应的路口" + planCmdCancelEvent.getCrossId());
                return;
            }

            Optional<Integer> currentLesControlMode = crossControlModePublish.getCurrentLesControlMode(crossingInfoOp.get().getCrossingId());
            if (!currentLesControlMode.isPresent()) {
                log.error("路口{}主动取消临时方案时，没有找到路口对应的控制方式", crossingInfoOp.get().getCrossingId());
                return;
            }

            if (LesControlMode.TEMPORARY_PATTERN.value() != currentLesControlMode.get().intValue()) {
                log.error("路口{}主动取消临时方案时,路口当前控制方式不是临时方案,而是{}",
                        crossingInfoOp.get().getCrossingId(), currentLesControlMode.get());
                return;
            }

            List<Object> objectList = new ArrayList<>();
            PlanCmdCancel planCmdCancel = PlanCmdCancel.builder()
                    .signalControllerID(crossingInfoOp.get().getControllerId())
                    .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                    .planNo(planCmdCancelEvent.getPlanNo())
                    .build();
            objectList.add(planCmdCancel);

            MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                    PlanCmdCancel.MqObjectId, objectList);
            messagePublisher.publishMessage(mqMessage);
            log.error("主动取消临时方案,发送信号机{}-命令-{}", crossingInfoOp.get().getControllerId(), planCmdCancel);
        }

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TmpPlanData {

        /**
         * 控制的路口ID
         */
        private String crossId;

        /**
         * 信号机id
         */
        private String controllerId;


        /**
         * 取消事件
         */
        private PlanCmdCancelEvent planCmdCancelEvent;

        /**
         * 当前临时方案控制的模式
         */
        private int mode;

        /**
         * 标记当前是否启动临时方案
         */
        private boolean start;
    }

    /**
     * 解锁临时方案事件
     */
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class PlanCmdCancelEvent {
        /**
         * 控制的路口ID
         */
        private String crossId;

        /**
         * 信号机id
         */
        private String controllerId;

        /**
         * 控制开始的时间
         */
        private LocalDateTime startTime;

        /**
         * 控制持续的时间
         */
        private int duration;

        /**
         * 锁定的方案编号
         */
        private int planNo;


        private AtomicBoolean enable;
    }

}
