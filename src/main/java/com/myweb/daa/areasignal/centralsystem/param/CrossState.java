package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.dto.DeviceStatusDTO;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 19:06
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossState implements RealTimeMsgInterface {
    public static final String MqObjectId = "2001";

    public static final int ON_LINE = 1;
    public static final int OFF_LINE = 2;

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int juncStatus;

    @Override
    public String generateDbUrl() {
        return "";
    }

    @Override
    public String getSgpUrl() {
        return "/deviceStatus/runtime/save";
    }

    @Override
    public Object transPushData() {
        DeviceStatusDTO deviceStatusDTO = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, DeviceStatusDTO.class);
        deviceStatusDTO.setDeviceStatus(juncStatus == 1 ? 1 : 0);
        return deviceStatusDTO;
    }
}
