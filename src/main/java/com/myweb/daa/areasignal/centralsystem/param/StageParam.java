package com.myweb.daa.areasignal.centralsystem.param;


import com.les.ads.ds.signal.Stage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 15:23
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StageParam implements SgpTransAble {
    public static final String MqObjectId = "1007";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int stageNo;

    private String stageName;
    private int feature;
    private int minGreen;
    private int maxGreen;
    private int yellow;
    private int allRed;
    private int pGreenFlash;
    private int vGreenFlash;
    private int pRedFlash;
    private int vRedFlash;
    private List<Integer> phaseNoList;
    private List<Integer> phaseLaterStart;
    private List<Integer> phaseEarlyStop;

    @Override
    public String getUrl() {
        return "stage/single/" + noArea + "/" + noJunc;
    }

    @Override
    public Object transData() {
        Stage stage = new Stage();
        stage.setName(stageName);
        stage.setStageNo(stageNo);
        stage.setPhaseNos(phaseNoList);
        stage.setYellow(yellow);
        stage.setRed(allRed);
        stage.setPedGreenFlash(pGreenFlash);
        stage.setVehGreenFlash(vGreenFlash);
        stage.setVehRedFlash(vRedFlash);
        stage.setPedRedFlash(pRedFlash);
        stage.setMaxGreen(maxGreen);
        stage.setMinGreen(minGreen);
        stage.setPhaseEarlyStopStr(phaseEarlyStop);
        stage.setPhaseLaterStartStr(phaseLaterStart);
        return stage;
    }

    @Override
    public String getClazzName() {
        return StageParam.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return stageNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
