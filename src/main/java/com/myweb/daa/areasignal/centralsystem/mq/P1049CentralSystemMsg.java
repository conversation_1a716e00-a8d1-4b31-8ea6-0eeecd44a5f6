package com.myweb.daa.areasignal.centralsystem.mq;

import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description: 封装的用于统一信控平台数据请求的接口数据项
 * @Date: create in 2022/3/31 13:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class P1049CentralSystemMsg {
    private MessageType messageType;
    private OperationName operationName;
    private Object object;
    private SignalBrandPort signalBrandPort;
    private Class clazz;
    private String address;

    /**
     * 标记是否真的发送数据项
     * @return
     */
    private boolean simuSend;
}
