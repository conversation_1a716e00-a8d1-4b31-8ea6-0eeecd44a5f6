package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.daa.areasignal.centralsystem.param.LockFlowDirection;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossCtrlInfoPublish;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class LockStageExchangeService {


    @Autowired
    private CrossCtrlInfoPublish crossCtrlInfoPublish;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    /**
     * //校验信号机当前方案里面有没有这个阶段，
     * // 如果有，直接下发
     * // 如果没有，需要从当前运行的方案阶段种匹配放行
     * // 相位最接近的阶段
     *
     * @param toLockStageNo1049
     * @param crossingBaseInfo
     * @param lockFlowDirection
     * @return
     */
    public Optional<String> changeLockStage(String toLockStageNo1049,
                                            CrossingService.CrossingBaseInfo crossingBaseInfo,
                                            LockFlowDirection lockFlowDirection) {

        String toLockStageNo = toLockStageNo1049;
        Optional<StageParam> stageParamOp = ht1049SignalCacheService.getData(crossingBaseInfo.getCrossingId(),
                crossingBaseInfo.getSubJuncNo(),
                toLockStageNo, StageParam.class);
        if (!stageParamOp.isPresent()) {
            log.error("路口-{}-待锁定阶段-{}数据未能查询到", crossingBaseInfo.getCrossingId(), toLockStageNo);
            return Optional.empty();
        }

        return Optional.empty();
    }


}
