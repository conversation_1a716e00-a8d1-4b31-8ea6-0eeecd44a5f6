package com.myweb.daa.areasignal.centralsystem.param;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/5/2 14:14
 */

public enum LaneMovementTypeSort {
    /**
     * 枚举字段名请谨慎改动
     * 流向默认初始化依赖'RF'和分隔符'_'
     *
     * <AUTHOR>
     */
    RF_STRAIGHT(11, "直行", 4),
    RF_LEFT(12, "左转", 0),
    RF_RIGHT(13, "右转", 9),
    RF_STRAIGHT_LEFT(21, "直左混行", 1),
    RF_STRAIGHT_RIGHT(22, "直右混行", 5),
    RF_LEFT_RIGHT(23, "左右混行", 6),
    RF_STRAIGHT_LEFT_RIGHT(24, "直左右混行", 2),
    RF_TURNROUND(31, "掉头", -1),
    RF_LEFT_TURNROUND(32, "左转掉头混行", 3),
    RF_STRAIGHT_TURNROUND(33, "直行掉头混行", 7),
    RF_RIGHT_TURNROUND(34, "右转掉头混行", 8),
    RF_VARIABLE(41, "可变车道", 99),
    RF_TIDAL(42, "潮汐机动车道", 99),
    RF_OTHER(99, "其他", 99);

    private final int code;
    private final String description;

    private final int sort;

    LaneMovementTypeSort(int code, String description, int sort) {
        this.code = code;
        this.description = description;
        this.sort = sort;
    }

    public int sort() {
        return this.sort;
    }

    public static LaneMovementTypeSort parseCode(Integer laneMovementType) {
        if (laneMovementType != null) {
            for (LaneMovementTypeSort type : LaneMovementTypeSort.values()) {
                if (laneMovementType.equals(type.code)) {
                    return type;
                }
            }
        }
        return LaneMovementTypeSort.RF_OTHER;
    }
}
