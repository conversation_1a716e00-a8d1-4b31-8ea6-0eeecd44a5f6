package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.dto.SchedulerTabDTO;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 19:09
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossPlan implements RealTimeMsgInterface {
    public static final String MqObjectId = "2007";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int planNo;
    private Integer dayPlanNo;
    private Integer scheduleNo;
    private int crossingSeqNo;

    @Override
    public String generateDbUrl() {
        return "juncpattern";
    }

    @Override
    public String getSgpUrl() {
        return "/scheduler/runtime/save";
    }

    @Override
    public Object transPushData() {
        SchedulerTabDTO schedulerTabDTO = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, SchedulerTabDTO.class);
        return schedulerTabDTO;
    }
}
