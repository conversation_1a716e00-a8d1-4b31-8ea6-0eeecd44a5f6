package com.myweb.daa.areasignal.centralsystem.service;

import com.les.ads.ds.ReturnEntity;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.business.entity.Cross1049;
import com.myweb.daa.areasignal.business.entity.QCross1049;
import com.myweb.daa.areasignal.centralsystem.dto.Cross1049Dto;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/2/9 14:45
 */
@Service
@Slf4j
public class Cross1049Service {

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;


    /**
     * 读取莱斯信号机数据项
     *
     * @return
     */
    @GetMapping("/ext/cross1049")
    public ReturnEntity<?> getCross1049(@RequestParam(required = false) String crossName) {
        List<Cross1049> cross1049s;

        if (crossName != null && !crossName.isEmpty()) {
            cross1049s = queryFactory.selectFrom(QCross1049.cross10491)
                    .where(QCross1049.cross10491.name.contains(crossName)).fetch();
        } else {
            cross1049s = queryFactory.selectFrom(QCross1049.cross10491)
                    .fetch();
        }

        return new ReturnEntity<>(true, cross1049s);
    }


    /**
     * 存储信号机映射数据项
     *
     * @return
     */
    @PostMapping("/ext/cross1049")
    @Transactional
    public ReturnEntity<?> saveCross1049(@RequestBody Cross1049Dto cross1049Dto) {

        if (cross1049Dto == null || cross1049Dto.getCross1049() == null || cross1049Dto.getName() == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        long count = queryFactory.selectFrom(QCross1049.cross10491)
                .where(QCross1049.cross10491.cross1049.eq(cross1049Dto.getCross1049())).fetchCount();
        if (count > 0) {
            return new ReturnEntity<>(false, "10001", "1049路口已经配置，请检查!", "");
        }

        Cross1049 cross1049 = Cross1049.builder().cross1049(cross1049Dto.getCross1049())
                .name(cross1049Dto.getName()).build();
        Cross1049 save = repositoryDao.save(cross1049, Cross1049.class);

        //设置数据项
        return new ReturnEntity<>(true, save);
    }

    /**
     * 删除信号机映射数据项
     *
     * @return
     */
    @DeleteMapping("/ext/cross1049")
    @Transactional
    public ReturnEntity<?> delCross1049(@RequestParam String id) {

        if (id == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        queryFactory.delete(QCross1049.cross10491)
                .where(QCross1049.cross10491.id.eq(id)).execute();
        return new ReturnEntity<>(true, "删除成功");
    }

}
