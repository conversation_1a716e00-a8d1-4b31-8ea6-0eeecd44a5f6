package com.myweb.daa.areasignal.centralsystem.holo;

public enum CentralCmdType {

    TabCancelGuard(21, "指定控制方式"),
    TabGuardControl(20, "指定相位"),
    TabHolographicCycle(49, "全息周期优化"),
    TabHolographicStage(50, "全息相位优化"),
    ModeSyncFirstPart(45, "控制方式检验表-前125个路口"),
    ModeSyncSecondPart(46, "控制方式检验表-后125个路口"),
    ModeSyncThirdPart(146, "控制方式检验表-后250个路口");

    private int value;
    private String des;

    CentralCmdType(int value, String des) {
        this.value = value;
        this.des = des;
    }

    public int getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}
