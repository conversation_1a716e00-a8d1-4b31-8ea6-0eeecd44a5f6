package com.myweb.daa.areasignal.centralsystem.controller;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: UserController
 * @Description: 用于信号控制
 * @Author: king
 * @CreateDate: 2019/5/14 18:29
 */
@RestController
@RequestMapping("user")
public class UserController {

    public static final String ADMIN_TOKEN = "ADMIN_TOKEN";
    public static final String EDITOR_TOKEN = "EDITOR_TOKEN";
    public static final Map<String, SimmUser> userMap = new ConcurrentHashMap<>();

    static {
        List<String> roles = new ArrayList<>();
        roles.add("admin");
        SimmUser adminUser = SimmUser.builder()
                .roles(roles)
                .introduction("I am a super administrator")
                .avatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif")
                .name("supper admin").build();
        userMap.put(ADMIN_TOKEN, adminUser);

        List<String> roles2 = new ArrayList<>();
        roles2.add("editor");
        SimmUser editorUser = SimmUser.builder()
                .roles(roles2)
                .introduction("I am a editor")
                .avatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif")
                .name("editor").build();
        userMap.put(EDITOR_TOKEN, editorUser);
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SimmUser {
        private List<String> roles;
        private String introduction;
        private String avatar;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Token {
        private String token;
    }

    @PostMapping("login")
    public JsonResult login(@RequestBody String body) {
        String username = JSONObject.parseObject(body).get("username").toString();
        String token = username.equalsIgnoreCase("admin") ?
                ADMIN_TOKEN : EDITOR_TOKEN;
        return new JsonResult<>(true, "20000", "登录成功", Token.builder().token(token).build());
    }

    @GetMapping("info")
    public JsonResult info(@RequestHeader("X-Token") String token) {
        SimmUser simmUser = userMap.get(EDITOR_TOKEN);
        if (userMap.containsKey(token)) {
            simmUser = userMap.get(token);
        }
        return new JsonResult<>(true, "20000", "查询成功", simmUser);
    }

    @PostMapping("logout")
    public JsonResult logout() {
        return new JsonResult<>(true, "20000", "退出成功", "admin-token");
    }
}
