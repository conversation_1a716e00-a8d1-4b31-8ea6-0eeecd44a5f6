package com.myweb.daa.areasignal.centralsystem.param;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/21 14:10
 */
public enum LesControlMode {
    OPTIMIZE_SYSTEM(1, "系统优化"),
    COORDINATE_SYSTEM(2, "线控配时"),
    GREEN_WAVE(3, "绿波"),
    LOCK_FLOW(4, "指定相位"),
    SYSTEM_MANUAL(5, "系统手动"),
    BUS_PRIORITY_CONTROL(6, "公交优先"),

    LOCK_FLOWS(7, "锁定流向"),
    OVER_FLOW_CONTROL(9, "溢出控制"),
    ADAPTIVE_CONTROL(10, "系统全息"),
    PRIORITY_LOCAL(16, "单点优先控制"),
    PEDESTRIAN_CROSSING(17, "行人过街"),
    OPTIMIZE_LOCAL(19, "单点优化"),
    OFFLINE(20, "脱机"),
    COORDINATE_LOCAL(21, "无电缆绿波"),
    ACTUATED_LOCAL(22, "自感应"),
    FIXED_CYCLE(23, "定周期"),
    YELLOW_FLASH(24, "黄闪"),
    CLOSE_LAMP(25, "关灯"),
    ALL_RED(26, "全红"),
    STREET_CAR_PRIORITY_ABS(34, "有轨绝对优先"),
    STREET_CAR_PRIORITY_REL(35, "有轨相对优先"),
    TEMPORARY_PATTERN(38, "临时方案"),
    INTERSECTION_MANUAL(40, "路口手动"),
    STREET_CAR_PRIORITY_LOCAL(43, "单点电车优先"),
    STREET_CAR_PRIORITY_LOCAL_JUMP(44, "单点电车跳相"),
    STREET_CAR_PRIORITY_LOCAL_FOLLOW(45, "单点电车跟随"),
    ON_LINE(99, "联机");

    private int value;
    private String des;

    LesControlMode(int value, String des) {
        this.value = value;
        this.des = des;
    }

    public int value() {
        return this.value;
    }

    public String des() {
        return this.des;
    }

    public static Optional<LesControlMode> getType(int orgValue) {
        Optional<LesControlMode> messageTypeOptional = Arrays.asList(LesControlMode.values()).stream()
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();

        return messageTypeOptional;
    }

}
