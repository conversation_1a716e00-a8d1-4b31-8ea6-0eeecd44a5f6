package com.myweb.daa.areasignal.centralsystem.param.v2;

import com.les.ads.ds.enums.ControlModeType;
import lombok.NonNull;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description:
 * @date: 2024/7/24
 * @version: 1.0
 * @modifyHistory:
 */
public enum ControlMode1049V2 {

    OFFLINE("00", "撤销或恢复自主, 仅在“指定控制方式方案”（5.3.2）中使用"),
    INTERSECTION_MANUAL("01", "本地手动控制, 用户在路口现场手动控制信号机"),
    CLOSE_LAMP("11", "特殊控制-全部关灯"),
    ALL_RED("12", "特殊控制-全红"),
    YELLOW_FLASH("13", "特殊控制-全部黄闪"),
    FIXED_CYCLE("21", "单点多时段定时控制"),
    ACTUATED_LOCAL("22", "单点感应控制"),
    ADAPTIVE_CONTROL("23", "单点自适应控制"),
    COORDINATE_LOCAL("31", "线协调定时控制"),
    COORDINATE_ACTUATED("32", "线协调感应控制"),
    COORDINATE_SYSTEM("33", "线协调自适应控制"),
    COORDINATE_AREA("41", "区域协调控制"),
    SYSTEM_MANUAL("51", "干预控制-手动控制"),
    LOCK_STAGE("52", "干预控制-锁定阶段"),
    LOCK_PATTERN("53", "干预控制-指定方案");

    private final String code;
    private final String desc;

    ControlMode1049V2(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ControlMode1049V2 trans(@NonNull ControlModeType modeLes) {
        ControlMode1049V2 mode1049 = ControlMode1049V2.FIXED_CYCLE;
        switch (modeLes) {
            case COORDINATE_SYSTEM:
                mode1049 = ControlMode1049V2.COORDINATE_SYSTEM;
                break;
            case LOCK_FLOW:
                mode1049 = ControlMode1049V2.LOCK_STAGE;
                break;
            case SYSTEM_MANUAL:
                mode1049 = ControlMode1049V2.SYSTEM_MANUAL;
                break;
            case OVER_FLOW_CONTROL:
            case ADAPTIVE_CONTROL:
            case SINGLE_ADAPTIVE:
                mode1049 = ControlMode1049V2.ADAPTIVE_CONTROL;
                break;
            case PEDESTRIAN_CROSSING:
            case ACTUATED_LOCAL:
                mode1049 = ControlMode1049V2.ACTUATED_LOCAL;
                break;
            case COORDINATE_LOCAL:
                mode1049 = ControlMode1049V2.COORDINATE_LOCAL;
                break;
            case YELLOW_FLASH:
                mode1049 = ControlMode1049V2.YELLOW_FLASH;
                break;
            case CLOSE_LAMP:
                mode1049 = ControlMode1049V2.CLOSE_LAMP;
                break;
            case ALL_RED:
                mode1049 = ControlMode1049V2.ALL_RED;
                break;
            case TEMPORARY_PATTERN:
                mode1049 = ControlMode1049V2.LOCK_PATTERN;
                break;
            case INTERSECTION_MANUAL:
                mode1049 = ControlMode1049V2.INTERSECTION_MANUAL;
                break;
            default:
                break;
        }
        return mode1049;
    }

    public static ControlMode1049V2 trans(@NonNull Integer modeNoLes) {
        return trans(ControlModeType.parseCode(modeNoLes));
    }
}
