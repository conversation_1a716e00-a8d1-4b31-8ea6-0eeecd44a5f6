package com.myweb.daa.areasignal.centralsystem.utils;

import com.les.ads.common.model.Point;
import com.les.ads.common.util.HttpHeadersUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Project: nacos-server-parent
 * @PackageName: com.les.ads.ds.com.les.ads.common.util
 * @Author: whr
 * @DateTime: 2021/2/22 8:47
 * @Description:
 * @Version:
 * @ModifyHistory:
 */
public class CoordinateUtil {

    private final static double earthRad = 6378137.0;//地球半径
    private final static double rad = Math.PI / 180;//弧度计算
    private final static double re = earthRad * rad;
    public static double pi = 3.1415926535897932384626;
    public static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    public static double a = 6378245.0;
    public static double ee = 0.00669342162296594323;
    public static String coordinateSplit = ";";

    /**
     * 经纬度保留小数位处理
     */
    public static Double splitDouble(Double val) {

        if (val != null && val.toString().length() > 9) {
            String lonStr = val.toString().substring(0, 9);
            val = Double.parseDouble(lonStr);
        }
        return val;
    }

    public static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    public static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
        return ret;
    }

    public static double[] transform(double lat, double lon) {

        if (outOfChina(lat, lon)) {
            return new double[]{lat, lon};
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * pi;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new double[]{mgLat, mgLon};
    }

    public static boolean outOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347)
            return true;
        if (lat < 0.8293 || lat > 55.8271)
            return true;
        return false;
    }

    public static Point wgs84_To_Gcj02(Point wgsNode) {
        double lat = wgsNode.getY();
        double lon = wgsNode.getX();

        if (outOfChina(lat, lon)) {
            return Point.builder().y(lat).x(lon).build();
        }
        double[] coord = transform(lat, lon);
        return Point.builder().x(coord[1]).y(coord[0]).build();
    }

    public static String wgs84_To_Gcj02(String wgsNode) {
        try {
            Optional<Point> optional = Point.parse(wgsNode);
            if (optional.isPresent()) {
                return wgs84_To_Gcj02(optional.get()).toString();
            }
        } catch (NumberFormatException ignored) {
        }
        return null;
    }

    /**
     * WG484坐标转为GCJ02坐标
     *
     * @param nodes 坐标列表，坐标格式：180.56894,32.464545
     * @return
     */
    public static List<String> wgs84_To_Gcj02(String[] nodes) {
        List<String> gcjNodes = new ArrayList<>();
        if (nodes != null && nodes.length > 0) {
            for (String node : nodes) {
                if (StringUtils.hasLength(node)) {
                    gcjNodes.add(wgs84_To_Gcj02(node));
                }
            }
        }
        return gcjNodes;
    }

    public static List<Point> wgs84_To_Gcj02(List<Point> wgsNodes) {
        List<Point> gcjNodes = new ArrayList<>();
        if (CollectionUtils.isEmpty(wgsNodes)) {
            return gcjNodes;
        }
        for (Point node : wgsNodes) {
            gcjNodes.add(wgs84_To_Gcj02(node));
        }
        return gcjNodes;
    }

    public static Point gcj02_To_Wgs84(Point gcjNode) {
        if (gcjNode == null) {
            return null;
        }
        double lat = gcjNode.getY();
        double lon = gcjNode.getX();

        double[] gps = transform(lat, lon);
        double lontitude = lon * 2 - gps[1];
        double latitude = lat * 2 - gps[0];
        return Point.builder().x(lontitude).y(latitude).build();
    }

    public static String gcj02_To_Wgs84(String gcjNode) {
        try {
            Optional<Point> optional = Point.parse(gcjNode);
            if (optional.isPresent()) {
                return gcj02_To_Wgs84(optional.get()).toString();
            }
        } catch (NumberFormatException ignored) {
        }
        return null;
    }

    public static List<String> gcj02_To_Wgs84(String[] nodes) {
        List<String> wgsNodes = new ArrayList<>();
        if (nodes != null && nodes.length > 0) {
            for (String node : nodes) {
                if (StringUtils.hasLength(node)) {
                    wgsNodes.add(gcj02_To_Wgs84(node));
                }
            }
        }
        return wgsNodes;
    }

    public static double rad(double d) {
        return d * pi / 180.0;
    }

    private static double getEarthRadius(double latitude) {
        double polarRadius = 6356752;
        double equatorialRadius = 6378137;
        double radius;
        double lat = Math.abs(latitude);
        if (lat >= 90) {
            radius = polarRadius;
        } else {
            double k = Math.tan(rad(lat));
            radius = (1 + (k * k)) * polarRadius * equatorialRadius / (polarRadius + (equatorialRadius * k * k));
        }
        return radius;
    }

    /**
     * 获取两地理坐标间的直线距离
     *
     * @param lat1 起点纬度
     * @param lng1 起点经度
     * @param lat2 终点纬度
     * @param lng2 终点经度
     * @return 两坐标点间距离，单位：m
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        if (Math.abs(lat1 - lat2) < 0.000001 && Math.abs(lng1 - lng2) < 0.000001) {
            return 0;
        }
        double earthRadius = getEarthRadius((lat1 + lat2) / 2);
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double dLat = (radLat1 - radLat2) / 2;
        double dLng = (rad(lng1) - rad(lng2)) / 2;
        double a = Math.pow(Math.sin(dLat), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(dLng), 2);
        return 2 * earthRadius * Math.asin(Math.sqrt(a));
    }

    /**
     * 获取两地理坐标间的直线距离
     *
     * @param startNode 起点
     * @param endNode   终点
     * @return 两坐标点间距离，单位：m
     */
    public static double getDistance(Point startNode, Point endNode) {
        return getDistance(startNode.getY(), startNode.getX(), endNode.getY(), endNode.getX());
    }

    /**
     * 获取两webMercator坐标间的直线距离
     *
     * @param startNode 起点
     * @param endNode   终点
     * @return 两坐标点间距离，单位：m
     */
    public static double getDistanceByProject(Point startNode, Point endNode) {
        if (startNode != null && endNode != null) {
            double lat1 = webMercatorY2Lat(startNode.getY());
            double lat2 = webMercatorY2Lat(endNode.getY());
            double rate = Math.cos((Math.toRadians(lat1) + Math.toRadians(lat2)) / 2);
            double dis = Math.sqrt(Math.pow(endNode.getX() - startNode.getX(), 2) + Math.pow(endNode.getY() - startNode.getY(), 2));
            return rate * dis;
        }
        return -1;
    }

    /// <summary>
    /// WGS84地理坐标转Web墨卡托投影坐标
    /// </summary>
    /// <param name="lat">纬度</param>
    /// <param name="lon">经度</param>
    /// <returns>返回Web墨卡托投影坐标</returns>
    public static Point toWebMercator(double lat, double lon) {
        return Point.builder().x(lon2WebMercatorX(lon)).y(lat2WebMercatorY(lat)).build();
    }

    /// <summary>
    /// Wgs84纬度转换成墨卡托Y轴坐标（米）
    /// </summary>
    /// <param name="lat"></param>
    public static double lat2WebMercatorY(double lat) {
        double a = lat * rad;
        return earthRad / 2 * Math.log((1.0 + Math.sin(a)) / (1.0 - Math.sin(a)));
    }

    /// <summary>
    /// Wgs84经度转换成墨卡托X轴坐标（米）
    /// </summary>
    /// <param name="lon"></param>
    public static double lon2WebMercatorX(double lon) {
        return lon * re;
    }

    /**
     * 墨卡托x轴坐标转换成Wgs84经度（度）
     *
     * @param x Web Mercator x值
     * @return 经度
     */
    public static double webMercatorX2Lon(double x) {
        return x / re;
    }

    /**
     * 墨卡托y轴坐标转换成Wgs84纬度（度）
     *
     * @param y Web Mercator y值
     * @return 纬度
     */
    public static double webMercatorY2Lat(double y) {
        y = y / re;
        return (2 * Math.atan(Math.exp(y * rad)) - Math.PI / 2) / rad;
    }

    /**
     * 根据wgs84点位经纬度，偏移距离，方位角换算偏移后距离
     *
     * @param lon        x
     * @param lat        y
     * @param distance
     * @param coordinate 坐标系
     * @return
     */
    public static Point coordinateOffset(double lon, double lat, double distance, double azimuth, String coordinate) {
        Point point = new Point(lon, lat);
        if (coordinate.equals(HttpHeadersUtil.GCJ02)) {
            point = gcj02_To_Wgs84(point);
        }
        point = toWebMercator(lat, lon);

        double x = point.getX();
        double y = point.getY();

        //角度换算为弧度
        azimuth = Math.toRadians(azimuth);
        x = x + distance * Math.cos(azimuth);
        y = y + distance * Math.sin(azimuth);
        point.setX(x);
        point.setY(y);
        point = point.toWgs84();

        if (coordinate.equals(HttpHeadersUtil.GCJ02)) {
            wgs84_To_Gcj02(point);
        }
        return point;
    }

    /**
     * 计算多点之间的中心点
     *
     * @param centerPoints
     * @return
     */
    public static Point getCenterPoint(List<Point> centerPoints) {
        double minLat = 90;
        double minLon = 180;
        double maxLat = 0;
        double maxLon = 0;
        for (Point point : centerPoints) {
            minLat = Math.min(minLat, point.getY());
            minLon = Math.min(minLon, point.getX());
            maxLat = Math.max(maxLat, point.getY());
            maxLon = Math.max(maxLon, point.getX());
        }
        return new Point((minLon + maxLon) / 2, (minLat + maxLat) / 2);
    }

    public static void main(String[] args) {
        Point point = coordinateOffset(118.87963503772495, 32.02097181745381, 50, 50, "wgs84");
        System.out.println(point.getX());
        System.out.println(point.getY());
    }
}
