package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.Pattern;
import com.les.ads.ds.signal.Ring;
import com.les.ads.ds.signal.Sequence;
import com.les.ads.ds.signal.Split;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 16:03
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlanParam implements SgpTransAble {
    public static final String MqObjectId = "1008";

    private String signalControllerID;
    private int noArea;
    private int noJunc;

    private int planNo;
    private long cycleLen;
    private int coordPhaseNo;
    private int coordPhaseNoIndex;
    private int offSet;
    private List<Integer> stageNoList;
    private List<Integer> greenList;
    private List<Integer> timeList;
    private int crossingSeqNo;

    /**
     * 方案描述
     */
    private String description;

    /**
     * 用于标记是否是原始1049阶段
     */
    private boolean org1049StageNo;

    @Override
    public String getUrl() {
        return "/pattern/" + signalControllerID;
    }

    @Override
    public Object transData() {
        Pattern pattern = new Pattern();
        pattern.setPatternNo(planNo);
        pattern.setCycleLen((int) cycleLen);
        pattern.setStageNum(stageNoList != null ? stageNoList.size() : 0);
        pattern.setCoordStageNo(coordPhaseNo);
        pattern.setOffset(offSet);
        pattern.setCrossingSeqNo(crossingSeqNo);
        pattern.setDescription(description != null ? description : "");
        if (stageNoList != null && greenList != null
                && greenList.size() == stageNoList.size()
                && timeList != null
                && timeList.size() == stageNoList.size()) {
            //设置sequece数据项
            Sequence sequence = new Sequence();
            sequence.setSequenceNo(0);
            List<Ring> rings = new ArrayList<>();
            Ring ring = new Ring();
            ring.setRingNo(0);
            ring.setPhaseIds(stageNoList);
            rings.add(ring);
            sequence.setRings(rings);
            pattern.setSequence(sequence);
            //设置split数据项
            List<Split> splits = new ArrayList<>();
            IntStream.range(0, stageNoList.size()).forEach(
                    index -> {
                        splits.add(Split.builder()
                                .no(planNo)
                                .index(index)
                                .stageNo(stageNoList.get(index))
                                .green(greenList.get(index))
                                .time(timeList.get(index))
                                .coordinateStage(stageNoList.get(index).equals(coordPhaseNo))
                                .build());
                    }
            );
            pattern.setSplits(splits);
        }

        return pattern;
    }

    @Override
    public String getClazzName() {
        return PlanParam.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return planNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }


}
