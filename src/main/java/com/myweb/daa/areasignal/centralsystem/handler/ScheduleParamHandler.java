package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.ScheduleParam;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.process.P1049InfosProcess;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class ScheduleParamHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private DataInternalNotify dataInternalNotify;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private P1049InfosProcess p1049InfosProcess;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return ScheduleParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return ScheduleParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return ScheduleParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {

        log.error("调看信号机{}调度数据-{}", controllerId, datas);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        //批量数据
        if (datas.isEmpty()) {
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();


            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);

        } 	else {
            //请求调度计划数据参数
            datas.stream().forEach(
                    planNo -> {
                        {
                            TSCCmd tscCmd = TSCCmd.builder()
                                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam.class.getSimpleName())
                                    .ID(crossingInfoOp.get().getCrossingId1049())
                                    .No(String.valueOf(planNo))
                                    .build();


                            List<TSCCmd> tscCmds = new ArrayList<>();
                            tscCmds.add(tscCmd);
                            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Get)
                                    .object(tscCmds)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(TSCCmd.class).build();
                            requestMsgs.add(p1049CentralSystemMsg);
                        }
                    }
            );
        }
        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<Integer> scheduleNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        scheduleNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }
        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());
        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        String signalId = signalInfoOp.get().getSignalId();

        //解析返回数据
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //批量调看数据时，调度计划编号需要从已有数据中查找
        if (scheduleNos.isEmpty()) {
            //同步删除sgp所有的原先调度数据项，因为调度计划个数存在变化
             {
                //删除sgp数据项
                dataInternalNotify.deleteSgpScheduleSync(signalId);

                itemDatas.stream().filter(
                        itemData -> itemData.getData() instanceof BaseMessage1049
                ).forEach(
                        itemData -> {
                            BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                            Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                            if (!firstMsgData.isPresent()) {
                                return;
                            }

                            if (firstMsgData.get() instanceof SDO_Error1049) {
                                log.error("请求应答异常-{}", firstMsgData.get());
                                return;
                            }


                            List<Object> objectList = (List<Object>) (firstMsgData.get());
                            if (objectList == null || objectList.isEmpty()) {
                                return;
                            }

                            //如果是调度计划数据项
                            if ((objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam)) {
                                List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam> scheduleParamList
                                        = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam>) (firstMsgData.get());
                                scheduleParamList.stream().forEach(
                                        scheduleParam -> {
                                            //调度计划添加
                                            scheduleNos.add(Integer.parseInt(scheduleParam.getScheduleNo()));
                                        }
                                );
                            }

                        });

            }
        }
        //按照方案编号进行排序
        scheduleNos.sort(Comparator.comparingInt(Integer::intValue));

        List<Object> scheduleParams = new ArrayList<>();
        //待更新的通知数据项
        List<Object> notifyScheduleParams = new ArrayList<>();
        List<String> notifyNos = new ArrayList<>();
        scheduleNos.stream().forEach(
                scheduleNo -> {
                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }

                                 {
                                    List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam> scheduleParamList
                                            = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam>) (firstMsgData.get());

                                    if (scheduleParamList == null || scheduleParamList.isEmpty()) {
                                        return;
                                    }

                                    Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam> scheduleParamOptional = scheduleParamList.stream().filter(
                                            scheduleParam ->
                                            {
                                                if (scheduleParam.getScheduleNo() == null) {
                                                    return false;
                                                } else {
                                                    return Integer.parseInt(scheduleParam.getScheduleNo()) == scheduleNo;
                                                }
                                            }

                                    ).findAny();

                                    if (!scheduleParamOptional.isPresent()) {
                                        log.error("未找到调度计划参数-{}", scheduleParamList);
                                        return;
                                    }

                                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam scheduleParamOrg
                                            = scheduleParamOptional.get();


                                        {
                                            //内部数据项变更通知,存储的数据项是列表
                                            //内部数据项变更通知
                                            notifyScheduleParams.add(scheduleParamOrg);
                                            notifyNos.add(String.valueOf(scheduleNo));

                                            //日方案数据项
                                            ScheduleParam scheduleParam = ScheduleParam.builder()
                                                    .signalControllerID(signalInfoOp.get().getSignalId())
                                                    .noArea(signalInfoOp.get().getNoArea())
                                                    .noJunc(signalInfoOp.get().getNoJunc())
                                                    .scheduleNo(scheduleNo)
                                                    .crossingSeqNo(1)
                                                    .dayPlanNo(Integer.parseInt(scheduleParamOrg.getDayPlanNo() != null ? scheduleParamOrg.getDayPlanNo() : "1"))
                                                    .priority(255).build();

                                            //数据项
                                            int type = Integer.parseInt(scheduleParamOrg.getType());


                                            String startDay = scheduleParamOrg.getStartDay();
                                            String endDay = scheduleParamOrg.getEndDay();
                                            String weekDay = scheduleParamOrg.getWeekDay();

                                            if (1 == type || (2 == type)) {
                                                //特殊日调度
                                                int dataIntMonth_S = 0;
                                                int dataIntDay_S = 0;
                                                int dataIntMonth_E = 0;
                                                int dataIntDay_E = 0;

                                                // 解析 MM-DD 格式的日期
                                                if (startDay != null && startDay.contains("-")) {
                                                    String[] parts = startDay.split("-");
                                                    if (parts.length == 2) {
                                                        dataIntMonth_S = Integer.parseInt(parts[0]);
                                                        dataIntDay_S = Integer.parseInt(parts[1]);
                                                    }
                                                }

                                                // 解析 MM-DD 格式的日期
                                                if (endDay != null && endDay.contains("-")) {
                                                    String[] parts = endDay.split("-");
                                                    if (parts.length == 2) {
                                                        dataIntMonth_E = Integer.parseInt(parts[0]);
                                                        dataIntDay_E = Integer.parseInt(parts[1]);
                                                    }
                                                }

                                                //调度星期
                                                {
                                                    int dataInt = Integer.parseInt(weekDay);

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 0; i < 7; i++) {
                                                        if ((dataInt % 7) == i) {
                                                            bits.add(i);
                                                            datas.add(1);
                                                        } else {
                                                            datas.add(0);
                                                        }
                                                    }
                                                    scheduleParam.setWeekNos(bits);
                                                    scheduleParam.setWeek(1 << (dataInt % 7));
                                                    scheduleParam.setWeekList(datas);
                                                }

                                                //调度表月份
                                                {
                                                    if(dataIntMonth_S != dataIntMonth_E){
                                                        log.error("跨月的调度计划解析会出现异常-{}", scheduleParamOrg);
                                                    }

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 1; i <= 12; i++) {
                                                        if ((dataIntMonth_S == i) || (dataIntMonth_E == i)) {
                                                            bits.add(i);
                                                            datas.add(1);
                                                        } else {
                                                            datas.add(0);
                                                        }
                                                    }
                                                    scheduleParam.setMonthNos(bits);
                                                    scheduleParam.setMonth( ((1 << dataIntMonth_S)
                                                            + (1 << dataIntMonth_E)) >> 1);
                                                    scheduleParam.setMonthList(datas);

                                                }

                                                //调度表日期
                                                try{

                                                    int year = LocalDate.now().getYear();

                                                    // Create LocalDate objects
                                                    LocalDate start = LocalDate.of(year, dataIntMonth_S, dataIntDay_S);
                                                    LocalDate end = LocalDate.of(year, dataIntMonth_E, dataIntDay_E);

                                                    // If end date is before start date, assume it's in the next year
                                                    if (end.isBefore(start)) {
                                                        log.error("起始日期异常-{}", scheduleParamOrg);
                                                        return;
                                                    }

                                                    // Generate all dates in the range
                                                    LocalDate current = start;

                                                    List<LocalDate> localDates = new ArrayList<>();
                                                    while (!current.isAfter(end)) {
                                                        localDates.add(current);
                                                        current = current.plusDays(1);
                                                    }

                                                    long dataLong = 0;
                                                    for (int i = 0; i < localDates.size() ; i++) {
                                                        dataLong |= (1 << (localDates.get(i).getDayOfMonth()));
                                                    }

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 1; i <= 31; i++) {
                                                        if (((dataLong >> i) & 0x01) == 1) {
                                                            bits.add(i);
                                                            datas.add(1);
                                                        } else {
                                                            datas.add(0);
                                                        }
                                                    }
                                                    scheduleParam.setDayNos(bits);
                                                    scheduleParam.setDay(dataLong & 0x7fffffff);
                                                    scheduleParam.setDayList(datas);
                                                } catch (Exception e) {
                                                    log.error("调度计划解析异常", e);
                                                }

                                            } else if (3 == type) {
                                                //调度表星期  WeekDay正常取值为1-7 代表周一至周日的调度
                                                {
                                                    int dataInt = Integer.parseInt(weekDay);

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 0; i < 7; i++) {
                                                        if ((dataInt % 7) == i) {
                                                            bits.add(i);
                                                            datas.add(1);
                                                        } else {
                                                            datas.add(0);
                                                        }
                                                    }
                                                    scheduleParam.setWeekNos(bits);
                                                    scheduleParam.setWeek(1 << (dataInt % 7));
                                                    scheduleParam.setWeekList(datas);
                                                }

                                                //调度表月份
                                                {
                                                    int dataInt = 0;

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 1; i <= 12; i++) {
                                                        datas.add(0);
                                                    }
                                                    scheduleParam.setMonthNos(bits);
                                                    scheduleParam.setMonth(0);
                                                    scheduleParam.setMonthList(datas);

                                                }

                                                //调度表日期
                                                {
                                                    long dataLong = 0;

                                                    List<Integer> bits = new ArrayList<>();
                                                    List<Integer> datas = new ArrayList<>();
                                                    for (int i = 1; i <= 31; i++) {
                                                        datas.add(0);
                                                    }
                                                    scheduleParam.setDayNos(bits);
                                                    scheduleParam.setDay(0);
                                                    scheduleParam.setDayList(datas);
                                                }
                                            }

                                            //设置启用
                                            {
                                                scheduleParam.setIsEnabled(1);
                                            }

                                            log.debug("获取华通信号机-{}-调度-{}-{}", signalId, scheduleNo, scheduleParam);
                                            scheduleParams.add(scheduleParam);
                                        }
                                }
                            }
                    );
                }
        );

        //批量数据通知,必须放在此处，否则会出现获取编码时获取不到转换数据项
        if (!notifyNos.isEmpty()) {
            dataInternalNotify.datasNotify(requestMessage.getSignalControllerID(), subJuncNo,
                    notifyNos, notifyScheduleParams);
        }

        //如果没有查找到对应的方案数据
        if (scheduleParams.isEmpty()) {
            return Optional.empty();
        }

        log.error("调看信号机{}调度计划-{}，数据返回-{}", crossingInfoOp.get().getControllerId(), scheduleNos, scheduleParams);
        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, scheduleParams);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        log.error("收到调度计划设置原始数据-{}", datas);


        AtomicInteger errorCount = new AtomicInteger(0);

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    ScheduleParam scheduleParam;
                    if (data instanceof ScheduleParam) {
                        scheduleParam = (ScheduleParam) data;
                    } else {
                        JSONObject jsonObject = (JSONObject) data;
                        scheduleParam =
                                jsonObject.toJavaObject(com.myweb.daa.areasignal.centralsystem.param.ScheduleParam.class);
                    }

                    log.error("收到调度计划设置命令{}", scheduleParam);

                    //转换成内部数据处理的类型
                    scheduleParam.exchangeToLocal();

                    //数据生成
                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam scheduleParamOrg
                            = new com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.ScheduleParam();

                    scheduleParamOrg.setCrossID(crossingInfoOp.get().getCrossingId1049());
                    scheduleParamOrg.setScheduleNo(String.valueOf(scheduleParam.getScheduleNo()));
                    //日计划方案
                    scheduleParamOrg.setDayPlanNo(String.valueOf(scheduleParam.getDayPlanNo()));


                    //默认为调度计划修改
                    List<SetScheduleParam> scheduleParams = new ArrayList<>();
                    SetScheduleParam setScheduleParam = SetScheduleParam.builder()
                            .Oper("2")
                            .ScheduleParam(scheduleParamOrg).build();
                    scheduleParams.add(setScheduleParam);

                    //所有调度计划数据项均为空时，表示删除
                    if (scheduleParam.getWeekNos().isEmpty()
                            && scheduleParam.getMonthNos().isEmpty()
                            && scheduleParam.getDayNos().isEmpty()) {
                        //删除调度计划数据项
                        setScheduleParam.setOper("3");
                        scheduleParamOrg.setWeekDay("");
                        scheduleParamOrg.setType("3");
                        scheduleParamOrg.setStartDay("");
                        scheduleParamOrg.setEndDay("");
                    } else {

                        {
                            //原始内存中没有找到这个调度计划
                            Optional<com.myweb.daa.areasignal.centralsystem.param.ScheduleParam> scheduleParamOp
                                    = signalCacheService.getData(crossingInfoOp.get().getControllerId(),
                                    scheduleParam.getScheduleNo(), com.myweb.daa.areasignal.centralsystem.param.ScheduleParam.class);
                            if (!scheduleParamOp.isPresent()) {
                                //新增调度计划数据项
                                setScheduleParam.setOper("1");
                            }
                        }

                        //星期调度
                        if (!scheduleParam.getWeekNos().isEmpty() && scheduleParam.getMonthNos().isEmpty()
                                && scheduleParam.getDayNos().isEmpty()) {
                            if (scheduleParam.getWeekNos().size() != 1) {
                                log.error("星期调度计划数据只支持一个星期-{}", scheduleParam);
                                return;
                            }
                            Integer weekNo = scheduleParam.getWeekNos().get(0);
                            if (weekNo < 0 || weekNo > 6) {
                                log.error("星期调度数据异常-{}", scheduleParam);
                                return;
                            }

                            //星期调度
                            scheduleParamOrg.setWeekDay(String.valueOf(weekNo == 0 ? 7 : weekNo));
                            scheduleParamOrg.setType("3");
                            scheduleParamOrg.setStartDay("");
                            scheduleParamOrg.setEndDay("");
                        }
                        // 月 日、星期都有
                        else if (!scheduleParam.getWeekNos().isEmpty() && !scheduleParam.getMonthNos().isEmpty()
                                && !scheduleParam.getDayNos().isEmpty()) {

                            if (scheduleParam.getWeekNos().size() != 1) {
                                log.error("星期调度计划数据只支持一个星期-{}", scheduleParam);
                                return;
                            }
                            Integer weekNo = scheduleParam.getWeekNos().get(0);
                            if (weekNo < 0 || weekNo > 6) {
                                log.error("星期调度数据异常-{}", scheduleParam);
                                return;
                            }


                            //特殊日调度,暂时只支持一个月，多个连续日调度
                            if (scheduleParam.getMonthNos().size() != 1) {
                                log.error("特殊日调度计划数据只支持一个月份-{}", scheduleParam);
                                return;
                            }
                            Integer month = scheduleParam.getMonthNos().get(0);

                            List<Integer> dayNos = scheduleParam.getDayNos();
                            if (dayNos == null || dayNos.isEmpty()) {
                                log.error("特殊日调度计划数据日没有设置-{}", scheduleParam);
                                return;
                            }

                            int minDay = dayNos.stream().min(Comparator.comparing(x -> x)).get();
                            int maxDay = dayNos.stream().max(Comparator.comparing(x -> x)).get();

                            //特殊日星期调度调度
                            scheduleParamOrg.setType("2");
                            scheduleParamOrg.setWeekDay(String.valueOf(weekNo == 0 ? 7 : weekNo));
                            scheduleParamOrg.setStartDay(String.format("%02d", month) + "-" + String.format("%02d", minDay));
                            scheduleParamOrg.setEndDay(String.format("%02d", month) + "-" + String.format("%02d", maxDay));
                        }
                        //特殊日调度
                        else {
                            //特殊日调度,暂时只支持一个月，多个连续日调度
                            if (scheduleParam.getMonthNos().size() != 1) {
                                log.error("特殊日调度计划数据只支持一个月份-{}", scheduleParam);
                                return;
                            }
                            Integer month = scheduleParam.getMonthNos().get(0);

                            List<Integer> dayNos = scheduleParam.getDayNos();
                            if (dayNos == null || dayNos.isEmpty()) {
                                log.error("特殊日调度计划数据日没有设置-{}", scheduleParam);
                                return;
                            }

                            int minDay = dayNos.stream().min(Comparator.comparing(x -> x)).get();
                            int maxDay = dayNos.stream().max(Comparator.comparing(x -> x)).get();

                            //特殊日调度
                            scheduleParamOrg.setType("1");
                            scheduleParamOrg.setWeekDay("");
                            scheduleParamOrg.setStartDay(String.format("%02d", month) + "-" + String.format("%02d", minDay));
                            scheduleParamOrg.setEndDay(String.format("%02d", month) + "-" + String.format("%02d", maxDay));
                        }

                    }

                    P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                            .messageType(MessageType.REQUEST)
                            .operationName(OperationName.Set)
                            .object(scheduleParams)
                            .signalBrandPort(signalBrandPort)
                            .address(crossingInfoOp.get().getAddress1049Ip())
                            .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam.class).build();
                    requestMsgs.add(p1049CentralSystemMsg);
                    }
                );


        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送调度计划设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<ScheduleParam> scheduleParams = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        if (objectId instanceof ScheduleParam) {
                            ScheduleParam schedulePara = (ScheduleParam) objectId;
                            scheduleParams.add(schedulePara);
                        } else {
                            JSONObject jsonObject = (JSONObject) objectId;
                            ScheduleParam scheduleParam = jsonObject.toJavaObject(ScheduleParam.class);
                            scheduleParams.add(scheduleParam);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        AtomicBoolean error = new AtomicBoolean(false);
        scheduleParams.stream().forEach(
                scheduleParam -> {
                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(scheduleParam.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }
                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    error.set(true);
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    error.set(true);
                                    return;
                                }

                                List<Object> objectList = (List<Object>) (firstMsgData.get());
                                if (objectList == null || objectList.isEmpty()) {
                                    return;
                                }

                                //如果非阶段数据项
                                if ((objectList.get(0)
                                        instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam)) {

                                    List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam> scheduleParamList
                                            = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam>) (firstMsgData.get());
                                    if (scheduleParamList == null || scheduleParamList.isEmpty()) {
                                        return;
                                    }

                                    Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetScheduleParam> returnData = Optional.empty();
                                    //返回列表数据项的系统，需要找到对应数据项返回

                                    returnData = scheduleParamList.stream().filter(
                                            scheduleParam1 ->
                                                    scheduleParam1.getScheduleParam().getScheduleNo().equalsIgnoreCase(String.valueOf(scheduleParam.getScheduleNo()))

                                    ).findAny();


                                    if (returnData.isPresent()) {
                                        //内部数据项变更通知
                                        dataInternalNotify.dataNotify(requestMessage.getSignalControllerID(), subJuncNo,
                                                String.valueOf(scheduleParam.getScheduleNo()), returnData.get());

                                        log.error("加载{}调度参数返回{}", crossingInfoOp.get().getControllerId(), returnData.get());
                                    }
                                }
                            }
                    );
                }
        );

        if (error.get()) {
            return Optional.empty();
        }

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
