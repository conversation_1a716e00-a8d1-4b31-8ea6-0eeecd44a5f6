package com.myweb.daa.areasignal.centralsystem.mq;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:04
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MqMessage {
    private String version;
    private String token;
    private int type;
    private int operator;
    private long sequenceCode;
    private String objectId;
    private String signalControllerID;
    private String errorCode;
    private String errorInfo;
    private List<Object> objectList;

    /**
     * 是否是系统内部模拟数据项
     */
    @JsonIgnore
    @JSONField(serialize = false)
    private boolean simu;

    /**
     * 收到数据数据tag
     */
    private long timeStamp;

    public long getMapKey() {
        return sequenceCode + (timeStamp << 8);
    }
}
