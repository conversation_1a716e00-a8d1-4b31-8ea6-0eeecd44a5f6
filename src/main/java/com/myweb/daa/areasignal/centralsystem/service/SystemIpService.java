package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Author: WJ
 * @Description: 系统ip数据管理
 * @Date: create in 2022/9/26 17:27
 */
@Service
public class SystemIpService {

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    /**
     * 根据从系统读取的CrossParam以及SignalController设置系统ip地址
     */
    public void updateSystemIp() {

        //设置信号机系统源
        controllerService.getSignalBaseInfoMap().values().stream().forEach(
                signalBaseInfo -> {
                    String signalId1049 = signalBaseInfo.getSignalId1049();
                    //获取系统源ip地址数据项
                    Optional<String> signalSystemIpOp = ht1049SignalCacheService.getSignalSystemIp(signalId1049);
                    if (signalSystemIpOp.isPresent()) {
                        signalBaseInfo.setAddress1049Ip(signalSystemIpOp.get());
                    }
                }
        );

        //设置路口系统源
        crossingService.getCrossingBaseInfoMap().values().stream().forEach(
                crossingBaseInfo -> {
                    String crossingId1049 = crossingBaseInfo.getCrossingId1049();
                    //获取系统源ip地址数据项
                    Optional<String> signalSystemIpOp = ht1049SignalCacheService.getCrossSystemIp(crossingId1049);
                    if (signalSystemIpOp.isPresent()) {
                        crossingBaseInfo.setAddress1049Ip(signalSystemIpOp.get());
                    }
                }
        );
    }


}
