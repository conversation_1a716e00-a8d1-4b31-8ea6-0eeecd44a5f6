package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgOperator;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgType;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.DayPlanParam;
import com.myweb.daa.areasignal.centralsystem.param.PlanParam;
import com.myweb.daa.areasignal.centralsystem.param.ScheduleParam;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.ControllerNotifyMsg;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: WJ
 * @Description: 用于中控判定是否需要添加特征参数加载数据
 * @Date: create in 2023/2/15 9:02
 */
@Slf4j
@Service
public class AcsCharaService {

    /**
     * 用来串行化参数加载
     */
    private Map<String, Map<String, MqMessage>> controllerSetMsgMap = new ConcurrentHashMap<>();

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private P1049Sender p1049Sender;

    /**
     * 存储需要处理的消息类型
     */
    private static List<String> setMsgTypes = new ArrayList<>();

    static
    {
        setMsgTypes.add(StageParam.MqObjectId);
        setMsgTypes.add(PlanParam.MqObjectId);
        setMsgTypes.add(DayPlanParam.MqObjectId);
        setMsgTypes.add(ScheduleParam.MqObjectId);
    }

    /**
     * 初始化
     */
    public void initKey(){
        controllerService.getSignalBaseInfoMap().keySet().stream().forEach(
                controllerId -> {
                    //初始化数据项
                    controllerSetMsgMap.put(controllerId, new ConcurrentHashMap<>());
                }
        );
    }

    public void addController(String controllerId){
        if (!controllerSetMsgMap.containsKey(controllerId)) {
            controllerSetMsgMap.put(controllerId, new ConcurrentHashMap<>());
        }
    }


    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void startStopEventProcess(ControllerNotifyMsg controllerNotifyMsg) {
        addController(controllerNotifyMsg.getControllerId());
    }

    public void addAcsChar(List<P1049CentralSystemMsg> p1049CentralSystemMsgs, CrossingService.CrossingBaseInfo crossingBaseInfo,
                           SignalBrandPort signalBrandPort){

    }

    /**
     * 添加需要串行处理的数据对象
     * @param signalBaseInfo
     * @param mqMessage
     */
    public void addSetMqMessage(ControllerService.SignalBaseInfo signalBaseInfo, MqMessage mqMessage,
                                MqMsgType mqMsgType,
                                MqMsgOperator mqMsgOperator ){

        if (mqMsgType.value() == MqMsgType.MqMsgType_Request.value()
                && mqMsgOperator.value() == MqMsgOperator.MqMsgOperator_Set.value())
        {
            if(setMsgTypes.contains(mqMessage.getObjectId())) {
                Map<String, MqMessage> stringMqMessageMap = controllerSetMsgMap.get(signalBaseInfo.getSignalId());
                //针对同一个信号机的数据项串行数据处理
                if (stringMqMessageMap != null) {
                    //数据暂存
                    stringMqMessageMap.put(String.valueOf(mqMessage.getMapKey()), mqMessage);
                }
            }
        }
    }

    /**
     * 添加需要串行处理的数据对象
     * @param mqMessage
     */
    public void rmSetMqMessage( MqMessage mqMessage ){
        Optional<ControllerService.SignalBaseInfo> signalInfoOp =
                controllerService.getSignalInfo(mqMessage.getSignalControllerID());

        if(signalInfoOp.isPresent()) {
            Map<String, MqMessage> stringMqMessageMap = controllerSetMsgMap.get(signalInfoOp.get().getSignalId());
            //针对同一个信号机的数据项串行数据处理
            if (stringMqMessageMap != null) {
                //数据暂存
                stringMqMessageMap.remove(String.valueOf(mqMessage.getMapKey()));
            }
        }
    }



    /**
     * 添加特征参数加载
     * @param mqMessage
     */
    public void sendAcsCharOut( MqMessage mqMessage)
    {
    }

}
