package com.myweb.daa.areasignal.centralsystem.service;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataEntity;
import com.myweb.daa.areasignal.centralsystem.dbsave.QDataEntity;
import com.myweb.daa.areasignal.centralsystem.param.StageManualConfig;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.myweb.daa.areasignal.htbussiness.service.HtSignalService.DEFAULT_ID;

/**
 * @Author: WJ
 * @Description: 本地缓存数据项
 * @Date: create in 2021/5/31 16:26
 */
@Service
@Slf4j
@Getter
public class SignalCacheService {

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;

    /**
     * 信号机数据项
     */
    private ConcurrentHashMap<String, Map<String, DataEntity>> signalInfoMap = new ConcurrentHashMap<>();


    /**
     * 从数据库中加载基础数据项
     */
    public void loadDataFromDb() {
        try {
            //读取数据项
            List<DataEntity> dataEntities = queryFactory.selectFrom(QDataEntity.dataEntity).fetch();

            //保存到当前内存中
            dataEntities.forEach(
                    dataEntity -> {
                        Map<String, DataEntity> stringP1049EntityMap = signalInfoMap.get(dataEntity.getSignalId());
                        //此信号机的第一条数据项
                        if (stringP1049EntityMap == null) {
                            stringP1049EntityMap = new ConcurrentHashMap<>();
                            signalInfoMap.put(dataEntity.getSignalId(), stringP1049EntityMap);
                        }
                        stringP1049EntityMap.put(dataEntity.getKey(), dataEntity);
                    }
            );
        } catch (Exception e) {
            log.error("初始化加载中心机数据项异常——{}", e);
        }

    }


    /**
     * 删除特定的数据项指定no
     *
     * @param signalId
     * @param type
     */
    @Transactional(rollbackOn = Exception.class)
    public void deleteDataEntity(String signalId, String type) {

        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap != null) {
            String dataKeyPrefix = DataEntity.getDataKeyPrefix(signalId, type);
            List<String> keys = stringDataEntityMap.keySet().stream().filter(
                    key -> key.startsWith(dataKeyPrefix)
            ).collect(Collectors.toList());

            keys.stream().forEach(
                    key -> stringDataEntityMap.remove(key)
            );
        }

        //移除数据库数据项
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(signalId).and(
                        QDataEntity.dataEntity.type.eq(type)
                )).execute();
    }


    /**
     * 删除特定的数据项指定
     *
     * @param signalId
     * @param type
     */
    @Transactional(rollbackOn = Exception.class)
    public void deleteDataEntity(String signalId, String type, int no) {
        //移除内存数据项
        //查询是否已经有数据项
        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap != null) {
            stringDataEntityMap.remove(DataEntity.getDataKey(signalId, type, no));
        }

        //移除数据库数据项
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(signalId).and(
                        QDataEntity.dataEntity.type.eq(type)
                                .and(QDataEntity.dataEntity.no.eq(no))
                )).execute();
    }


    /**
     * 调看信号机数据项时候更新本地缓存
     */
    @Transactional(rollbackOn = Exception.class)
    public void updateData(DataEntity dataEntity) {
        String signalId = (dataEntity.getSignalId());

        //查询是否已经有数据项
        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap == null) {
            stringDataEntityMap = new ConcurrentHashMap<>();
            signalInfoMap.put(signalId, stringDataEntityMap);
        }

        stringDataEntityMap.put(dataEntity.getKey(), dataEntity);

        //本地数据存储
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(dataEntity.getSignalId()).and(
                        QDataEntity.dataEntity.typeId.eq(dataEntity.getTypeId())
                                .and(QDataEntity.dataEntity.no.eq(dataEntity.getNo()))
                )).execute();
        repositoryDao.save(dataEntity, DataEntity.class);
    }

    /**
     * 从缓存中获取一种数据项，指定no
     *
     * @param signalId
     * @param no
     * @param <T>
     * @return
     */
    public <T> Optional<T> getData(String signalId, int no, Class clazz) {
        Map<String, DataEntity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = DataEntity.getDataKey(signalId, clazz.getSimpleName(), no);
        if (signalInfo.containsKey(nameKey)) {
            T data = (T) JSONObject.parseObject(signalInfo.get(nameKey).getData(), clazz);
            return Optional.of(data);
        } else {
            return Optional.empty();
        }
    }


    /**
     * 从缓存中获取一种数据项列表
     *
     * @param signalId
     * @param <T>
     * @return
     */
    public <T> Optional<List<T>> getDatas(String signalId, Class clazz) {
        Map<String, DataEntity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }
        //获取数据项前缀
        String nameKey = DataEntity.getDataKeyPrefix(signalId, clazz.getSimpleName());
        List<String> keyList = signalInfo.keySet().stream().filter(
                key -> key.startsWith(nameKey)
        ).collect(Collectors.toList());

        List<T> datas = new ArrayList<>();
        keyList.stream().forEach(
                key -> {
                    T data = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                    datas.add(data);
                }
        );

        if (!datas.isEmpty()) {
            return Optional.of(datas);
        } else {
            return Optional.empty();
        }
    }


    /**
     * 从缓存中获取一类数据项
     *
     * @param signalId
     * @param <T>
     * @return
     */
    public <T> Optional<Map<String, T>> getDataMap(String signalId, Class clazz) {
        Map<String, DataEntity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }


        //获取数据项前缀
        String nameKeyPrefix = DataEntity.getDataKeyPrefix(signalId, clazz.getSimpleName());
        Map<String, T> datas = new HashMap<>();
        signalInfo.keySet().stream().filter(
                key -> key.startsWith(nameKeyPrefix)
        ).forEach(
                key -> {
                    try {
                        T eachData = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                        datas.put(key.replace(nameKeyPrefix, ""), (eachData));
                    }catch (Exception e){
                        log.error("异常-{}解析类型{}", signalInfo.get(key).getData(), clazz.getSimpleName());
                    }
                }
        );

        if (datas.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(datas);
        }
    }

    ////////////////////判定路口是否手动配置阶段参数
    public boolean isStageManualConfig(CrossingService.CrossingBaseInfo crossingBaseInfo){
        Optional<StageManualConfig> stageManualConfigOp =
                getData(crossingBaseInfo.getControllerId(), Integer.valueOf(DEFAULT_ID),
                StageManualConfig.class);
        return stageManualConfigOp.isPresent();
    }

    public Optional<com.myweb.daa.areasignal.centralsystem.param.StageParam> getStageManualConfig(CrossingService.CrossingBaseInfo crossingBaseInfo, int centralStageNo){
        Optional<StageManualConfig> stageManualConfigOp = getData(crossingBaseInfo.getControllerId(), Integer.valueOf(DEFAULT_ID),
                StageManualConfig.class);
        if(!stageManualConfigOp.isPresent()){
            return Optional.empty();
        }

        if(stageManualConfigOp.get().getStageParamList() == null
                || stageManualConfigOp.get().getStageParamList().isEmpty()){
            return Optional.empty();
        }
        return stageManualConfigOp.get().getStageParamList().stream().filter(
                stageParam -> stageParam.getStageNo() == centralStageNo
        ).findAny();

    }
}
