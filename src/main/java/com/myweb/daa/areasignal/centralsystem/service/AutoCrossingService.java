package com.myweb.daa.areasignal.centralsystem.service;

import com.alibaba.fastjson.JSONObject;
import com.les.ads.ds.ReturnEntity;
import com.les.ads.ds.enums.LaneAttributeType;
import com.les.ads.ds.enums.LaneFeatureType;
import com.les.ads.ds.enums.LaneMovementType;
import com.les.ads.ds.gis.Lane;
import com.les.ads.ds.gis.arm.ArmSignalControllerDTO;
import com.les.ads.ds.gis.arm.CrossingDTO;
import com.myweb.daa.areasignal.centralsystem.param.DirectionType1049;
import com.myweb.daa.areasignal.centralsystem.param.LaneMovementTypeSort;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.groovy.RuleInterface;
import com.myweb.daa.areasignal.groovy.SelfDefinedRuleManager;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossParam;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LaneParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/4/21 10:36
 */
@Service
@Slf4j
public class AutoCrossingService {
    @Autowired
    private CrossingService crossingService;

    @Autowired
    private SystemIpService systemIpService;

    @Autowired
    private ControllerService controllerService;


    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    /**
     * 数据请求的ip地址
     */
    @Value("${global.basicSignalParamIp:null}")
    private static String basicSignalParamIp;

    @Autowired
    private SelfDefinedRuleManager selfDefinedRuleManager;

    @Autowired
    private RestTemplate restTemplate;


    @Autowired
    private CrossMapService crossMapService;

    @Autowired
    private HtSignalService htSignalService;

    /**
     * 获取数据基础URL
     *
     * @return
     */
    private String getBasicUrl() {
        if (basicSignalParamIp != null && !basicSignalParamIp.isEmpty()) {
            return "http://" + basicSignalParamIp;
        } else {
            return "http://" + GlobalConfigure.signalParamIp;
        }
    }

    /**
     * 获取数据基础URL
     *
     * @return
     */
    private String getSgpUrl() {
        return "http://" + GlobalConfigure.signalParamIp;
    }


    /**
     * 删除信号机数据项
     *
     * @param lesControllerId
     * @return
     */
    public boolean delController(String lesControllerId) {
        try {
            String basicUrl = getBasicUrl() + "/arm/signalController/" + lesControllerId;
            restTemplate.delete(basicUrl);
            return true;
        } catch (Exception e) {
            log.error("删除信号机{}出现异常{}", lesControllerId, e);
            return false;
        }
    }

    /**
     * 保存信号机
     *
     * @param armSignalControllerDTO
     * @return
     */
    public boolean saveController2Sgp(ArmSignalControllerDTO armSignalControllerDTO) {
        try {
            String basicUrl = getBasicUrl() + "/arm/signalController";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json;charset=utf-8"));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(armSignalControllerDTO), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(basicUrl, httpEntity, String.class);
            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                log.debug("结果-{}", responseEntity.getBody());
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("保存信号机{}出现异常{}", armSignalControllerDTO, e);
            return false;
        }
    }

    /**
     * 删除路口数据项
     *
     * @param lesCrossingId
     * @return
     */
    public boolean delCrossing(String lesCrossingId) {
        try {
            String basicUrl = getBasicUrl() + "/arm/crossing/" + lesCrossingId;
            restTemplate.delete(basicUrl);
            return true;
        } catch (Exception e) {
            log.error("删除路口数据项{}出现异常{}", lesCrossingId, e);
            return false;
        }
    }

    /**
     * 保存路口
     *
     * @param crossingDTO
     * @return
     */
    public boolean saveCrossing2Sgp(CrossingDTO crossingDTO) {
        try {
            String basicUrl = getBasicUrl() + "/arm/crossing";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json;charset=utf-8"));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(crossingDTO), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(basicUrl, httpEntity, String.class);
            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                log.debug("结果-{}", responseEntity.getBody());
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("保存路口{}出现异常{}", crossingDTO, e);
            return false;
        }
    }

    /**
     * 根据路口1049编号生成arm数据项
     *
     * @param cross1049
     * @param ruleId    规则id
     * @return
     */
    public ReturnEntity<?> saveCrossing(String cross1049, String ruleId, boolean reloadSetting) {
        Optional<CrossParam> crossParamOptional = ht1049SignalCacheService.getData(cross1049, 1, "0", CrossParam.class);
        if (!crossParamOptional.isPresent()) {
            return new ReturnEntity<>(false, "10001", "没有找到此编号的路口信息，请确认1049路口ID是否正常!", "");
        }

        Optional<RuleInterface> ruleOp = selfDefinedRuleManager.getRuleClass(ruleId);
        if (!ruleOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "没有找到规则引擎，请确认规则ID是否正确!", "");
        }

        RuleInterface ruleInterface = ruleOp.get();
        Optional<String> lesSignalIdOp = ruleInterface.change1049CrossToLesSignalId(cross1049);
        if (!lesSignalIdOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "使用规则引擎生成莱斯信号机ID失败，请确认规则引擎规则!", "");
        }

        Optional<String> manufacturerOp = ruleInterface.getManufacturer(cross1049);
        if (!manufacturerOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "使用规则引擎生成设备厂家失败，请确认规则引擎规则!", "");
        }

        Optional<String> officeIdOp = ruleInterface.getOfficeId(cross1049);
        if (!officeIdOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "使用规则引擎生成所属officeID失败，请确认规则引擎规则!", "");
        }

        Optional<String> signalTypeOp = ruleInterface.getSignalType(cross1049);
        if (!signalTypeOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "使用规则引擎生成所属设备类型失败，请确认规则引擎规则!", "");
        }

        String lesCrossId = lesSignalIdOp.get() + "1";
        //删除原先的信号机以及路口数据项
        delCrossing(lesCrossId);
        delController(lesSignalIdOp.get());

        //保存路口数据项
        CrossingDTO crossingDTO = crossingService.genCrossing(crossParamOptional.get(), lesCrossId, lesSignalIdOp.get(), officeIdOp.get());
        log.error("准备保存路口数据项-{}", crossingDTO);

        //保存信号机数据项
        ArmSignalControllerDTO armSignalControllerDTO = controllerService.genController(crossParamOptional.get(), lesCrossId, lesSignalIdOp.get(), officeIdOp.get(), manufacturerOp.get(),
                signalTypeOp.get(), crossingDTO);
        log.error("准备保存的信号机数据项-{}", armSignalControllerDTO);

        boolean result1 = saveCrossing2Sgp(crossingDTO);
        boolean result2 = saveController2Sgp(armSignalControllerDTO);

        if (result1 && result2) {
            //更新映射关系
            crossMapService.updateCrossMap(lesCrossId, cross1049);
            //参数是否需要重新加载
            if (reloadSetting) {
                //重新加载路口映射配置
                crossingService.loadCrossIdMap();
                systemIpService.updateSystemIp();
            }
            return new ReturnEntity<>(true, armSignalControllerDTO);
        } else {
            return new ReturnEntity<>(false, "10001", "保存出现异常，请排查!", armSignalControllerDTO);
        }
    }


    /**
     * 一键保存参数数据项
     *
     * @param ruleId
     * @return
     */
    public ReturnEntity<?> saveCrossings(String ruleId) {

        Optional<RuleInterface> ruleOp = selfDefinedRuleManager.getRuleClass(ruleId);
        if (!ruleOp.isPresent()) {
            return new ReturnEntity<>(false, "10001", "没有找到规则引擎，请确认规则ID是否正确!", "");
        }

        Optional<Map<String, Map<String, CrossParam>>> crossParamMap = ht1049SignalCacheService.getData(1, CrossParam.class);
        if (!crossParamMap.isPresent()) {
            return new ReturnEntity<>(false, "10001", "查找1049路口失败，请检查", "");
        }

        //生成路口ID
        List<String> cross1049s = new ArrayList<>();
        crossParamMap.get().values().forEach(
                crossDataMap -> {
                    crossDataMap.values().stream().forEach(
                            crossParam -> {
                                if (!cross1049s.contains(crossParam.getCrossID())) {
                                    cross1049s.add(crossParam.getCrossID());
                                }
                            }
                    );
                }
        );

        log.error("!!!!!!!!!!-步骤1, 生成信号机及路口数据项");
        List<ReturnEntity> returnEntities = new ArrayList<>();
        for (int i = 0; i < cross1049s.size(); i++) {
            log.error("准备处理1049路口-{}", cross1049s.get(i));
            try {
                log.error("!!!!!!!!!!-步骤1.1, 处理1049路口-{}", cross1049s.get(i));
                ReturnEntity<?> returnEntity = saveCrossing(cross1049s.get(i), ruleId, false);
                if (!returnEntity.isSuccess()) {
                    returnEntities.add(returnEntity);
                }
            } catch (Exception e) {
                log.error("1049路口{}生成处理异常-{}", cross1049s.get(i), e);
            }
        }

        log.error("!!!!!!!!!!-步骤2, 重新读取sgp信号机数据项");
        //从sgp重新读取路口以及信号机数据项
        try {
            controllerService.updateControllerFromSgp();
        } catch (Exception e) {
            log.error("重新加载信号机参数数据异常", e);
        }

        log.error("!!!!!!!!!!-步骤3, 重新读取sgp路口数据项");
        try {
            crossingService.updateCrossingFromSgp();
        } catch (Exception e) {
            log.error("重新加载路口参数数据异常", e);
        }

        //统一重新加载
        //重新加载路口映射配置
        log.error("!!!!!!!!!!-步骤4, 重新加载路口映射配置");
        try {
            crossingService.loadCrossIdMap();
            systemIpService.updateSystemIp();
        } catch (Exception e) {
            log.error("重新加载ip参数时异常", e);
        }

        log.error("!!!!!!!!!!-步骤5, 重新调看路口数据项");
        //重新调看路口的数据项
        try {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            crossIDs.stream().forEach(
                    crossID -> {
                        try {
                            Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(crossID);
                            if (crossingInfo.isPresent()) {
                                log.error("!!!!!!!!!!-步骤5.1, 重新调看路口{}数据项", crossID);
                                htSignalService.loadData(crossingInfo.get());
                            }
                        } catch (Exception e) {
                            log.error("调看路口数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );

        } catch (Exception e) {
            log.error("调看路口数据异常", e);
        }

        log.error("!!!!!!!!!!-步骤6, 生成路口渠化数据项");
        //生成渠化数据项
        {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            crossIDs.stream().forEach(
                    crossID -> {
                        try {
                            ReturnEntity<?> returnEntity = saveLanes(crossID);
                            log.error("!!!!!!!!!!-步骤6.1, 生成路口{}渠化数据项-{}", crossID, returnEntity);
                        } catch (Exception e) {
                            log.error("生成路口渠化数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );
        }

        log.error("!!!!!!!!!!-步骤7, 结束，数据返回");
        if (returnEntities.isEmpty()) {
            return new ReturnEntity<>(true, "所有路口生成成功");
        } else {
            return new ReturnEntity<>(false, "10001", "部分路口欧生成失败，请检查", returnEntities);
        }
    }


    /**
     * 保存路口车道数据项IAngela
     *
     * @param lanes
     * @return
     */
    public boolean saveLanes2Sgp(String crossIdLes, List<Lane> lanes) {
        try {
            String basicUrl = getSgpUrl() + "/arm/lane/" + crossIdLes;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json;charset=utf-8"));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(lanes), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(basicUrl, httpEntity, String.class);
            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                log.debug("结果-{}", responseEntity.getBody());
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("保存路口{}车道{}出现异常{}", crossIdLes, lanes, e);
            return false;
        }
    }

    /**
     * 1049车道转换成莱斯车道数据项
     *
     * @param laneParam
     * @return
     */
    public Lane getLane(String crossIdLes, LaneParam laneParam, int laneNo) {
        Lane lane = new Lane();
        lane.setId(crossIdLes + "-" + laneNo);
        lane.setCrossingId(crossIdLes);
        lane.setLaneNo(laneNo);
        lane.setDirection(
                DirectionType1049.parseCode(Integer.parseInt(laneParam.getDirection())).directionType());
        lane.setCharacter(LaneFeatureType.MOTORWAY);
        Optional<LaneAttributeType> laneAttributeTypeOptional = LaneAttributeType.parseCode(Integer.parseInt(laneParam.getAttribute()));
        lane.setAttribute(laneAttributeTypeOptional.isPresent() ? laneAttributeTypeOptional.get() : LaneAttributeType.OTHER);
        LaneMovementType laneMovementType = LaneMovementType.parseCode(Integer.parseInt(laneParam.getMovement()));
        lane.setMovement(laneMovementType);
        lane.setWidth(5.0f);
        lane.setIsRamp(false);
        return lane;
    }

    /**
     * 1049车道转换成莱斯车道数据项
     *
     * @return
     */
    public Lane getOutLane(String crossIdLes, int direction1049, int laneNo) {
        Lane lane = new Lane();
        lane.setId(crossIdLes + "-" + laneNo);
        lane.setCrossingId(crossIdLes);
        lane.setLaneNo(laneNo);
        lane.setDirection(
                DirectionType1049.parseCode(direction1049).directionType());
        lane.setCharacter(LaneFeatureType.MOTORWAY);
        lane.setAttribute(LaneAttributeType.EXIT_PORT);
        lane.setMovement(LaneMovementType.RF_OTHER);
        lane.setWidth(5.0f);
        lane.setIsRamp(false);
        return lane;
    }

    /**
     * 生成人行横道数据项
     *
     * @return
     */
    public Lane genPedLane(String crossIdLes, int direction1049, int laneNo) {
        Lane lane = new Lane();
        lane.setId(crossIdLes + "-" + laneNo);
        lane.setCrossingId(crossIdLes);
        lane.setLaneNo(laneNo);
        lane.setDirection(
                DirectionType1049.parseCode(direction1049).directionType());
        lane.setCharacter(LaneFeatureType.ZEBRA_CROSSING);
        lane.setAttribute(LaneAttributeType.OTHER);
        lane.setMovement(LaneMovementType.RF_OTHER);
        lane.setWidth(5.0f);
        lane.setIsRamp(false);
        return lane;
    }

    /**
     * 根据1049渠化配置生成莱斯路口数据项
     *
     * @param crossIdLes 莱斯路口ID
     * @return
     */
    public ReturnEntity<?> saveLanes(String crossIdLes) {
        Optional<Map<String, LaneParam>> laneParamMap = ht1049SignalCacheService.getData(crossIdLes, 1, LaneParam.class);
        if (!laneParamMap.isPresent()) {
            return new ReturnEntity<>(false, "10001", "没有找到此编号的路口渠化信息，请确认路口数据是否调看!", "");
        }

        List<LaneParam> laneParamOrgs = new ArrayList<>(laneParamMap.get().values());
        //只考虑机动车道数据项
        List<LaneParam> laneParams = laneParamOrgs.stream().filter(
                laneParam ->
                {
                    if (String.valueOf(LaneFeatureType.MOTORWAY.getCode()).equalsIgnoreCase(laneParam.getFeature())) {
                        return true;
                    } else if (String.valueOf(LaneFeatureType.OTHER.getCode()).equalsIgnoreCase(laneParam.getFeature())) {
                        LaneMovementType laneMovementType = LaneMovementType.parseCode(Integer.parseInt(laneParam.getMovement()));
                        if (laneMovementType.getCode() == LaneMovementType.RF_OTHER.getCode()) {
                            return false;
                        } else {
                            return true;
                        }
                    }
                    return false;
                }
        ).collect(Collectors.toList());

        List<Lane> lanes = new ArrayList<>();
        AtomicInteger laneNo = new AtomicInteger(1);
        //遍历进口车道
        List<LaneParam> enterLanes = laneParams.stream().filter(
                laneParam -> String.valueOf(LaneAttributeType.ENTER_PORT.getCode()).equalsIgnoreCase(laneParam.getAttribute())
        ).collect(Collectors.toList());
        //根据方向\流向进行排序
        enterLanes.sort(
                (lane1, lane2) -> {
                    int direction1 = Integer.parseInt(lane1.getDirection());
                    LaneMovementTypeSort laneMovementTypeSort1 = LaneMovementTypeSort.parseCode(Integer.parseInt(lane1.getMovement()));
                    int direction2 = Integer.parseInt(lane2.getDirection());
                    LaneMovementTypeSort laneMovementTypeSort2 = LaneMovementTypeSort.parseCode(Integer.parseInt(lane2.getMovement()));
                    if (direction1 != direction2) {
                        return direction1 - direction2;
                    } else {
                        //同一方向需要根据流向进行排序，莱斯根据顺时针方向进行编号
                        return laneMovementTypeSort2.sort() - laneMovementTypeSort1.sort();
                    }
                }
        );

        //保存路口数据项
        log.error("路口{}排序后原始车道数据项-{}", crossIdLes, enterLanes);

        //进口车道所有数据项
        List<Lane> lanesIn = enterLanes.stream().map(laneParam -> getLane(crossIdLes, laneParam, laneNo.getAndIncrement())).collect(Collectors.toList());
        log.error("路口{}进口车道数据项-{}", crossIdLes, lanesIn);

        //存在进口车道方向统计
        List<Integer> directions = new ArrayList<>();
        //各个方向车道数据项
        Map<Integer, Integer> defaultDirectionMap = new HashMap<>();
        enterLanes.stream().forEach(
                laneParam -> {
                    int direction1049 = Integer.parseInt(laneParam.getDirection());
                    if (!directions.contains(direction1049)) {
                        directions.add(direction1049);
                        defaultDirectionMap.put(direction1049, 1);
                    } else {
                        //车道数据+1
                        defaultDirectionMap.put(direction1049, defaultDirectionMap.get(direction1049) + 1);
                    }
                }
        );

        //原始出口车道
        Map<String, List<LaneParam>> outLaneDirMap = laneParams.stream().filter(
                laneParam -> String.valueOf(LaneAttributeType.EXIT_PORT.getCode()).equalsIgnoreCase(laneParam.getAttribute())
        ).collect(Collectors.groupingBy(LaneParam::getDirection));

        //存在某些方向只有出口车道，没有进口车道路口
        try {
            if (outLaneDirMap != null && !outLaneDirMap.isEmpty()) {
                outLaneDirMap.keySet().forEach(
                        outLaneDir -> {
                            int direction1049 = Integer.parseInt(outLaneDir);
                            if (!directions.contains(direction1049)) {
                                directions.add(direction1049);
                            }
                        }
                );
            }
        } catch (Exception e) {
            log.error("处理出口车道的时候数据异常-{}", outLaneDirMap);
        }

        //人行横道生成
        List<Lane> lanesPed = new ArrayList<>();
        directions.stream().forEach(
                direction -> {
                    lanesPed.add(genPedLane(crossIdLes, direction, laneNo.getAndIncrement()));
                }
        );
        log.error("路口{}人行横道车道数据项-{}", crossIdLes, lanesPed);

        //出口车道生成
        List<Lane> lanesOut = new ArrayList<>();
        directions.stream().forEach(
                direction -> {
                    //获取对向车道数据
                    Integer laneNumber = defaultDirectionMap.get((direction + 4) % 8);

                    //如果存在原始数据项
                    List<LaneParam> outLanesOrg = outLaneDirMap.get(String.valueOf(direction));

                    //如果存在原始出口车道数据，则默认使用原始数据
                    if (outLanesOrg != null && !outLanesOrg.isEmpty()) {
                        laneNumber = outLanesOrg.size();
                    } else {
                        //没有取到对象车道数据
                        if (laneNumber == null) {
                            laneNumber = 2;
                        } else {
                            //防止出现无出口车道
                            if (laneNumber - 1 > 1) {
                                laneNumber = laneNumber - 1;
                            }
                        }
                    }

                    //添加两条出口车道数据项
                    for (int i = 0; i < laneNumber; i++) {
                        lanesOut.add(getOutLane(crossIdLes, direction, laneNo.getAndIncrement()));
                    }
                }
        );
        log.error("路口{}出口车道数据项-{}", crossIdLes, lanesOut);

        lanes.addAll(lanesIn);
        lanes.addAll(lanesPed);
        lanes.addAll(lanesOut);
        boolean result1 = saveLanes2Sgp(crossIdLes, lanes);
        if (result1) {
            return new ReturnEntity<>(true, lanes);
        } else {
            return new ReturnEntity<>(false, "10001", "保存出现异常，请排查!", lanes);
        }
    }


    /**
     * 一键调看路口及保存车道数据数据项
     *
     * @return
     */
    public ReturnEntity<?> saveAllLanes() {
        log.error("!!!!!!!!!!-步骤5, 重新调看路口数据项");
        //重新调看路口的数据项
        try {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            AtomicInteger crossNumbers = new AtomicInteger(0);
            crossIDs.parallelStream().forEach(
                    crossID -> {
                        try {
                            Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(crossID);
                            if (crossingInfo.isPresent()) {
                                log.error("!!!!!!!!!!-步骤5.1, 重新调看路口{}数据项-当前已经调看-{}/{}", crossID, crossNumbers.get(), crossIDs.size());
                                htSignalService.loadData(crossingInfo.get());
                                crossNumbers.getAndIncrement();
                            }
                        } catch (Exception e) {
                            log.error("调看路口数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );

        } catch (Exception e) {
            log.error("调看路口数据异常", e);
        }

        log.error("!!!!!!!!!!-步骤6, 生成路口渠化数据项");
        //生成渠化数据项
        {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            AtomicInteger crossNumbers = new AtomicInteger(0);
            crossIDs.parallelStream().forEach(
                    crossID -> {
                        try {
                            log.error("!!!!!!!!!!-步骤6.1, 生成路口{}渠化数据项-当前已同步-{}/{}", crossID, crossNumbers.get(), crossIDs.size());
                            ReturnEntity<?> returnEntity = saveLanes(crossID);
                            crossNumbers.getAndIncrement();
                        } catch (Exception e) {
                            log.error("生成路口渠化数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );
        }

        log.error("!!!!!!!!!!-步骤7, 结束，数据返回");
        return new ReturnEntity<>(true, "所有路口生成成功");

    }


    /**
     * 一键调看路口及保存车道数据数据项
     *
     * @return
     */
    public ReturnEntity<?> saveAllLanesWithGetLaneParam() {
        log.error("!!!!!!!!!!-步骤5, 重新调看路口数据项");
        //重新调看路口的数据项
        try {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            AtomicInteger crossNumbers = new AtomicInteger(0);
            crossIDs.parallelStream().forEach(
                    crossID -> {
                        try {
                            Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(crossID);
                            if (crossingInfo.isPresent()) {
                                log.error("!!!!!!!!!!-步骤5.1, 重新调看路口{}渠化-当前已经调看-{}/{}", crossID, crossNumbers.get(), crossIDs.size());
                                htSignalService.loadCrossData(crossingInfo.get().getCrossingId(), "LaneParam", "0", 1);
                                crossNumbers.getAndIncrement();
                            }
                        } catch (Exception e) {
                            log.error("调看路口数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );

        } catch (Exception e) {
            log.error("调看路口数据异常", e);
        }

        log.error("!!!!!!!!!!-步骤6, 生成路口渠化数据项");
        //生成渠化数据项
        {
            Set<String> crossIDs = crossingService.getCrossingBaseInfoMap().keySet();
            AtomicInteger crossNumbers = new AtomicInteger(0);
            crossIDs.parallelStream().forEach(
                    crossID -> {
                        try {
                            log.error("!!!!!!!!!!-步骤6.1, 生成路口{}渠化数据项-当前已同步-{}/{}", crossID, crossNumbers.get(), crossIDs.size());
                            ReturnEntity<?> returnEntity = saveLanes(crossID);
                            crossNumbers.getAndIncrement();
                        } catch (Exception e) {
                            log.error("生成路口渠化数据异常-{}-{}", crossID, e.getMessage());
                        }
                    }
            );
        }

        log.error("!!!!!!!!!!-步骤7, 结束，数据返回");
        return new ReturnEntity<>(true, "所有路口生成成功");

    }

}
