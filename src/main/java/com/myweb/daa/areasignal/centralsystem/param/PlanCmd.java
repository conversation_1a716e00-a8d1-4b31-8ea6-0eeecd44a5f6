package com.myweb.daa.areasignal.centralsystem.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/24 10:12
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlanCmd {
    public static final String MqObjectId = "3002";

    private String signalControllerID;
    private int planNo;
    private int crossingSeqNo;

    /**
     * 分钟
     */
    private Integer duration;

    private List<Integer> stageNoList;

    private List<Integer> timeList;

    public String genMapKey() {
        return signalControllerID + "###" + crossingSeqNo;
    }
}
