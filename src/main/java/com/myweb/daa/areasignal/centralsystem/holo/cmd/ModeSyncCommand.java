package com.myweb.daa.areasignal.centralsystem.holo.cmd;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/2/13 14:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModeSyncCommand {

    /**
     * 表号
     * ModeSyncFirstPart(45, "控制方式检验表-前125个路口"),
     * ModeSyncSecondPart(46, "控制方式检验表-后125个路口"),
     * ModeSyncThirdPart(146, "控制方式检验表-后250个路口");
     */
    private Integer noTab;

    /**
     * 数据大小
     */
    private Integer numByte;

    /**
     * 区域号
     */
    private Integer noArea;


    /**
     * 控制方式号
     */
    private ArrayList<Integer> juncMode;

}
