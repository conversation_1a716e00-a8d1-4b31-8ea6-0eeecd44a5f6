package com.myweb.daa.areasignal.centralsystem.service;

import com.les.ads.ds.ReturnEntity;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.business.entity.Cross1049;
import com.myweb.daa.areasignal.business.entity.QCross1049;
import com.myweb.daa.areasignal.business.entity.QSignal1049;
import com.myweb.daa.areasignal.business.entity.Signal1049;
import com.myweb.daa.areasignal.centralsystem.dto.Cross1049Dto;
import com.myweb.daa.areasignal.centralsystem.dto.Signal1049Dto;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/2/9 14:45
 */
@Service
@Slf4j
public class Signal1049Service {


    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;


    /**
     * 读取莱斯信号机数据项
     *
     * @return
     */
    @GetMapping("/ext/signal1049")
    public ReturnEntity<?> getSignal1049(@RequestParam(required = false) String signalName) {
        List<Signal1049> signal1049s;

        if (signalName != null && !signalName.isEmpty()) {
            signal1049s = queryFactory.selectFrom(QSignal1049.signal10491)
                    .where(QSignal1049.signal10491.name.contains(signalName)).fetch();
        } else {
            signal1049s = queryFactory.selectFrom(QSignal1049.signal10491)
                    .fetch();
        }

        return new ReturnEntity<>(true, signal1049s);
    }


    /**
     * 存储信号机映射数据项
     *
     * @return
     */
    @PostMapping("/ext/signal1049")
    @Transactional
    public ReturnEntity<?> saveSignal1049(@RequestBody Signal1049Dto signal1049Dto) {

        if (signal1049Dto == null || signal1049Dto.getSignal1049() == null || signal1049Dto.getName() == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        long count = queryFactory.selectFrom(QSignal1049.signal10491)
                .where(QSignal1049.signal10491.signal1049.eq(signal1049Dto.getSignal1049())).fetchCount();
        if (count > 0) {
            return new ReturnEntity<>(false, "10001", "1049路口已经配置，请检查!", "");
        }

        Signal1049 signal1049 = Signal1049.builder().signal1049(signal1049Dto.getSignal1049())
                .name(signal1049Dto.getName()).build();
        Signal1049 save = repositoryDao.save(signal1049, Signal1049.class);

        //设置数据项
        return new ReturnEntity<>(true, save);
    }

    /**
     * 删除信号机映射数据项
     *
     * @return
     */
    @DeleteMapping("/ext/signal1049")
    @Transactional
    public ReturnEntity<?> delSignal1049(@RequestParam String id) {

        if (id == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        queryFactory.delete(QSignal1049.signal10491)
                .where(QSignal1049.signal10491.id.eq(id)).execute();
        return new ReturnEntity<>(true, "删除成功");
    }

}
