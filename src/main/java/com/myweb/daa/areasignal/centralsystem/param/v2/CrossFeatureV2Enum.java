package com.myweb.daa.areasignal.centralsystem.param.v2;

import com.les.ads.ds.enums.CrossingCharacter;
import com.les.ads.ds.enums.CrossingFacility;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.enums.v2
 * @author: whr
 * @description:
 * @date: 2024/7/12
 * @version: 1.0
 * @modifyHistory:
 */
public enum CrossFeatureV2Enum {

    CROSS_TYPE(1, "十字形"),
    T_TYPE(2, "T形"),
    Y_TYPE(3, "Y形"),
    MULTIROAD(4, "五岔路口"),
    RING(5, "环形交叉口_环岛"),
    PED(6, "行人过街"),
    PED_TWICE(62, "2次行人过街"),
    RAMP(7, "匝道"),
    RAMP_IN(71, "匝道-入口"),
    RAMP_OUT(72, "匝道-出口"),
    EXPRESSWAY(81, "快速路主路路段_交汇区"),
    OTHER(9, "其他");

    private final int code;
    private final String desc;

    CrossFeatureV2Enum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getCode(CrossingCharacter character, CrossingFacility facility) {
        int code1049 = OTHER.getCode();
        if (character == null || facility == null) {
            return code1049;
        }
        if (CrossingFacility.COMMON == facility) {
            switch (character) {
                case Ped_Crossing:
                    code1049 = PED.getCode();
                    break;
                case T_Type:
                    code1049 = T_TYPE.getCode();
                    break;
                case Cross_Type:
                    code1049 = CROSS_TYPE.getCode();
                    break;
                default:
                    break;
            }
        } else if (CrossingFacility.RAMP_IN == facility) {
            code1049 = RAMP_IN.getCode();
        } else if (CrossingFacility.RAMP_OUT == facility) {
            code1049 = RAMP_OUT.getCode();
        }
        return code1049;
    }

}
