package com.myweb.daa.areasignal.centralsystem.param;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description: /**
 * *      * ht
 * *      * 1		11	特殊控制-关灯   -》 25
 * *      * 2		12	特殊控制-全红   -》 26
 * *      * 3		13	特殊控制-全部黄闪  -》 24
 * *              14  步进   -》 5      //中控
 * *              15  跳相   -》 4      //中控
 * *      * 4		21	单点多时段定时控制  -》 23
 * *      * 5		22	单点感应控制  -》 22
 * *      * 6		23	单点自适应控制  -》 22
 * *      * 7		31	线协调控制 -》 21
 * *      * 8		41	区域协调控制 -》 21
 * *      * 9		51	干预控制-手动控制 -》 4
 * *      * 10		52	干预控制-锁定阶段控制 -》 4
 * *      * 11		53	干预控制-指定方案 -》4
 * *      60 - 公交优先周期优化
 * *      61 -阶段优化
 * *
 * @Date: create in 2023/3/18 11:06
 */
public enum P1049ControlMode {

    CLOSE_LAMP(11, "特殊控制-关灯"),
    ALL_RED(12, "特殊控制-全红"),
    YELLOW_FLASH(13, "特殊控制-全部黄闪"),
    SYSTEM_MANUAL(14, "中控-步进"),
    LOCK_FLOW(15, "中控-跳相"),
    ADAPTIVE_CONTROL(16, "系统优化控制"),

    SINGLE_CONTROL(20, "单点控制"),
    FIXED_CYCLE(21, "单点多时段定时控制"),
    ACTUATED_LOCAL_1(22, "单点感应控制"),
    ACTUATED_LOCAL_2(23, "单点自适应控制"),
    BUS_PRI(24, "公交优先"),
    PEDESTRIAN_CROSSING(25, "单点行人过街"),


    COORDINATE_LINE(31, "线协调控制"),
    COORDINATE_AREA(41, "区域协调控制"),
    LOCK_FLOW_MANUAL(51, "干预控制-手动控制"),
    LOCK_FLOW_STAGE(52, "干预控制-锁定阶段控制"),
    LOCK_FLOW_PLAN(53, "干预控制-指定方案"),

    LOCK_FLOWS(54, "干预控制-锁定流向"),

    HOLO(61, "中心优化控制");

    private int value;
    private String des;

    P1049ControlMode(int value, String des) {
        this.value = value;
        this.des = des;
    }

    public int value() {
        return this.value;
    }

    public String des() {
        return this.des;
    }

    public static Optional<P1049ControlMode> getType(int orgValue) {
        Optional<P1049ControlMode> messageTypeOptional = Arrays.asList(P1049ControlMode.values()).stream()
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();

        return messageTypeOptional;
    }


}
