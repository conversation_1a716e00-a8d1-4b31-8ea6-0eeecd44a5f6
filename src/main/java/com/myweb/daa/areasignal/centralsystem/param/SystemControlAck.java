package com.myweb.daa.areasignal.centralsystem.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/18 9:52
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemControlAck {
    public static final String MqObjectId = "4001";

    private String signalControllerID;
    private String signalControlerID;
    private int type;
    private int iden;
    private int ack;
    private int crossingSeqNo;
    private String msg;
}
