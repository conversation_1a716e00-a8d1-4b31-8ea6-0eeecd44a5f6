package com.myweb.daa.areasignal.centralsystem.holo.process;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.TabHolographicStageDto;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageCtrl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class HolographicStageDtoProcess {


    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private HtSignalController htSignalController;

    @Autowired
    private HtSignalService htSignalService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void holographicStageDtoProcess(TabHolographicStageDto tabHolographicStageDto) {
        log.error("收到全息实时优化-{}", tabHolographicStageDto);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabHolographicStageDto.getNoArea(),
                tabHolographicStageDto.getNoJunc());

        if (!signalInfoOp.isPresent()) {
            log.error("没有找到信号机进行全息实时优化-{}", tabHolographicStageDto);
            return;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalInfoOp.get().getSignalId());

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            log.error("没有找到路口进行全息实时优化-{}", tabHolographicStageDto);
            return;
        }

        {
            {

                //检查阶段号
                Optional<String> p1049StageNo = ht1049SignalCacheService.getP1049StageNo(signalInfoOp.get().getSignalId(), subJuncNo,
                        String.valueOf(tabHolographicStageDto.getNoStage()));
                if (!p1049StageNo.isPresent()) {
                    log.error("信号控制参数异常{},未知的阶段-{}", tabHolographicStageDto, tabHolographicStageDto.getNoStage());
                    return;
                }


                if ((signalBrandPort.brandCode() == SignalBrandPort.DW.brandCode())
                        || (signalBrandPort.brandCode() == SignalBrandPort.YL.brandCode())
                        || (SignalBrandPort.isBrandLesExtProtocol(signalBrandPort))) {
                    //准备优化数据项
                    StageCtrl stageCtrl = new StageCtrl();
                    stageCtrl.setCrossID(crossingInfoOp.get().getCrossingId());

                    stageCtrl.setStageNo(p1049StageNo.get());
                    stageCtrl.setGreen(tabHolographicStageDto.getLenStage());

                    log.error("全息实时优化准备发送-{}", stageCtrl);
                    JsonResult jsonResult = htSignalController.setStageCtrl(signalInfoOp.get().getSignalId(), subJuncNo, stageCtrl);
                    log.error("全息实时优化结果-{}", jsonResult);

                    //通知异常情况
                    if (!jsonResult.isSuccess()) {
                        htSignalService.notifyHoloAck(signalInfoOp.get(), subJuncNo, jsonResult.getMessage());
                    }
                }
            }
        }
    }

}
