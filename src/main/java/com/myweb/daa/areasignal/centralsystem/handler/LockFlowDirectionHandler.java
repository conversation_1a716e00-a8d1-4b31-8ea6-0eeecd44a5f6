package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.LockFlowDirection;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.LockFlowDirectionService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 13:26
 */
@Component
@Slf4j
public class LockFlowDirectionHandler implements MqMsgBaseHandler {

    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;
    @Autowired
    private LockFlowDirectionService lockFlowDirectionService;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return LockFlowDirection.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return LockFlowDirection.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return LockFlowDirection.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {

        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    LockFlowDirection lockFlowDirection = jsonObject.toJavaObject(LockFlowDirection.class);
                    lockFlowDirection.setSignalControllerID(controllerId);
                    log.error("准备指定信号机-{}阶段-{},数据项是-{}", controllerId, lockFlowDirection.getNoStage(), lockFlowDirection);

                    {

                         {

                             List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection> lockFlowDirections
                                     = lockFlowDirectionService.genLockFlowDirectionMsg(crossingInfoOp.get(), lockFlowDirection.getNoStage(), lockFlowDirection.getLenStage());

                             if(lockFlowDirections.isEmpty()){
                                 log.error("路口{}根据阶段{}生成锁定流向数据项异常", crossingInfoOp.get().getCrossingId(), lockFlowDirection.getNoStage());
                                 return;
                             }


                             P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Set)
                                    .object(lockFlowDirections)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection.class).build();
                            requestMsgs.add(p1049CentralSystemMsg);
                        }
                    }
                }
        );

        //如果没有生成配置数据项
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送锁定阶段命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<LockFlowDirection> lockFlowDirections = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        LockFlowDirection lockFlowDirection = jsonObject.toJavaObject(LockFlowDirection.class);
                        lockFlowDirection.setSignalControllerID(requestMessage.getSignalControllerID());
                        lockFlowDirections.add(lockFlowDirection);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }


        AtomicBoolean resultAtomic = new AtomicBoolean(true);
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        lockFlowDirections.stream().forEach(
                lockFlowDirection -> {

                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(lockFlowDirection.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        return;
                    }

                    //根据莱斯阶段编号，转换成1049阶段编号
                    Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(requestMessage.getSignalControllerID()
                            , subJuncNo, String.valueOf(lockFlowDirection.getNoStage()));
                    if (!p1049StageNoOp.isPresent()) {
                        return;
                    }

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("路口-{}锁定阶段{}请求应答异常-{}",
                                            crossingInfoOp.get().getCrossingId(), lockFlowDirection.getNoStage(), firstMsgData.get());
                                    resultAtomic.set(false);
                                    return;
                                }

                                log.error("路口-{}锁定结果-{}", crossingInfoOp.get().getCrossingId(), firstMsgData.get());

                                //记录当前锁定信息
                                ht1049SignalCacheService.updateLockStage(crossingInfoOp.get().getCrossingId(), p1049StageNoOp.get());

                            }
                    );
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        //判定是否失败
        if (!resultAtomic.get()) {
            mqMessageResponse = P1049HelpUtils.buildErrorMqResponseMsg(requestMessage, "9001", "信号机响应指定相位失败",
                    requestMessage.getObjectList());
        }

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<LockFlowDirection> lockFlowDirections = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        LockFlowDirection lockFlowDirection = jsonObject.toJavaObject(LockFlowDirection.class);
                        lockFlowDirection.setSignalControllerID(requestMessage.getSignalControllerID());

                        //修正子路口数据项
                        if (lockFlowDirection.getCrossingSeqNo() < 1 || lockFlowDirection.getCrossingSeqNo() > 4) {
                            lockFlowDirection.setCrossingSeqNo(1);
                        }
                        lockFlowDirections.add(lockFlowDirection);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        lockFlowDirections.stream().forEach(
                lockFlowDirection -> {
                    //指定阶段
                    {
                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(lockFlowDirection.getSignalControllerID())
                                .signalControlerID(lockFlowDirection.getSignalControllerID())
                                .crossingSeqNo(lockFlowDirection.getCrossingSeqNo())
                                .ack(1)
                                .iden(lockFlowDirection.getNoStage())
                                .type(0).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }




}
