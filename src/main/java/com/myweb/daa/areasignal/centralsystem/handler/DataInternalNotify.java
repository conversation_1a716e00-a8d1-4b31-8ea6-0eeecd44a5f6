package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.centralsystem.dbsave.SgpDbSaveEntityProcess;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Infos;
import com.myweb.daa.areasignal.htbussiness.process.P1049InfosProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description: 此类用于中心机调看数据完成之后，内存数据发布通知进行数据更新
 * @Date: create in 2022/4/16 10:22
 */
@Service
@Slf4j
public class DataInternalNotify {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private P1049InfosProcess p1049InfosProcess;

    @Autowired
    private SgpDbSaveEntityProcess sgpDbSaveEntityProcess;

    /**
     * 单数据通知
     *
     * @param controllerId
     * @param subJuncNo
     * @param no
     * @param object
     */
    public void dataNotify(String controllerId, int subJuncNo, String no, Object object) {
        //内部数据项变更及存储
        {
            //发布数据项进行异步数据保存
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
            if (crossingInfoOp.isPresent()) {

                List<P1049Entity> controlerInfos = new ArrayList<>();
                //获取基础数据项
                controlerInfos.add(
                        P1049Entity.builder().type(object.getClass().getSimpleName())
                                .no(String.valueOf(no))
                                .subJuncNo(subJuncNo)
                                .ip(crossingInfoOp.get().getAddress1049Ip())
                                .data(JSONObject.toJSONString(object)).build());

                P1049Infos p1049Infos = P1049Infos.builder().signalId(
                        crossingInfoOp.get().getCrossingId()
                ).controlerInfos(controlerInfos).build();
                messagePublisher.publishMessage(p1049Infos);
            } else {
                log.error("未找到数据项对应的路口id,信号机-[{}],子路口-[{}]，数据项-no-[{}],数据-[{}]", controllerId,
                        subJuncNo, no, object);
            }
        }
    }

    /**
     * 批量数据通知时，数据类型必定是同类数据项
     *
     * @param controllerId
     * @param subJuncNo
     * @param nos
     * @param objects
     */
    public void datasNotify(String controllerId, int subJuncNo, List<String> nos, List<Object> objects) {

        if (objects == null || objects.isEmpty()) {
            return;
        }

        if (nos == null || nos.isEmpty()) {
            return;
        }

        if (nos.size() != objects.size()) {
            log.error("数据长度不一致，数据项对应的路口id,信号机-[{}],子路口-[{}]，数据项-nos-[{}],数据-[{}]", controllerId,
                    subJuncNo, nos, objects);
            return;
        }

        //内部数据项变更及存储
        {
            //发布数据项进行异步数据保存
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
            if (crossingInfoOp.isPresent()) {

                List<P1049Entity> controlerInfos = new ArrayList<>();

                for (int i = 0; i < objects.size(); i++) {
                    //获取基础数据项
                    controlerInfos.add(
                            P1049Entity.builder().type(objects.get(i).getClass().getSimpleName())
                                    .no(String.valueOf(nos.get(i)))
                                    .subJuncNo(subJuncNo)
                                    .ip(crossingInfoOp.get().getAddress1049Ip())
                                    .data(JSONObject.toJSONString(objects.get(i))).build());

                }

                P1049Infos p1049Infos = P1049Infos.builder().signalId(
                        crossingInfoOp.get().getCrossingId()
                ).controlerInfos(controlerInfos).build();
                messagePublisher.publishMessage(p1049Infos);
            } else {
                log.error("未找到数据项对应的路口id,信号机-[{}],子路口-[{}]，数据项-nos-[{}],数据-[{}]", controllerId,
                        subJuncNo, nos, objects);
            }
        }
    }


    /**
     * 删除原始阶段数据项
     *
     * @param crossId
     * @param subJuncNo
     */
    public void deleteOldInfoSync(String crossId, int subJuncNo, Class type) {
        try {
            p1049InfosProcess.deleteP1049Infos(crossId, subJuncNo, type.getSimpleName());
        } catch (Exception e) {
            log.error("数据操作异常2", e);
        }
    }

    /**
     * 删除sgp调度数据项
     *
     * @param controllerId
     */
    public void deleteSgpScheduleSync(String controllerId) {
        try {
            sgpDbSaveEntityProcess.deleteData("schedule", controllerId);
        } catch (Exception e) {
            log.error("数据操作异常3", e);
        }
    }

    /**
     * 批量数据通知时，数据类型必定是同类数据项---同步数据存储
     *
     * @param controllerId
     * @param subJuncNo
     * @param nos
     * @param objects
     */
    public void datasNotifySync(String controllerId, int subJuncNo, List<String> nos, List<Object> objects) {
        try {

            if (objects == null || objects.isEmpty()) {
                return;
            }

            if (nos == null || nos.isEmpty()) {
                return;
            }

            if (nos.size() != objects.size()) {
                log.error("数据长度不一致，数据项对应的路口id,信号机-[{}],子路口-[{}]，数据项-nos-[{}],数据-[{}]", controllerId,
                        subJuncNo, nos, objects);
                return;
            }

            //内部数据项变更及存储
            {
                //发布数据项进行异步数据保存
                Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
                if (crossingInfoOp.isPresent()) {

                    List<P1049Entity> controlerInfos = new ArrayList<>();

                    for (int i = 0; i < objects.size(); i++) {
                        //获取基础数据项
                        controlerInfos.add(
                                P1049Entity.builder().type(objects.get(i).getClass().getSimpleName())
                                        .no(String.valueOf(nos.get(i)))
                                        .subJuncNo(subJuncNo)
                                        .ip(crossingInfoOp.get().getAddress1049Ip())
                                        .data(JSONObject.toJSONString(objects.get(i))).build());

                    }

                    P1049Infos p1049Infos = P1049Infos.builder().signalId(
                            crossingInfoOp.get().getCrossingId()
                    ).controlerInfos(controlerInfos).build();
                    p1049InfosProcess.p1049InfoProcessSync(p1049Infos);
                } else {
                    log.error("未找到数据项对应的路口id,信号机-[{}],子路口-[{}]，数据项-nos-[{}],数据-[{}]", controllerId,
                            subJuncNo, nos, objects);
                }
            }
        } catch (Exception e) {
            log.error("数据操作异常4", e);
        }
    }

}
