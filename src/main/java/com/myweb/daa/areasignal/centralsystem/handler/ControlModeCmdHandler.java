package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.ControlModeCmd;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.utils.Utils;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 15:01
 */
@Component
@Slf4j
public class ControlModeCmdHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private TmpPlanService tmpPlanService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return ControlModeCmd.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return ControlModeCmd.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return ControlModeCmd.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    ControlModeCmd controlModeCmd = jsonObject.toJavaObject(ControlModeCmd.class);
                    controlModeCmd.setSignalControllerID(controllerId);

                    log.error("收到控制方式命令-{}", controlModeCmd);

                       {
                       //默认恢复控制
                       String controlMode = "00";
                       if(controlModeCmd.getMode() == LesControlMode.YELLOW_FLASH.value()
                               || controlModeCmd.getMode() == LesControlMode.ALL_RED.value()
                               || controlModeCmd.getMode() == LesControlMode.CLOSE_LAMP.value()){
                           controlMode = Utils.change21049Mode(String.valueOf(controlModeCmd.getMode()));
                       }

                       CrossCtrlInfo crossCtrlInfo = CrossCtrlInfo.builder()
                               .CrossID(crossingInfoOp.get().getCrossingId1049())
                               .controlMode(controlMode)
                               .planNo(0)
                               .build();

                       List<CrossCtrlInfo> crossCtrlInfos = new ArrayList<>();
                       crossCtrlInfos.add(crossCtrlInfo);

                        log.error("准备发送控制方式-{}", crossCtrlInfo);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(crossCtrlInfos)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(CrossCtrlInfo.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    }

                }
        );
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<ControlModeCmd> controlModeCmds = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        ControlModeCmd controlModeCmd = jsonObject.toJavaObject(ControlModeCmd.class);
                        controlModeCmd.setSignalControllerID(requestMessage.getSignalControllerID());
                        controlModeCmds.add(controlModeCmd);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        log.error("控制方式应答-{}", itemDatas);
        controlModeCmds.stream().forEach(
                controlModeCmd -> {
                    //指定阶段
                    {
                        int subJuncNo = 1;
                        String signalControllerID = requestMessage.getSignalControllerID();
                        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalControllerID,
                                subJuncNo);
                        if (!crossingInfoOp.isPresent()) {
                            return;
                        }
                        tmpPlanService.stopTmpPlan(crossingInfoOp.get().getCrossingId());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<ControlModeCmd> controlModeCmds = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        ControlModeCmd controlModeCmd = jsonObject.toJavaObject(ControlModeCmd.class);
                        controlModeCmd.setSignalControllerID(requestMessage.getSignalControllerID());

                        //修正子路口数据项
                        if (controlModeCmd.getCrossingSeqNo() < 1 || controlModeCmd.getCrossingSeqNo() > 4) {
                            controlModeCmd.setCrossingSeqNo(1);
                        }

                        controlModeCmds.add(controlModeCmd);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        controlModeCmds.stream().forEach(
                controlModeCmd -> {
                    //取消指定
                    {
                        Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(controlModeCmd.getSignalControllerID(), controlModeCmd.getCrossingSeqNo());

                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(controlModeCmd.getSignalControllerID())
                                .crossingSeqNo(controlModeCmd.getCrossingSeqNo())
                                .ack(crossingInfo.isPresent() ? (crossingInfo.get().isOnline() ? 1 : 2) : 2)
                                .iden(controlModeCmd.getMode())
                                .type(1).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
