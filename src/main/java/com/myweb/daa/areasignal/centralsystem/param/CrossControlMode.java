package com.myweb.daa.areasignal.centralsystem.param;


import com.les.ads.ds.signal.dto.ControlModeDTO;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 19:06
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossControlMode implements RealTimeMsgInterface {
    public static final String MqObjectId = "2003";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int mode;
    private int iden;
    private int crossingSeqNo;

    @Override
    public String generateDbUrl() {
        return "juncmode";
    }

    @Override
    public String getSgpUrl() {
        return "/controlMode/runtime/save";
    }

    @Override
    public Object transPushData() {
        ControlModeDTO controlModeDTO = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, ControlModeDTO.class);
        return controlModeDTO;
    }
}
