sequenceDiagram
    participant 系统 as NATS3.0系统
    participant 全息 as 全息控制系统
    participant 线控 as 线控系统
    participant 模块 as 协议适配器
    participant 华通 as 华通控制系统
    系统 ->> 模块: 下发临时方案指令(绿灯时长)
    模块 ->> 模块: 记录指令优先级临时方案
    全息 ->> 模块: 下发全息优化指令(？绿灯时长)
    模块 ->> 模块: 记录指令优先级全息控制
    线控 ->> 模块: 下发线控指令(绿灯时长)
    模块 ->> 模块: 记录指令优先级线控控制
    模块 ->> 模块: 根据当前优先级控制(临时方案>全息>线控)
    alt 优先级判定通过
        模块 ->> 模块: 适配器转换 (1-2-3-4 -> 201-202-203-204)
        华通 -->> 模块: 返回临时方案编号 (如231)
        模块 ->> 模块: 记录方案231阶段列表 (201, 202, 203, 204)
        华通 ->> 模块: 控制方式切换指令
        模块 ->> 模块: 临时方案转换为临时方案、全息、线控
        模块 ->> 系统: 控制方式切换指令
        模块 ->> 全息: 控制方式切换指令
        模块 ->> 线控: 控制方式切换指令
        华通 ->> 模块: 运行方案切换至231
        模块 ->> 模块: 根据规则强制转换道123\124\125(根据下发控制方式，可配)方案
        模块 ->> 系统: 运行方案切换至124
        模块 ->> 全息: 运行方案切换至124(是否会影响？协议适配器广播231方案能否解决)
        模块 ->> 线控: 运行方案切换至124
        华通 ->> 模块: 推送相位灯态
        模块 ->> 模块: 根据方案124阶段列表 (201, 202, 203, 204)匹配放行阶段
        模块 -->> 系统: 推送阶段切换信息
        系统 ->> 系统: 结束流程
    else 优先级判定失败
        模块 ->> 模块: 结束流程
    end