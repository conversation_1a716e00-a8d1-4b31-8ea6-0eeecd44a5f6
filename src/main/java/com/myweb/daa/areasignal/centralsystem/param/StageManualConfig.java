package com.myweb.daa.areasignal.centralsystem.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StageManualConfig implements SgpTransAble {
    public static final String MqObjectId = "3013";

    private String signalControllerID;
    private int crossingSeqNo;

    private List<StageParam> stageParamList;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return StageManualConfig.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 0;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
