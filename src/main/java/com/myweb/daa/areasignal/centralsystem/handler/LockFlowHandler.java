package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.LockFlow;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 15:01
 */
@Component
@Slf4j
public class LockFlowHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return LockFlow.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return LockFlow.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return LockFlow.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    LockFlow lockFlow = jsonObject.toJavaObject(LockFlow.class);

                    log.error("收到锁定流向-{}", lockFlow);

                    if (lockFlow == null || lockFlow.getFlowNos() == null || lockFlow.getFlowNos().isEmpty()) {
                        log.error("异常的锁定流向数据-{}", lockFlow);
                        return;
                    }



                }
        );
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<LockFlow> lockFlows = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        LockFlow lockFlow = jsonObject.toJavaObject(LockFlow.class);
                        lockFlows.add(lockFlow);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        log.error("控制返回数据项-{}", itemDatas);
        lockFlows.stream().forEach(
                lockFlow -> {
                    //指定阶段
                    {

                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<LockFlow> lockFlows = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        LockFlow lockFlow = jsonObject.toJavaObject(LockFlow.class);
                        lockFlow.setSignalControllerID(requestMessage.getSignalControllerID());
                        lockFlows.add(lockFlow);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        lockFlows.stream().forEach(
            lockFlow -> {
                //取消指定
                {
                    systemControlAcks.add(SystemControlAck.builder().signalControllerID(lockFlow.getSignalControllerID())
                            .crossingSeqNo(lockFlow.getCrossingSeqNo())
                            .ack(1)
                            .iden(0)
                            .type(9)
                            .msg(JSONObject.toJSONString(lockFlow)).build());
                }
            }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
