package com.myweb.daa.areasignal.centralsystem.utils;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/4/20 17:27
 */
public class SgpUtils {

    /**
     * 根据标准信号机ID获取区域ID
     * * 获取标准的信号机编号比如 320100YL01214
     * * 320100 -- 前六位表示城市编码
     * * YL -- 中间表示品牌
     * * 01 -- 区域号
     * * 214 -- 路口号
     *
     * @param signalId
     * @return
     */
    public static Integer getNoAreaFromStandSignalId(String signalId) {
        return Integer.parseInt(signalId.substring(8, 10));
    }

    /**
     * 根据标准信号机ID获取城市编码
     * * 获取标准的信号机编号比如 320100YL01214
     * * 320100 -- 前六位表示城市编码
     * * YL -- 中间表示品牌
     * * 01 -- 区域号
     * * 214 -- 路口号
     *
     * @param signalId
     * @return
     */
    public static Integer getNoJuncFromStandSignalId(String signalId) {
        return Integer.parseInt(signalId.substring(10));
    }

    /**
     * 根据标准信号机ID获取城市编码
     * * 获取标准的信号机编号比如 320100YL01214
     * * 320100 -- 前六位表示城市编码
     * * YL -- 中间表示品牌
     * * 01 -- 区域号
     * * 214 -- 路口号
     *
     * @param signalId
     * @return
     */
    public static String getCityCodeFromStandSignalId(String signalId) {
        return (signalId.substring(0, 6));
    }
}
