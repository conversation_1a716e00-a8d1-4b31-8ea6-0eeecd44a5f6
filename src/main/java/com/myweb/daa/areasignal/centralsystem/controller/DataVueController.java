package com.myweb.daa.areasignal.centralsystem.controller;

import com.les.ads.ds.ReturnEntity;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.business.entity.*;
import com.myweb.daa.areasignal.centralsystem.dto.CrossIdMapDto;
import com.myweb.daa.areasignal.centralsystem.dto.SignalIdMapDto;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SystemIpService;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.monitor.P1049SystemLogMonitor;
import com.myweb.daa.areasignal.monitor.P1049SystemMonitor;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossParam;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalController;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/4/8 15:52
 */
@RestController
@RequestMapping("/data")
@Slf4j
public class DataVueController {
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private ControllerService controllerService;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private SystemIpService systemIpService;

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private P1049SystemMonitor p1049SystemMonitor;

    @Autowired
    private P1049SystemLogMonitor p1049SystemLogMonitor;

    /**
     * 读取莱斯路口数据项
     *
     * @return
     */
    @GetMapping("/lesCrossing")
    public ReturnEntity<?> getLesCrossing(@RequestParam(required = false) String name) {
        List<CrossingService.CrossingBaseInfo> crossingBaseInfos = crossingService.getCrossingBaseInfoMap().values().stream().collect(Collectors.toList());
        if (name != null && !name.isEmpty()) {
            crossingBaseInfos = crossingBaseInfos.stream().filter(
                    crossingBaseInfo -> crossingBaseInfo.getName() != null && crossingBaseInfo.getName().contains(name)
            ).collect(Collectors.toList());
        }
        return new ReturnEntity<>(true,
                crossingBaseInfos.stream().sorted((info1, info2) -> info1.getCrossingId().compareToIgnoreCase(info2.getCrossingId()))
                        .collect(Collectors.toList()));
    }

    /**
     * 读取莱斯信号机数据项
     *
     * @return
     */
    @GetMapping("/lesController")
    public ReturnEntity<?> getLesController(@RequestParam(required = false) String name) {
        List<ControllerService.SignalBaseInfo> signalBaseInfos = controllerService.getSignalBaseInfoMap().values().stream().collect(Collectors.toList());
        if (name != null && !name.isEmpty()) {
            signalBaseInfos = signalBaseInfos.stream().filter(
                    signalBaseInfo -> signalBaseInfo.getName() != null && signalBaseInfo.getName().contains(name)
            ).collect(Collectors.toList());
        }
        return new ReturnEntity<>(true, signalBaseInfos.stream().sorted((info1, info2) -> info1.getSignalId().compareToIgnoreCase(info2.getSignalId()))
                .collect(Collectors.toList()));
    }


    /**
     * 根据名字以及是否过滤非映射数据项
     *
     * @param name
     * @return
     */
    public List<SignalIdMapDto> getSignalIdMapDtos(String name, boolean filterNonMap) {
        List<SignalIdMapDto> signalIdMapDtos = new ArrayList<>();
        Optional<Map<String, Map<String, SignalController>>> ht1049SignalCacheServiceData = ht1049SignalCacheService.getData(0, SignalController.class);
        if (ht1049SignalCacheServiceData.isPresent()) {
            List<SignalController> signalControlerList = new ArrayList<>();

            Map<String, Map<String, SignalController>> stringMapMap = ht1049SignalCacheServiceData.get();
            //过滤掉进行转换后的数据项
            stringMapMap.keySet().stream().filter(
                    id -> {
                        SignalBrandPort[] values = SignalBrandPort.values();
                        for (int i = 0; i < values.length; i++) {
                            if (id.contains(values[i].name())) {
                                return false;
                            }
                        }
                        return true;
                    }
            ).forEach(
                    id -> {
                        List<SignalController> signalControlers = stringMapMap.get(id).values().stream().collect(Collectors.toList());
                        if (name != null && !name.isEmpty()) {
                            signalControlers = signalControlers.stream().filter(
                                    signalControler -> signalControler != null && signalControler.getSignalControllerID().contains(name)
                            ).collect(Collectors.toList());
                        }
                        signalControlerList.addAll(signalControlers);
                    }
            );

            signalControlerList.stream().forEach(
                    signalControler -> {
                        //原始信号机数据项会存在数据库中，虽然传递的是信号机编号
                        Optional<CrossParam> crossParamOp = Optional.empty();
                        if (signalControler.getCrossIDList() != null
                                && signalControler.getCrossIDList().getCrossID() != null
                                && !signalControler.getCrossIDList().getCrossID().isEmpty()) {
                            //原始路口数据项会存在数据库中，虽然传递的是路口编号
                            String crossId = signalControler.getCrossIDList().getCrossID().get(0);
                            crossParamOp = ht1049SignalCacheService.getData(crossId, 1, "0", CrossParam.class);
                        }
                        SignalIdMapDto crossIdMapDto = SignalIdMapDto.builder()
                                .signalId1049(signalControler.getSignalControllerID())
                                .crossName1049(crossParamOp.isPresent() ? crossParamOp.get().getCrossName() : "未知信号机路口")
                                .mapped(false).build();
                        signalIdMapDtos.add(crossIdMapDto);
                    }
            );



        }

        //用户手动输入的信号机id数据项
        List<Signal1049> signal1049s = queryFactory.selectFrom(QSignal1049.signal10491)
                .fetch();
        if(signal1049s != null && !signal1049s.isEmpty()){
            signal1049s.stream().forEach(
                    signal1049 -> {
                        SignalIdMapDto signalIdMapDto = SignalIdMapDto.builder()
                                .signalId1049(signal1049.getSignal1049())
                                .crossName1049(signal1049.getName())
                                .mapped(false).build();
                        signalIdMapDtos.add(signalIdMapDto);
                    }
            );
        }

        signalIdMapDtos.stream().sorted((info1, info2) -> info1.getSignalId1049().compareToIgnoreCase(info2.getSignalId1049()))
                .collect(Collectors.toList());


        //查询所有的映射关系数据项
        List<SignalIdMap> signalIdMaps = queryFactory.selectFrom(QSignalIdMap.signalIdMap)
                .fetch();
        Set<String> mappedSignalId1049s = signalIdMaps.stream().map(
                signalIdMap -> signalIdMap.getSignalId1049()
        ).collect(Collectors.toSet());


        if (filterNonMap) {
            List<SignalIdMapDto> collected = signalIdMapDtos.stream().filter(
                    signalIdMapDto -> {
                        if (mappedSignalId1049s.contains(signalIdMapDto.getSignalId1049())) {
                            signalIdMapDto.setMapped(true);
                            return false;
                        } else {
                            return true;
                        }
                    }
            ).collect(Collectors.toList());
            return collected;
        } else {
            signalIdMapDtos.stream().forEach(
                    signalIdMapDto -> {
                        if (mappedSignalId1049s.contains(signalIdMapDto.getSignalId1049())) {
                            signalIdMapDto.setMapped(true);
                        }
                    }
            );

            return signalIdMapDtos;

        }
    }

    @GetMapping("/signal1049")
    @Transactional
    @ApiOperation(value = "信号机参数")
    public ReturnEntity<?> getSignals(@RequestParam(required = false) String name) {

        List<SignalIdMapDto> signalIdMapDtos = getSignalIdMapDtos(name, false);
        return new ReturnEntity<>(true, signalIdMapDtos);
    }

    @GetMapping("/signal1049NonMap")
    @Transactional
    @ApiOperation(value = "信号机参数")
    public ReturnEntity<?> getSignalsNonMap(@RequestParam(required = false) String name) {

        List<SignalIdMapDto> signalIdMapDtos = getSignalIdMapDtos(name, true);
        return new ReturnEntity<>(true, signalIdMapDtos);
    }


    /**
     * 按照名称进行数据过滤
     *
     * @param name
     * @param filterNonMap
     * @return
     */
    public List<CrossParam> getCrossParamByName(String name, boolean filterNonMap) {
        List<CrossParam> crossParamList = new ArrayList<>();
        Optional<Map<String, Map<String, CrossParam>>> ht1049SignalCacheServiceData = ht1049SignalCacheService.getData(1, CrossParam.class);
        if (ht1049SignalCacheServiceData.isPresent()) {
            Map<String, Map<String, CrossParam>> stringMapMap = ht1049SignalCacheServiceData.get();
            //过滤掉进行转换后的数据项
            stringMapMap.keySet().stream().filter(
                    id -> {
                        //特殊处理 重庆HK 开头的数据
                        if (id.startsWith(SignalBrandPort.HK.name())) {
                            return true;
                        }

                        SignalBrandPort[] values = SignalBrandPort.values();
                        for (int i = 0; i < values.length; i++) {
                            if (id.contains(values[i].name())) {
                                return false;
                            }
                        }
                        return true;
                    }
            ).forEach(
                    id -> {
                        List<CrossParam> crossParams = stringMapMap.get(id).values().stream().collect(Collectors.toList());
                        if (name != null && !name.isEmpty()) {
                            crossParams = crossParams.stream().filter(
                                    crossParam -> crossParam != null && crossParam.getCrossName().contains(name)
                            ).collect(Collectors.toList());
                        }
                        crossParamList.addAll(crossParams);
                    }
            );

        }

        //用户手动输入的路口id数据项
        List<Cross1049> cross1049s = queryFactory.selectFrom(QCross1049.cross10491)
                .fetch();
        if(cross1049s != null && !cross1049s.isEmpty()){
            cross1049s.stream().forEach(
                    cross1049 -> {
                        CrossParam crossParam = CrossParam.builder()
                                .CrossID(cross1049.getCross1049()).build();
                        crossParam.setCrossName(cross1049.getName());
                        crossParamList.add(crossParam);
                    }
            );
        }

        try {
            List<CrossIdMap> crossIdMaps = queryFactory.selectFrom(QCrossIdMap.crossIdMap)
                    .fetch();
            Map<String, String> mapped1049Cross = new ConcurrentHashMap<>();
            crossIdMaps.stream().forEach(
                    crossIdMap -> {
                        mapped1049Cross.put(crossIdMap.getCrossId1049(), "");
                    }
            );

            crossParamList.stream().forEach(
                    crossParam -> {
                        crossParam.setMapped(mapped1049Cross.containsKey(crossParam.getCrossID()));
                    }
            );
        } catch (Exception e) {
            log.error("设置路口是否映射异常", e);
        }


        //是否需要过滤非映射
        if (filterNonMap) {
            List<CrossParam> collectNonMapped = crossParamList.stream().filter(
                    crossParam -> !crossParam.isMapped()
            ).collect(Collectors.toList());
            //自排序
            collectNonMapped.stream().sorted((info1, info2) -> info1.getCrossID().compareToIgnoreCase(info2.getCrossID()))
                    .collect(Collectors.toList());
            return collectNonMapped;
        } else {
            //自排序
            crossParamList.stream().sorted((info1, info2) -> info1.getCrossID().compareToIgnoreCase(info2.getCrossID()))
                    .collect(Collectors.toList());
            return crossParamList;
        }
    }

    @GetMapping("/crossing1049")
    @Transactional
    @ApiOperation(value = "路口参数")
    public ReturnEntity<?> getCrossings(@RequestParam(required = false) String name) {
        List<CrossParam> crossParamList = getCrossParamByName(name, false);
        return new ReturnEntity<>(true, crossParamList);
    }

    @GetMapping("/crossing1049NonMap")
    @Transactional
    @ApiOperation(value = "路口参数")
    public ReturnEntity<?> getCrossingsNonMap(@RequestParam(required = false) String name) {
        List<CrossParam> crossParamList = getCrossParamByName(name, true);
        return new ReturnEntity<>(true, crossParamList);
    }

    /**
     * 读取莱斯路口数据项
     *
     * @return
     */
    @GetMapping("/crossMap")
    public ReturnEntity<?> getCrossMap(@RequestParam(required = false) String lesCrossId) {
        List<CrossIdMap> crossIdMaps;

        if (lesCrossId != null && !lesCrossId.isEmpty()) {
            crossIdMaps = queryFactory.selectFrom(QCrossIdMap.crossIdMap)
                    .where(QCrossIdMap.crossIdMap.lesCrossId.contains(lesCrossId)).fetch();
        } else {
            crossIdMaps = queryFactory.selectFrom(QCrossIdMap.crossIdMap)
                    .fetch();
        }

        List<CrossIdMapDto> crossIdMapDtoList = new ArrayList<>();
        if (crossIdMaps != null && !crossIdMaps.isEmpty()) {
            crossIdMaps.stream().forEach(
                    crossIdMap -> {
                        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossIdMap.getLesCrossId());

                        //原始路口数据项会存在数据库中，虽然传递的是路口编号
                        Optional<CrossParam> crossParamOp = ht1049SignalCacheService.getData(crossIdMap.getCrossId1049(), 1, "0", CrossParam.class);

                        CrossIdMapDto crossIdMapDto = CrossIdMapDto.builder()
                                .id(crossIdMap.getId())
                                .crossId1049(crossIdMap.getCrossId1049())
                                .crossName1049(crossParamOp.isPresent() ? crossParamOp.get().getCrossName() : "未知1049路口")
                                .lesCrossId(crossIdMap.getLesCrossId())
                                .lesCorssName(crossingInfoOp.isPresent() ? crossingInfoOp.get().getName() : "未知路口")
                                .lesSignalId(crossingInfoOp.isPresent() ? crossingInfoOp.get().getControllerId() : "9999999999").build();
                        crossIdMapDtoList.add(crossIdMapDto);
                    }
            );
        }

        return new ReturnEntity<>(true, crossIdMapDtoList.stream().sorted((info1, info2) -> info1.getLesCrossId().compareToIgnoreCase(info2.getLesCrossId()))
                .collect(Collectors.toList()));
    }

    /**
     * 存储路口映射数据项
     *
     * @return
     */
    @PostMapping("/crossMap")
    @Transactional
    public ReturnEntity<?> saveCrossMap(@RequestBody CrossIdMapDto crossIdMapDto) {

        if (crossIdMapDto == null || crossIdMapDto.getCrossId1049() == null || crossIdMapDto.getLesCrossId() == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        long count = queryFactory.selectFrom(QCrossIdMap.crossIdMap)
                .where(QCrossIdMap.crossIdMap.lesCrossId.eq(crossIdMapDto.getLesCrossId())
                        .or(QCrossIdMap.crossIdMap.crossId1049.eq(crossIdMapDto.getCrossId1049()))).fetchCount();
        if (count > 0) {
            return new ReturnEntity<>(false, "10001", "路口已经配置，请检查!", "");
        }

        CrossIdMap crossIdMap = CrossIdMap.builder().lesCrossId(crossIdMapDto.getLesCrossId())
                .crossId1049(crossIdMapDto.getCrossId1049()).build();
        CrossIdMap save = repositoryDao.save(crossIdMap, CrossIdMap.class);

        crossingService.loadCrossIdMap();
        systemIpService.updateSystemIp();
        return new ReturnEntity<>(true, save);
    }

    /**
     * 删除路口映射数据项
     *
     * @return
     */
    @DeleteMapping("/crossMap")
    @Transactional
    public ReturnEntity<?> delCrossMap(@RequestParam String id) {

        if (id == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        queryFactory.delete(QCrossIdMap.crossIdMap)
                .where(QCrossIdMap.crossIdMap.id.eq(id)).execute();
        return new ReturnEntity<>(true, "删除成功");
    }


    /**
     * 查询映射数据项
     *
     * @param lesSignalId
     * @return
     */
    public List<SignalIdMapDto> getSignalIdMapDtos(String lesSignalId) {
        List<SignalIdMap> signalIdMaps;

        if (lesSignalId != null && !lesSignalId.isEmpty()) {
            signalIdMaps = queryFactory.selectFrom(QSignalIdMap.signalIdMap)
                    .where(QSignalIdMap.signalIdMap.lesSignalId.contains(lesSignalId)).fetch();
        } else {
            signalIdMaps = queryFactory.selectFrom(QSignalIdMap.signalIdMap)
                    .fetch();
        }


        List<SignalIdMapDto> signalIdMapDtos = new ArrayList<>();
        if (signalIdMaps != null && !signalIdMaps.isEmpty()) {
            signalIdMaps.stream().forEach(
                    signalIdMap -> {
                        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(signalIdMap.getLesSignalId());

                        //原始信号机数据项会存在数据库中，虽然传递的是信号机编号
                        Optional<SignalController> signalControlerOp = ht1049SignalCacheService.getData(signalIdMap.getSignalId1049(), 0, "0", SignalController.class);
                        Optional<CrossParam> crossParamOp = Optional.empty();
                        if (signalControlerOp.isPresent() && signalControlerOp.get().getCrossIDList() != null
                                && signalControlerOp.get().getCrossIDList().getCrossID() != null
                                && !signalControlerOp.get().getCrossIDList().getCrossID().isEmpty()) {
                            //原始路口数据项会存在数据库中，虽然传递的是路口编号
                            String crossId = signalControlerOp.get().getCrossIDList().getCrossID().get(0);
                            crossParamOp = ht1049SignalCacheService.getData(crossId, 1, "0", CrossParam.class);
                        }
                        SignalIdMapDto crossIdMapDto = SignalIdMapDto.builder()
                                .id(signalIdMap.getId())
                                .lesSignalId(signalIdMap.getLesSignalId())
                                .lesSignalName(signalInfoOp.isPresent() ? signalInfoOp.get().getName() : "未知信号机")
                                .signalId1049(signalIdMap.getSignalId1049())
                                .crossName1049(crossParamOp.isPresent() ? crossParamOp.get().getCrossName() : "未知信号机路口").build();
                        signalIdMapDtos.add(crossIdMapDto);
                    }
            );
        }

        signalIdMapDtos.stream().sorted((info1, info2) -> info1.getLesSignalId().compareToIgnoreCase(info2.getLesSignalId()))
                .collect(Collectors.toList());
        return signalIdMapDtos;
    }

    /**
     * 读取莱斯信号机数据项
     *
     * @return
     */
    @GetMapping("/signalMap")
    public ReturnEntity<?> getSignalMap(@RequestParam(required = false) String lesSignalId) {
        List<SignalIdMapDto> signalIdMapDtos = getSignalIdMapDtos(lesSignalId);
        return new ReturnEntity<>(true, signalIdMapDtos);
    }


    /**
     * 存储信号机映射数据项
     *
     * @return
     */
    @PostMapping("/signalMap")
    @Transactional
    public ReturnEntity<?> saveSignalMap(@RequestBody SignalIdMapDto signalIdMapDto) {

        if (signalIdMapDto == null || signalIdMapDto.getSignalId1049() == null || signalIdMapDto.getLesSignalId() == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        long count = queryFactory.selectFrom(QSignalIdMap.signalIdMap)
                .where(QSignalIdMap.signalIdMap.lesSignalId.eq(signalIdMapDto.getLesSignalId())
                        .or(QSignalIdMap.signalIdMap.signalId1049.eq(signalIdMapDto.getSignalId1049()))).fetchCount();
        if (count > 0) {
            return new ReturnEntity<>(false, "10001", "信号机已经配置，请检查!", "");
        }

        SignalIdMap signalIdMap = SignalIdMap.builder().lesSignalId(signalIdMapDto.getLesSignalId())
                .signalId1049(signalIdMapDto.getSignalId1049()).build();
        SignalIdMap save = repositoryDao.save(signalIdMap, SignalIdMap.class);

        //设置数据项
        controllerService.loadSignalIdMap();
        systemIpService.updateSystemIp();
        return new ReturnEntity<>(true, save);
    }

    /**
     * 删除信号机映射数据项
     *
     * @return
     */
    @DeleteMapping("/signalMap")
    @Transactional
    public ReturnEntity<?> delSignalMap(@RequestParam String id) {

        if (id == null) {
            return new ReturnEntity<>(false, "10001", "参数异常", "");
        }

        queryFactory.delete(QSignalIdMap.signalIdMap)
                .where(QSignalIdMap.signalIdMap.id.eq(id)).execute();
        return new ReturnEntity<>(true, "删除成功");
    }


    @GetMapping("/p1049")
    public ReturnEntity<?> getCurrentSystem() {
        return new ReturnEntity<>(true, "0", "获取成功", p1049SystemMonitor.invoke());
    }

    @GetMapping("/p1049Log")
    public ReturnEntity<?> getLogSystem() {
        return new ReturnEntity<>(true, "0", "获取成功", p1049SystemLogMonitor.invoke());
    }
}
