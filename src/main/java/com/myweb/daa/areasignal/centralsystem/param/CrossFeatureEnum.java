package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.enums.CrossingCharacter;
import com.les.ads.ds.enums.CrossingFacility;

/**
 * @projectName: nacos-server-parent
 * @packageName: com.les.its.scs.core.gb1049.enums
 * @author: whr
 * @description:
 * @date: 2022/10/1
 * @version: 1.0
 * @modifyHistory:
 */
public enum CrossFeatureEnum {

    CIRCLE("00", "环形交叉口", CrossingCharacter.MultiRoad_Type, CrossingFacility.COMMON),
    RAMP("11", "匝道出入口", CrossingCharacter.Ped_Crossing, CrossingFacility.RAMP_IN),
    PED_CROSSING("21", "路段", CrossingCharacter.Ped_Crossing, CrossingFacility.COMMON),
    T_TYPE("31", "T形路口", CrossingCharacter.T_Type, CrossingFacility.COMMON),
    Y_TYPE("32", "Y形路口", CrossingCharacter.T_Type, CrossingFacility.COMMON),
    E_T_TYPE("33", "错位T形路口", CrossingCharacter.T_Type, CrossingFacility.COMMON),
    E_Y_TYPE("34", "错位Y形路口", CrossingCharacter.T_Type, CrossingFacility.COMMON),
    CROSS_TYPE("41", "十字形路口", CrossingCharacter.Cross_Type, CrossingFacility.COMMON),
    E_CROSS_TYPE("43", "斜交十字形路口", CrossingCharacter.Cross_Type, CrossingFacility.COMMON),
    MULTIROAD_TYPE("51", "多路路口", CrossingCharacter.MultiRoad_Type, CrossingFacility.COMMON),
    UN_KNOWN("99", "其他", CrossingCharacter.Un_Known, CrossingFacility.COMMON);

    private String code;
    private String desc;

    private CrossingCharacter crossingCharacter;

    private CrossingFacility crossingFacility;

    CrossFeatureEnum(String code, String desc,
                     CrossingCharacter crossingCharacter,
                     CrossingFacility crossingFacility) {
        this.code = code;
        this.desc = desc;
        this.crossingCharacter = crossingCharacter;
        this.crossingFacility = crossingFacility;
    }

    public String code() {
        return code;
    }

    public CrossingCharacter crossingCharacter() {
        return crossingCharacter;
    }

    public CrossingFacility crossingFacility() {
        return crossingFacility;
    }

    /**
     * 解析数据项
     *
     * @param code
     * @return
     */
    public static CrossFeatureEnum parseType(String code) {
        if (code != null) {
            for (CrossFeatureEnum crossFeatureEnum : CrossFeatureEnum.values()) {
                if ((Integer.parseInt(code)) == (Integer.parseInt(crossFeatureEnum.code()))) {
                    return crossFeatureEnum;
                }
            }
        }
        return UN_KNOWN;
    }


}
