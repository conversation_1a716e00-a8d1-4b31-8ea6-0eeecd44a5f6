package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.LesManualControl;
import com.myweb.daa.areasignal.centralsystem.param.ManualControl;
import com.myweb.daa.areasignal.centralsystem.param.P1049ControlMode;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.ManualControlService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossCtrlInfoPublish;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.AdjustStage;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 15:01
 */
@Component
@Slf4j
public class ManualControlHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private CrossCtrlInfoPublish crossCtrlInfoPublish;

    @Autowired
    private ManualControlService manualControlService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return ManualControl.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return ManualControl.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return ManualControl.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }
        String crossingId = crossingInfoOp.get().getCrossingId();

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    ManualControl manualControl = jsonObject.toJavaObject(ManualControl.class);
                    manualControl.setSignalControllerID(controllerId);

                    log.error("收到步进命令-{}", manualControl);


                    //开始时间
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = new Date();
                    String startTime = simpleDateFormat.format(date);

                    if (manualControl.getIden() == LesManualControl.START.getCode()) {

                        Optional<Integer> curPlanNoOp = crossCtrlInfoPublish.getCurPlanNo(crossingId);
                        if(!curPlanNoOp.isPresent())
                        {
                            log.error("路口{}系统步进时获取当前运行方案异常,使用默认方案1", crossingId);
                            curPlanNoOp = Optional.of(1);
                        }

                        //开始系统控制
                        CrossCtrlInfo crossCtrlInfo = CrossCtrlInfo.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .controlMode(String.valueOf(P1049ControlMode.LOCK_FLOW_MANUAL.value()))
                                .planNo(curPlanNoOp.get())
                                .time(startTime)
                                .build();
                        List<CrossCtrlInfo> crossCtrlInfos = new ArrayList<>();
                        crossCtrlInfos.add(crossCtrlInfo);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(crossCtrlInfos)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(CrossCtrlInfo.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    } else if(manualControl.getIden() == LesManualControl.STEP.getCode())
                    {
                        //系统控制步进
                        AdjustStage adjustStage = AdjustStage.builder()
                                .crossId(crossingInfoOp.get().getCrossingId1049())
                                .stageNo(0)
                                .type(3)
                                .len(0).build();
                        List<AdjustStage> adjustStages = new ArrayList<>();
                        adjustStages.add(adjustStage);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(adjustStages)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(AdjustStage.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    }else {
                        //结束系统控制
                        CrossCtrlInfo crossCtrlInfo = CrossCtrlInfo.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .controlMode("00")
                                .planNo(0)
                                .time(startTime)
                                .build();
                        List<CrossCtrlInfo> crossCtrlInfos = new ArrayList<>();
                        crossCtrlInfos.add(crossCtrlInfo);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(crossCtrlInfos)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(CrossCtrlInfo.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);
                    }

                }
        );
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        log.error("准备发送步进阶段命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<ManualControl> manualControls = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        ManualControl manualControl = jsonObject.toJavaObject(ManualControl.class);
                        manualControls.add(manualControl);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        manualControls.stream().forEach(
                manualControl -> {
                    //系统步进
                    {
                        int subJuncNo = 1;
                        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(requestMessage.getSignalControllerID(),
                                subJuncNo);
                        if (!crossingInfoOp.isPresent()) {
                            return;
                        }
                        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());

                        itemDatas.stream().filter(
                                itemData -> itemData.getData() instanceof BaseMessage1049
                        ).forEach(
                                itemData -> {
                                    BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                    Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                    if (!firstMsgData.isPresent()) {
                                        return;
                                    }

                                    if (firstMsgData.get() instanceof SDO_Error1049) {
                                        log.error("请求应答异常-{}", firstMsgData.get());
                                        return;
                                    }

                                    log.error("路口-{}步进成功-应答-{}", crossingInfoOp.get().getCrossingId(),
                                            firstMsgData.get());
                                }
                        );
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<ManualControl> manualControls = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        ManualControl manualControl = jsonObject.toJavaObject(ManualControl.class);
                        manualControl.setSignalControllerID(requestMessage.getSignalControllerID());
                        manualControls.add(manualControl);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        manualControls.stream().forEach(
                manualControl -> {
                    //取消指定
                    {
                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(manualControl.getSignalControllerID())
                                .crossingSeqNo(manualControl.getCrossingSeqNo())
                                .ack(1)
                                .iden(manualControl.getIden())
                                .type(4).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
