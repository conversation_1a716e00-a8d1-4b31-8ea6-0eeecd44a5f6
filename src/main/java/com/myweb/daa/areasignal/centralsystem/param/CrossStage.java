package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.dto.StageDTO;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 19:06
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossStage implements RealTimeMsgInterface {
    public static final String MqObjectId = "2005";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int noOldStage;
    private int lenOldStage;
    private int noNewStage;
    private int lenNewStage;
    private int crossingSeqNo;
    private String cityCode;

    /**
     * 设置当前信号机运行方案编号
     */
    private Integer curPlanNo;

    @Override
    public String generateDbUrl() {
        return "juncstage";
    }

    @Override
    public String getSgpUrl() {
        return "/stage/runtime/save";
    }

    @Override
    public Object transPushData() {
        StageDTO stageDTO = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, StageDTO.class);
        return stageDTO;
    }
}
