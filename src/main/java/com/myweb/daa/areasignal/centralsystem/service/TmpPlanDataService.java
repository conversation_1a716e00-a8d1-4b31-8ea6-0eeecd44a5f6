package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.myweb.daa.areasignal.htbussiness.utils.CentralUtils.*;


@Service
@Slf4j
public class TmpPlanDataService {


    @Autowired
    private P1049Configure p1049Configure;

    /**
     * 临时方案控制数据存储
     */
    @Getter
    private Map<String, TmpPlanService.TmpPlanData> tmpPlanDataMap = new ConcurrentHashMap<>();


    @Value("#{'${global.planCordSystem:123}'}")
    private int planCordSystem;

    @Value("#{'${global.planHolo:124}'}")
    private int planHolo;

    @Value("#{'${global.planTmpPlan:125}'}")
    private int planTmpPlan;

    /**
     * 特殊方案转换
     */
    public void spChangPlanNo() {

        DEFAULT_SYSTEM_PLANNO = planCordSystem;

        DEFAULT_OPTIMIZE_PLANNO = planHolo;

        DEFAULT_TMP_PLANNO = planTmpPlan;

        log.error("系统 线控使用特殊方案编号-{}, 全息使用特殊方案编号-{}, 临时方案使用特殊方案编号-{}", DEFAULT_SYSTEM_PLANNO,
                DEFAULT_OPTIMIZE_PLANNO, DEFAULT_TMP_PLANNO);

    }


    /**
     * 判定是否是特殊的方案编号
     *
     * @param planNo
     * @param signalBrandPort
     * @return
     */
    public boolean isPlanNoSp(int planNo, SignalBrandPort signalBrandPort) {
        //如果不是华通信号机，返回false
        if (signalBrandPort.brandCode()
                != SignalBrandPort.HT.brandCode()) {
            return false;
        }

        if (planNo == DEFAULT_SYSTEM_PLANNO || planNo == DEFAULT_OPTIMIZE_PLANNO
                || planNo == DEFAULT_TMP_PLANNO) {
            return true;
        }

        P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(SignalBrandPort.HT));
        //是否需要进行方案编号转换
        return false;
    }


    /**
     * 根据当前是否在临时方案控制下，是否允许下发控制方案
     * 临时方案控制模式 38 > 全息控制 10 > 线控 2
     *
     * @param crossId
     * @param toSetMode
     * @return
     */
    public boolean isControlAllow(String crossId, int toSetMode) {
        TmpPlanService.TmpPlanData tmpPlanData = tmpPlanDataMap.get(crossId);

        //当前未控制，允许控制;
        if (tmpPlanData == null || !tmpPlanData.isStart()) {
            return true;
        }

        return toSetMode >= tmpPlanData.getMode();
    }

    /**
     * 当前是否在全息控制
     *
     * @param crossId
     * @return
     */
    public boolean isInHolo(String crossId) {
        TmpPlanService.TmpPlanData tmpPlanData = tmpPlanDataMap.get(crossId);

        //当前未控制，允许控制;
        if (tmpPlanData == null || !tmpPlanData.isStart()) {
            return false;
        }

        return LesControlMode.ADAPTIVE_CONTROL.value() == tmpPlanData.getMode();
    }

    /**
     * 当前是否在全息控制
     *
     * @param crossId
     * @return
     */
    public boolean isInCordSystem(String crossId) {
        TmpPlanService.TmpPlanData tmpPlanData = tmpPlanDataMap.get(crossId);

        //当前未控制，允许控制;
        if (tmpPlanData == null || !tmpPlanData.isStart()) {
            return false;
        }

        return LesControlMode.COORDINATE_SYSTEM.value() == tmpPlanData.getMode();
    }


    /**
     * 临时方案控制方式切换
     *
     * @param controllerId
     * @param crossId
     * @param lesMode
     * @return
     */
    public String modeChange(String controllerId, String crossId, String lesMode) {

        if (SignalBrandPort.getType(controllerId) != SignalBrandPort.HT) {
            return lesMode;
        }

        //当前值针对华通进行临时方案控制方式转换
        String retMode = lesMode;
        if (lesMode.equalsIgnoreCase(String.valueOf(LesControlMode.TEMPORARY_PATTERN.value()))) {
            TmpPlanService.TmpPlanData tmpPlanData = tmpPlanDataMap.get(crossId);
            if (tmpPlanData != null) {
                if (tmpPlanData.isStart()) {
                    retMode = String.valueOf(tmpPlanData.getMode());
                }
            }
        }
        return retMode;
    }


    /**
     * 获取特殊的方案编号切换
     *
     * @param planNo
     * @param crossId
     * @param signalBrandPort
     * @return
     */
    public int getSpPlanNo(int planNo, String crossId, SignalBrandPort signalBrandPort) {

        //当前只针对华通信号机方案编号切换进行转换
        if (signalBrandPort.brandCode()
                != SignalBrandPort.HT.brandCode()) {
            return planNo;
        }


        TmpPlanService.TmpPlanData tmpPlanData = tmpPlanDataMap.get(crossId);
        if (tmpPlanData == null || !tmpPlanData.isStart()) {
            return planNo;
        }


        try {
            P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(SignalBrandPort.HT));
            //是否需要进行方案编号转换
            {
                {
                    int toChangPlanNo = DEFAULT_OPTIMIZE_PLANNO;
                    if (LesControlMode.ADAPTIVE_CONTROL.value() == tmpPlanData.getMode()) {
                        toChangPlanNo = DEFAULT_OPTIMIZE_PLANNO;
                    } else if (LesControlMode.TEMPORARY_PATTERN.value() == tmpPlanData.getMode()) {
                        toChangPlanNo = DEFAULT_TMP_PLANNO;
                    } else if (LesControlMode.COORDINATE_SYSTEM.value() == tmpPlanData.getMode()) {
                        toChangPlanNo = DEFAULT_SYSTEM_PLANNO;
                    }

                    log.error("路口{}当前实际临时方案编号{}转化为{}", crossId,
                            planNo, toChangPlanNo);
                    planNo = toChangPlanNo;
                }

            }
        } catch (Exception e) {
            log.error("数据处理异常", e);
        }
        return planNo;
    }

}
