package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.enums.DirectionType;

/**
 * 方向
 */
public enum DirectionType1049 {
    NORTH(0, "北", DirectionType.NORTH),
    NORTHEAST(1, "东北", DirectionType.NORTHEAST),
    EAST(2, "东", DirectionType.EAST),
    SOUTHEAST(3, "东南", DirectionType.SOUTHEAST),
    SOUTH(4, "南", DirectionType.SOUTH),
    SOUTHWEST(5, "西南", DirectionType.SOUTHWEST),
    WEST(6, "西", DirectionType.WEST),
    NORTHWEST(7, "西北", DirectionType.NORTHWEST),
    UNKNOWN(8, "未知", DirectionType.UNKNOWN);

    private final int code;

    private final String desp;

    private final DirectionType directionType;

    DirectionType1049(int code, String desp, DirectionType directionType) {
        this.code = code;
        this.desp = desp;
        this.directionType = directionType;
    }

    public static DirectionType1049 parseCode(Integer code) {

        if (code != null) {
            for (DirectionType1049 dir : DirectionType1049.values()) {
                if (code.equals(dir.getCode())) {
                    return dir;
                }
            }
        }
        return DirectionType1049.UNKNOWN;
    }

    public int getCode() {
        return code;
    }

    public String getDesp() {
        return desp;
    }

    public DirectionType directionType() {
        return directionType;
    }

    @Override
    public String toString() {
        return desp;
    }
}
