package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.signal.Phase;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:44
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhaseParam implements SgpTransAble {
    public static final String MqObjectId = "1006";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int phaseNo;
    private String phaseName;
    private Boolean enable;
    /**
     * 加载的时候需要进行数据存储，调看的时候需要将数据取出来返回
     */
    private Integer phaseDirection;
    private int minGreen;
    private int maxGreen1;
    private int maxGreen2;
    private int maxGreen3;
    private int yellow;
    private int allRed;
    private int walk;
    private int pedestrianClear;
    private List<Integer> laneNoList;
    private List<Integer> laneNos;
    private List<Integer> pedDirList;
    private List<Integer> lampGroupNoList;
    private int preClearance;

    /**
     * 获取路权时红灯时长
     */
    private int genRed;

    /**
     * 获取路权时红闪
     */
    private int genRedFlash;

    /**
     * 获取路权时红快闪
     */
    private int genRedFastFlash;

    /**
     * 适配NATS3.0中参数名称
     */
    private int redFlash;

    private int redFastFlash;

    @Override
    public String getUrl() {
        return "phase/single/" + noArea + "/" + noJunc;
    }


    public String delUrl() {
        return "phase/" + signalControllerID;
    }

    /**
     * 转换可能存在的异常数据
     */
    public void exchange() {
        if ((laneNoList != null && !laneNoList.isEmpty()) && (laneNos == null)) {
            laneNos = laneNoList;
        }
        if ((laneNos != null && !laneNos.isEmpty()) && (laneNoList == null)) {
            laneNoList = laneNos;
        }
    }

    @Override
    public Object transData() {
        Phase phase = P1049HelpUtils.getMapperFactory().getMapperFacade().map(this, Phase.class);
        phase.setMovementNo(phaseDirection);
        phase.setLoss(0);

        phase.setLaneNos(laneNoList);
        phase.setEnabled((lampGroupNoList == null || lampGroupNoList.size() == 0) ? false : true);
        phase.setPreClearance(preClearance);
        phase.setLampGroup(lampGroupNoList);
        return phase;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return phaseNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
