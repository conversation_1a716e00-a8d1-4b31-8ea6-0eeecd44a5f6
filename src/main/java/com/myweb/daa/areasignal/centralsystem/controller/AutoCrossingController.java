package com.myweb.daa.areasignal.centralsystem.controller;

import com.les.ads.ds.ReturnEntity;
import com.myweb.daa.areasignal.centralsystem.service.AutoCrossingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: WJ
 * @Description: 自动生成莱斯信号机、莱斯路口、渠化数据接口
 * @Date: create in 2023/4/20 17:03
 */
@Controller
@Slf4j
@RestController
@RequestMapping("/autogen")
public class AutoCrossingController {


    @Autowired
    private AutoCrossingService autoCrossingService;

    /**
     * 读取莱斯路口数据项
     *
     * @return
     */
    @GetMapping("/crossing/{ruleId}/{cross1049}")
    public ReturnEntity<?> getLesCrossing(@PathVariable String ruleId, @PathVariable String cross1049) {
        ReturnEntity<?> returnEntity = autoCrossingService.saveCrossing(cross1049, ruleId, true);
        return returnEntity;
    }

    @PostMapping("/crossingAll/{ruleId}")
    public ReturnEntity<?> getLesCrossing(@PathVariable String ruleId) {
        ReturnEntity<?> returnEntity = autoCrossingService.saveCrossings(ruleId);
        return returnEntity;
    }

    /**
     * 根据莱斯路口id生成莱斯车道信息
     *
     * @param crossingId
     * @return
     */
    @PostMapping("/lane/{crossingId}")
    public ReturnEntity<?> saveLanes(@PathVariable String crossingId) {
        ReturnEntity<?> returnEntity = autoCrossingService.saveLanes(crossingId);
        return returnEntity;
    }

    /**
     * 根据莱斯路口id生成莱斯车道信息
     *
     * @return
     */
    @PostMapping("/laneAll")
    public ReturnEntity<?> saveLaneAlls() {
        ReturnEntity<?> returnEntity = autoCrossingService.saveAllLanes();
        return returnEntity;
    }

    /**
     * 根据莱斯路口id生成莱斯车道信息
     *
     * @return
     */
    @PostMapping("/laneAll2")
    public ReturnEntity<?> saveLaneAlls2() {
        ReturnEntity<?> returnEntity = autoCrossingService.saveAllLanesWithGetLaneParam();
        return returnEntity;
    }

}
