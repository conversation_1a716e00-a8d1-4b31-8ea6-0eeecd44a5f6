package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.PlanParam;
import com.myweb.daa.areasignal.centralsystem.service.*;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
@Component
@Slf4j
public class PlanParamHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;
    @Autowired
    private DataInternalNotify dataInternalNotify;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;
    @Autowired
    private AcsCharaService acsCharaService;
    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return PlanParam.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return PlanParam.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return PlanParam.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {

        log.error("调看信号机{}方案数据-{}", controllerId, datas);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        //批量数据
        if (datas.isEmpty()) {
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);

        } else {
            //请求阶段数据参数
            datas.stream().forEach(
                    planNo -> {
                        {
                            TSCCmd tscCmd = TSCCmd.builder()
                                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class.getSimpleName())
                                    .ID(crossingInfoOp.get().getCrossingId1049())
                                    .No(String.valueOf(planNo))
                                    .build();
                            List<TSCCmd> tscCmds = new ArrayList<>();
                            tscCmds.add(tscCmd);
                            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Get)
                                    .object(tscCmds)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(TSCCmd.class).build();
                            requestMsgs.add(p1049CentralSystemMsg);
                        }
                    }
            );
        }

        //同时调看阶段参数数据项
         {
            TSCCmd tscCmd = TSCCmd.builder()
                    .ObjName(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName())
                    .ID(crossingInfoOp.get().getCrossingId1049())
                    .No("")
                    .build();
            List<TSCCmd> tscCmds = new ArrayList<>();
            tscCmds.add(tscCmd);
            P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                    .messageType(MessageType.REQUEST)
                    .operationName(OperationName.Get)
                    .object(tscCmds)
                    .signalBrandPort(signalBrandPort)
                    .address(crossingInfoOp.get().getAddress1049Ip())
                    .clazz(TSCCmd.class).build();
            requestMsgs.add(p1049CentralSystemMsg);
        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<Integer> planNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        planNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(requestMessage.getSignalControllerID());
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        //解析返回数据
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //批量调看数据时，方案编号需要从已有数据中查找
        boolean requestAll = false;
        if (planNos.isEmpty()) {
            requestAll = true;
            Optional<Map<String, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam>> planParamMapsOp
                    = ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(), subJuncNo, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class);
            if (planParamMapsOp.isPresent()) {
                Map<String, com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> stringPlanParamMap = planParamMapsOp.get();
                List<Integer> planNow = stringPlanParamMap.keySet().stream().map(planNo -> Integer.parseInt(planNo)).collect(Collectors.toList());
                planNos.addAll(planNow);
            }
        }

        //按照方案编号进行排序
        planNos.sort(Comparator.comparingInt(Integer::intValue));

        {
            //在调看方案的时候重新调看阶段参数
            //并且同步进行数据存储
            itemDatas.stream().filter(
                    itemData -> itemData.getData() instanceof BaseMessage1049
            ).forEach(
                    itemData -> {
                        BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                        Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                        if (!firstMsgData.isPresent()) {
                            return;
                        }

                        if (firstMsgData.get() instanceof SDO_Error1049) {
                            log.error("请求应答异常-{}", firstMsgData.get());
                            return;
                        }


                        List<Object> objectList = (List<Object>) (firstMsgData.get());
                        if (objectList == null || objectList.isEmpty()) {
                            return;
                        }

                        List<String> notifyNos = new ArrayList<>();
                        List<Object> notifyStageParams = new ArrayList<>();
                        //如果是阶段数据项
                        if ((objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam)) {
                            List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam> stageParamList
                                    = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam>) (firstMsgData.get());
                            stageParamList.stream().forEach(
                                    stageParam -> {
                                        notifyNos.add(stageParam.getStageNo());
                                        notifyStageParams.add(stageParam);
                                    }
                            );
                        }


                        //同步数据存储-批量数据通知,必须放在此处，否则会出现获取编码时获取不到转换数据项
                        if (!notifyNos.isEmpty()) {
                            dataInternalNotify.deleteOldInfoSync(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class);
                            dataInternalNotify.datasNotifySync(requestMessage.getSignalControllerID(), subJuncNo,
                                    notifyNos, notifyStageParams);
                        }
                    });
        }

        if (requestAll) {
            //删除内存及数据库的方案数据
             {
                try {
                    //删除中心机数据项
                    signalCacheService.deleteDataEntity(requestMessage.getSignalControllerID(),
                            com.myweb.daa.areasignal.centralsystem.param.PlanParam.class.getSimpleName());
                } catch (Exception e) {
                    log.error("删除中心机方案数据项出现异常", e);
                }
            }
        }

        List<Object> planParams = new ArrayList<>();
        //待更新的通知数据项
        List<Object> notifyPlanParams = new ArrayList<>();
        List<String> notifyNos = new ArrayList<>();
        planNos.stream().forEach(
                planNo -> {
                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    return;
                                }


                                List<Object> objectList = (List<Object>) (firstMsgData.get());
                                if (objectList == null || objectList.isEmpty()) {
                                    return;
                                }

                                //如果是阶段数据项
                                if (!(objectList.get(0) instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam)) {
                                    return;
                                }

                                List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> planParamList
                                        = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam>) (firstMsgData.get());

                                if (planParamList == null || planParamList.isEmpty()) {
                                    return;
                                }

                                Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> returnData
                                        = Optional.empty();
                                //返回列表数据项的系统，需要找到对应数据项返回
                                returnData = planParamList.stream().filter(
                                        planParam ->
                                                planParam.getPlanNo().equalsIgnoreCase(String.valueOf(planNo))

                                ).findAny();

                                if (returnData.isPresent()) {
                                    //内部数据项变更通知
                                    notifyPlanParams.add(returnData.get());
                                    notifyNos.add(String.valueOf(planNo));

                                    //周期数据项
                                    AtomicInteger cycle = new AtomicInteger(0);
                                    //协调阶段,转换成莱斯内部阶段编号
                                    int cordStageNo = 0;
                                    if (returnData.get().getCoordStageNo() != null) {

                                        String orgCoordStageNo = returnData.get().getCoordStageNo();

                                        Optional<String> centralStageNoOp = ht1049SignalCacheService.getCentralStageNo(requestMessage.getSignalControllerID(),
                                                subJuncNo, orgCoordStageNo);
                                        if (centralStageNoOp.isPresent()) {
                                            cordStageNo = Integer.parseInt(centralStageNoOp.get());
                                        }

                                    }

                                    //协调阶段差
                                    int offset = (returnData.get().getOffset() == null ? 0 : Integer.parseInt(returnData.get().getOffset()));
                                    //阶段长度以及时长，需要查询当前阶段参数的数据
                                    List<Integer> stageNos = new ArrayList<>();
                                    List<Integer> stageTimes = new ArrayList<>();
                                    List<Integer> greenTimes = new ArrayList<>();
                                    StageTimingList stageTimeList = returnData.get().getStageTimingList();

                                     {
                                        if (stageTimeList != null
                                                && stageTimeList.getStageTiming() != null
                                                && !stageTimeList.getStageTiming().isEmpty()) {
                                            log.error("信号机{}-方案-{}方案数据-{}", requestMessage.getSignalControllerID(),
                                                    planNo, returnData.get());

                                            List<StageTiming> stageTimings = stageTimeList.getStageTiming();
                                            for (int i = 0; i < stageTimings.size(); i++) {
                                                int yellow = 0;
                                                int green = 0;
                                                int red = 0;
                                                int yellowRed = 0;
                                                StageTiming stageTiming = stageTimings.get(i);
                                                String stageNo = stageTiming.getStageNo();

                                                Optional<String> centralStageNoOp = ht1049SignalCacheService.getCentralStageNo(requestMessage.getSignalControllerID(),
                                                        subJuncNo, stageNo);
                                                if (!centralStageNoOp.isPresent()) {
                                                    log.error("信号机-{}方案-{}出现异常未映射到中心机的阶段", requestMessage.getSignalControllerID(), returnData.get());
                                                    continue;
                                                }

                                                //参数初始化
                                                yellow = 0;
                                                green = 0;
                                                red = 0;
                                                yellowRed = 0;

                                                green = Integer.parseInt(stageTiming.getGreen());
                                                yellow =  Integer.parseInt(stageTiming.getYellow());
                                                red =  Integer.parseInt(stageTiming.getAllRed());

                                                stageNos.add(Integer.parseInt(centralStageNoOp.get()));
                                                stageTimes.add(yellow + green + red + yellowRed);
                                                greenTimes.add(green);
                                                cycle.getAndAdd(yellow + green + red + yellowRed);

                                            }
                                        }
                                    }

                                    //组装内部数据项
                                    PlanParam planParamCenreal = PlanParam.builder()
                                            .signalControllerID(requestMessage.getSignalControllerID())
                                            .noArea(signalInfoOp.isPresent() ? signalInfoOp.get().getNoArea() : 0)
                                            .noJunc(signalInfoOp.isPresent() ? signalInfoOp.get().getNoJunc() : 0)
                                            .planNo(planNo)
                                            .crossingSeqNo(subJuncNo)
                                            .cycleLen(cycle.get())
                                            .coordPhaseNo(cordStageNo)
                                            .offSet(offset)
                                            .stageNoList(stageNos)
                                            .timeList(stageTimes)
                                            .greenList(greenTimes).build();

                                    {
                                        //查找内存中中心机存储的数据项,20231123
                                        String planName = returnData.get().getPlanName();

                                        //20240708  优先使用接口中的方案名称
                                        if (planName != null && !planName.trim().isEmpty()) {
                                            planParamCenreal.setDescription(returnData.get().getPlanName());
                                        } else {
                                            Optional<PlanParam> planParamOp
                                                    = signalCacheService.getData(requestMessage.getSignalControllerID(), planNo, PlanParam.class);
                                            if (planParamOp.isPresent()) {
                                                //当前已经配置过数据项
                                                String description = planParamOp.get().getDescription();
                                                if (description != null && !description.isEmpty()) {
                                                    planParamCenreal.setDescription(description);
                                                } else {
                                                    planParamCenreal.setDescription(planName != null ? planName : "");
                                                }
                                            }
                                        }
                                    }

                                    planParams.add(planParamCenreal);

                                }
                            }
                    );
                }
        );

        //批量数据通知,必须放在此处，否则会出现获取编码时获取不到转换数据项
        if (!notifyNos.isEmpty()) {
            dataInternalNotify.datasNotify(requestMessage.getSignalControllerID(), subJuncNo,
                    notifyNos, notifyPlanParams);
        }


        //如果没有查找到对应的方案数据
        if (planParams.isEmpty()) {
            return Optional.empty();
        }

        log.error("调看信号机{}方案-{}，数据返回-{}", crossingInfoOp.get().getControllerId(),
                planNos,
                planParams
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, planParams);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        AtomicInteger errorCount = new AtomicInteger(0);
        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();


        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    com.myweb.daa.areasignal.centralsystem.param.PlanParam centralPlanParam =
                            jsonObject.toJavaObject(com.myweb.daa.areasignal.centralsystem.param.PlanParam.class);

                    log.error("收到方案设置数据项-{}", centralPlanParam);

                    List<Integer> stageNoList = centralPlanParam.getStageNoList();
                    List<Integer> greenTimeList = centralPlanParam.getGreenList();
                    if (stageNoList == null || greenTimeList == null || stageNoList.size() != greenTimeList.size()) {
                        log.error("异常的待加载参数-{}", centralPlanParam);
                        return;
                    }

                    //查找内存中原先方案的数据项
                    Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> planParamOp =
                            ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                    String.valueOf(centralPlanParam.getPlanNo())
                                    , com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class);

                    Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam> orgPlanParamOp
                            = planParamOp;

                    boolean newPattern = false;
                    if (!planParamOp.isPresent()) {
                        com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam
                                planParamNew = new com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam();
                        //设置默认协调相位 0
                        planParamNew.setCoordStageNo(String.valueOf(centralPlanParam.getPlanNo() * 100));
                        planParamNew.setPlanNo(String.valueOf(centralPlanParam.getPlanNo()));
                        planParamNew.setCrossID(crossingInfoOp.get().getCrossingId1049());
                        planParamOp = Optional.of(planParamNew);
                        newPattern = true;
                        log.error("信号机-{}的1049方案原始数据未能查询到{}，使用新建方案{}", controllerId, centralPlanParam.getPlanNo(), planParamNew);
                    }

                    //待修改的方案数据项
                    StageTimingList stageTimingList1049 = new StageTimingList();
                    List<StageTiming> stageTiming = new ArrayList<>();
                    stageTimingList1049.setStageTiming(stageTiming);

                    int cycle = 0;
                    int cordPhaseNoIndex = 0;
                    for (int i = 0; i < stageNoList.size(); i++) {
                        int stageNo = stageNoList.get(i);
                        int greenTime = greenTimeList.get(i);

                        //根据莱斯阶段编号，转换成1049阶段编号
                        Optional<String> p1049StageNoOp = Optional.of(String.valueOf(stageNo));
                        if (!centralPlanParam.isOrg1049StageNo()) {
                            p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(controllerId, subJuncNo,
                                    String.valueOf(stageNo));
                            if (!p1049StageNoOp.isPresent()) {
                                log.error("{}未找到1049阶段编号-中心机阶段编号{}", controllerId, stageNo);
                                errorCount.incrementAndGet();
                                return;
                            }
                        }

                        Optional<com.myweb.daa.areasignal.centralsystem.param.StageParam> centralStageParamOp
                                = signalCacheService.getData(crossingInfoOp.get().getControllerId(),
                                stageNo, com.myweb.daa.areasignal.centralsystem.param.StageParam.class);
                        if (!centralStageParamOp.isPresent()) {
                            log.error("信号机-{}的中心机阶段数据未能查询到{}", controllerId, stageNo);
                            errorCount.incrementAndGet();
                            return;
                        }

                        //协调阶段的索引,用于重新生成索引阶段
                        if (stageNo == centralPlanParam.getCoordPhaseNo()) {
                            cordPhaseNoIndex = i + 1;
                        }

                        //查找内存中原先阶段的数据项
                        Optional<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam> stageParamOp =
                                ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(), subJuncNo,
                                        p1049StageNoOp.get(), com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class);
                        if (!stageParamOp.isPresent()) {
                            log.error("信号机-{}的1049阶段原始数据未能查询到{}", controllerId, p1049StageNoOp.get());
                            errorCount.incrementAndGet();
                            return;
                        }

                        //更新修改数据项
                        //绿灯时
                        {
                            int yellow = centralStageParamOp.get().getYellow();
                            int green = greenTime;
                            int red = centralStageParamOp.get().getAllRed();

                            if(greenTime != 0) {
                                cycle += yellow;
                                cycle += green;
                                cycle += red;
                            }

                            //华通阶段数据项在方案参数里面
                             StageTiming stageTimingCur = new StageTiming();
                             stageTimingCur.setStageNo(p1049StageNoOp.get());
                             stageTimingCur.setGreen(String.valueOf(greenTime));
                             stageTimingCur.setYellow(String.valueOf(yellow));
                             stageTimingCur.setAllRed(String.valueOf(red));
                             stageTimingCur.setMaxGreen(String.valueOf(centralStageParamOp.get().getMaxGreen()));
                             stageTimingCur.setMinGreen(String.valueOf(centralStageParamOp.get().getMinGreen()));

                             //查询原始方案中是否包含 迟起早闭以及感应最大绿最小绿参数
                             if(orgPlanParamOp.isPresent()) {
                                 StageTimingList orgStageTimingList = orgPlanParamOp.get().getStageTimingList();
                                 if (orgStageTimingList != null
                                         && orgStageTimingList.getStageTiming() != null
                                         && !orgStageTimingList.getStageTiming().isEmpty()) {
                                     for (StageTiming orgStageTiming : orgStageTimingList.getStageTiming()) {
                                         if (orgStageTiming.getStageNo().equalsIgnoreCase(p1049StageNoOp.get())) {
                                             if(orgStageTiming.getAdjustList() != null) {
                                                 stageTimingCur.setAdjustList(orgStageTiming.getAdjustList());
                                             }
                                             if(orgStageTiming.getMaxGreen() != null) {
                                                 stageTimingCur.setMaxGreen(orgStageTiming.getMaxGreen());
                                             }
                                             if(orgStageTiming.getMinGreen() != null) {
                                                 stageTimingCur.setMinGreen(orgStageTiming.getMinGreen());
                                             }
                                             break;
                                         }
                                     }
                                 }
                             }

                             stageTiming.add(stageTimingCur);
                        }
                    }


                    //方案数据项
                    {
                        //拷贝数据项
                        com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam planParam = P1049HelpUtils.getMapperFactory().getMapperFacade().map(planParamOp.get(),
                                com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.PlanParam.class);

                        //更新周期参数
                        if (cycle != centralPlanParam.getCycleLen()) {
                            log.error("{}准备加载的方案{}传递的周期时长{}与计算的周期时长{}不一致", controllerId,
                                    centralPlanParam.getPlanNo(), centralPlanParam.getCycleLen(), cycle);
                        }
                        planParam.setCycleLen(String.valueOf(cycle));
                        //更新相位差
                        planParam.setOffset(String.valueOf(centralPlanParam.getOffSet()));

                        //更新协调相位
                        {
                            //原始协调阶段
                            String coordPhaseNo = planParam.getCoordStageNo();
                             {
                                //根据莱斯阶段编号，转换成1049阶段编号
                                Optional<String> p1049StageNoOp = ht1049SignalCacheService.getP1049StageNo(controllerId, subJuncNo,
                                        String.valueOf(centralPlanParam.getCoordPhaseNo()));
                                if (!p1049StageNoOp.isPresent()) {
                                    log.error("{}未找到1049阶段编号-中心机协调阶段编号{}", controllerId, centralPlanParam.getCoordPhaseNo());
                                } else {
                                    coordPhaseNo = p1049StageNoOp.get();
                                }
                            }
                            //设置协调阶段号
                            planParam.setCoordStageNo(coordPhaseNo);
                        }

                        //方案参数加载
                         {

                            //设置方案参数中阶段数据项
                            planParam.setStageTimingList(stageTimingList1049);

                            //设置方案名称
                            if (planParam.getPlanName() == null) {
                                planParam.setPlanName("");
                            }

                            //设置到对端系统方案名称20240620
                            if (centralPlanParam.getDescription() != null
                                    && !centralPlanParam.getDescription().isEmpty()) {
                                planParam.setPlanName(centralPlanParam.getDescription());
                            }

                            SetPlanParam setPlanParam = SetPlanParam.builder()
                                    .Oper(newPattern ? "1" : "2")
                                    .stageParamList(new StageParamList())
                                    .PlanParam(planParam).build();


                            List<SetPlanParam> setPlanParams = new ArrayList<>();
                            setPlanParams.add(setPlanParam);

                            P1049CentralSystemMsg setPlanParamMsg = P1049CentralSystemMsg.builder()
                                    .messageType(MessageType.REQUEST)
                                    .operationName(OperationName.Set)
                                    .object(setPlanParams)
                                    .signalBrandPort(signalBrandPort)
                                    .address(crossingInfoOp.get().getAddress1049Ip())
                                    .clazz(com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetPlanParam.class).build();

                            requestMsgs.add(setPlanParamMsg);

                        }
                    }

                }
        );


        //存在错误数据，不能进行方案加载
        if (errorCount.get() > 0) {
            log.error("{}方案数据存在异常, 无法加载", controllerId);
            return Optional.empty();
        }

        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        acsCharaService.addAcsChar(requestMsgs, crossingInfoOp.get(), signalBrandPort);

        log.error("准备发送方案设置命令-{}", requestMsgs);

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<PlanParam> planParams = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        PlanParam planParam = jsonObject.toJavaObject(PlanParam.class);
                        planParams.add(planParam);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        AtomicInteger errorCount = new AtomicInteger(0);
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        planParams.stream().forEach(
                planParam -> {

                    int subJuncNo = 1;
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(planParam.getSignalControllerID(),
                            subJuncNo);
                    if (!crossingInfoOp.isPresent()) {
                        errorCount.getAndIncrement();
                        return;
                    }

                    itemDatas.stream().filter(
                            itemData -> itemData.getData() instanceof BaseMessage1049
                    ).forEach(
                            itemData -> {
                                BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                if (!firstMsgData.isPresent()) {
                                    errorCount.getAndIncrement();
                                    return;
                                }

                                if (firstMsgData.get() instanceof SDO_Error1049) {
                                    log.error("请求应答异常-{}", firstMsgData.get());
                                    errorCount.getAndIncrement();
                                    return;
                                }


                                {
                                    List<Object> objectList = (List<Object>) (firstMsgData.get());
                                    if (objectList == null || objectList.isEmpty()) {
                                        errorCount.getAndIncrement();
                                        return;
                                    }

                                    //如果是方案数据项
                                     if (objectList.get(0)
                                            instanceof com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetPlanParam) {
                                        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetPlanParam> setplanParamList
                                                = (List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SetPlanParam>) (firstMsgData.get());


                                        {
                                            log.error("加载方案{}方案参数返回{}", crossingInfoOp.get().getControllerId(), setplanParamList);
                                        }

                                        if (!setplanParamList.isEmpty()) {
                                            setplanParamList.forEach(
                                                    setPlanParam -> {
                                                        //内部数据项变更通知
                                                        dataInternalNotify.dataNotify(requestMessage.getSignalControllerID(), subJuncNo,
                                                                setPlanParam.getPlanParam().getPlanNo(), setPlanParam.getPlanParam());
                                                    }
                                            );
                                        }
                                    }
                                }
                            }
                    );
                }
        );

        //存在错误数据，不能进行方案加载
        if (errorCount.get() > 0) {
            log.error("{}方案数据存在异常,加载失败", requestMessage.getSignalControllerID());
            return Optional.empty();
        }

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return false;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);
        if (signalBrandPort.brandCode() == SignalBrandPort.YL.brandCode()) {
            return true;
        }

        return false;
    }

}
