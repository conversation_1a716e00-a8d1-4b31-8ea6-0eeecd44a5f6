package com.myweb.daa.areasignal.centralsystem.holo.process;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.CordSystemCmd;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.TabHolographicCycleDto;
import com.myweb.daa.areasignal.centralsystem.param.CentralPlanParam;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanDataService;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCycle;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageCtrlList;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageOptList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/23 19:08
 */
@Service
@Slf4j
public class HolographicCycleDtoProcess {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private HtSignalController htSignalController;

    @Autowired
    private HtSignalService htSignalService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;

    /**
     * 用于记录当前路口已经几个周期没有发送周期优化方案。
     */
    private Map<String, AtomicInteger> holoCycleDataNotSendCycleCountMap = new ConcurrentHashMap<>();


    /**
     * 用于记录当前路口已经几个周期没有发送线控优化方案。
     */
    private Map<String, AtomicInteger> cordCycleDataNotSendCycleCountMap = new ConcurrentHashMap<>();

    /**
     * 用于进行全息优化恢复：
     * 当信号机处于全息控制的时候，如果在3个周期如果没有收到全息优化命令下发，则主动进行恢复。
     *
     * @param crossCycle
     */
    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void process(CrossCycle crossCycle) {
        //接收周期优化数据项
        String crossID = crossCycle.getCrossID();
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        String controllerId = crossingInfoOp.get().getControllerId();

        Optional<CrossCtrlInfo> crossControlModeOp = ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(), crossingInfoOp.get().getSubJuncNo(),
                "0",
                CrossCtrlInfo.class);
        if (!crossControlModeOp.isPresent()) {
            return;
        }

        AtomicInteger atomicInteger = holoCycleDataNotSendCycleCountMap.get(crossID);
        if (atomicInteger == null) {
            atomicInteger = new AtomicInteger(0);
            holoCycleDataNotSendCycleCountMap.put(crossID, atomicInteger);
        }

        AtomicInteger atomicIntegerCord = cordCycleDataNotSendCycleCountMap.get(crossID);
        if (atomicIntegerCord == null) {
            atomicIntegerCord = new AtomicInteger(0);
            cordCycleDataNotSendCycleCountMap.put(crossID, atomicIntegerCord);
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

          {
            //非全息/线控优化控制
            atomicInteger.set(0);
            atomicIntegerCord.set(0);
            log.warn("*****!!!!!路口-{}当前处于非全息/线控控制，初始化未下发全息优化指令周期为-{},线控优化指定周期-{}", crossID, atomicInteger.get(),
                    atomicIntegerCord.get());
        }

    }


    /**
     * 标记收到了线控指令
     *
     * @param cordSystemCmd
     */
    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void cordSystemCmdProcess(CordSystemCmd cordSystemCmd) {

        if (cordSystemCmd == null || cordSystemCmd.getCrossId() == null) {
            return;
        }

        log.error("收到线控命令标记-{}", cordSystemCmd);
        AtomicInteger atomicInteger = cordCycleDataNotSendCycleCountMap.get(cordSystemCmd.getCrossId());
        if (atomicInteger != null) {
            atomicInteger.set(0);
        }

    }


    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void juncModeDtoProcess(TabHolographicCycleDto tabHolographicCycleDto) {
        log.error("收到全息周期优化-{}", tabHolographicCycleDto);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabHolographicCycleDto.getNoArea(),
                tabHolographicCycleDto.getNoJunc());

        if (!signalInfoOp.isPresent()) {
            log.error("没有找到信号机进行全息周期优化-{}", tabHolographicCycleDto);
            return;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalInfoOp.get().getSignalId());

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            log.error("没有找到路口进行全息周期优化-{}", tabHolographicCycleDto);
            return;
        }

        AtomicInteger atomicInteger = holoCycleDataNotSendCycleCountMap.get(crossingInfoOp.get().getCrossingId());
        if (atomicInteger != null) {
            atomicInteger.set(0);
        }

        {
            if (tabHolographicCycleDto.getNoStage() == null ||
                    tabHolographicCycleDto.getLenStage() == null ||
                    tabHolographicCycleDto.getNoStage().size() != tabHolographicCycleDto.getLenStage().size()) {
                log.error("信号控制参数异常,相位个数与相位时长数据不一致-{}", tabHolographicCycleDto);
            } else {
                List<Integer> lenStages = tabHolographicCycleDto.getLenStage();
                List<Integer> noStages = tabHolographicCycleDto.getNoStage();
                List<String> p1049StageNos = new ArrayList<>();

                for (Integer noStage : noStages) {
                    //检查阶段号
                    Optional<String> p1049StageNo = ht1049SignalCacheService.getP1049StageNo(signalInfoOp.get().getSignalId(), subJuncNo, String.valueOf(noStage));
                    if (!p1049StageNo.isPresent()) {
                        log.error("信号控制参数异常{},未知的阶段-{}", tabHolographicCycleDto, noStage);
                        return;
                    }
                    p1049StageNos.add(p1049StageNo.get());
                }

                if ((signalBrandPort.brandCode() == SignalBrandPort.DW.brandCode())
                        || (signalBrandPort.brandCode() == SignalBrandPort.YL.brandCode())
                        || (SignalBrandPort.isBrandLesExtProtocol(signalBrandPort))) {

                    //准备优化数据项
                    StageCtrlList stageCtrlList = new StageCtrlList();
                    stageCtrlList.setCrossID(crossingInfoOp.get().getCrossingId());

                    List<StageOptList> stageOptList = new ArrayList<>();
                    for (int i = 0; i < lenStages.size(); i++) {
                        StageOptList stageOptListItem = new StageOptList();
                        stageOptListItem.setGreen(lenStages.get(i));
                        stageOptListItem.setStageNo(p1049StageNos.get(i));
                        stageOptList.add(stageOptListItem);
                    }
                    stageCtrlList.setStageOptList(stageOptList);

                    log.error("全息周期优化准备发送-{}", stageCtrlList);
                    JsonResult jsonResult = htSignalController.setStageCtrlList(signalInfoOp.get().getSignalId(), subJuncNo, stageCtrlList);
                    log.error("全息周期优化结果-{}", jsonResult);

                    //通知异常情况
                    if (!jsonResult.isSuccess()) {
                        htSignalService.notifyHoloAck(signalInfoOp.get(), subJuncNo, jsonResult.getMessage());
                    }

                } else if (signalBrandPort.brandCode() == SignalBrandPort.HT.brandCode()) {
                    //模拟中心数据
                    CentralPlanParam centralPlanParam = CentralPlanParam.builder()
                            .signalControllerID(signalInfoOp.get().getSignalId())
                            .mode(LesControlMode.ADAPTIVE_CONTROL.value())
                            .stageNoList(noStages)
                            .greenList(lenStages)
                            .duration(30 * 60)
                            .build();

                    //模拟MQ数据发送
                    htSignalController.optimizeCentral(signalInfoOp.get().getSignalId(), subJuncNo, centralPlanParam);
                } else {
                    //模拟中心数据
                    CentralPlanParam centralPlanParam = CentralPlanParam.builder()
                            .signalControllerID(signalInfoOp.get().getSignalId())
                            .mode(2)
                            .stageNoList(noStages)
                            .greenList(lenStages)
                            .build();

                    //模拟MQ数据发送
                    htSignalController.optimizeCentral(signalInfoOp.get().getSignalId(), subJuncNo, centralPlanParam);
                }
            }
        }
    }
}
