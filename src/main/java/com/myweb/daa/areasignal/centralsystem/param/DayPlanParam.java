package com.myweb.daa.areasignal.centralsystem.param;

import com.les.ads.ds.enums.ControlModeType;
import com.les.ads.ds.signal.DayPlan;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 16:52
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DayPlanParam implements SgpTransAble {
    public static final String MqObjectId = "1009";

    private String signalControllerID;
    private int noArea;
    private int noJunc;
    private int dayPlanNo;
    private List<Segment> segmentList;
    private int crossingSeqNo;

    @Override
    public String getUrl() {
        return "/dayPlan/" + signalControllerID;
    }

    @Override
    public Object transData() {
        DayPlan dayPlan = new DayPlan();
        dayPlan.setDayPlanNo(dayPlanNo);
        dayPlan.setSegmentNum(segmentList == null ? 0 : segmentList.size());
        dayPlan.setCrossingSeqNo(crossingSeqNo);
        if (segmentList != null) {
            List<com.les.ads.ds.signal.Segment> segments = new ArrayList<>();
            AtomicInteger atomicInteger = new AtomicInteger(0);
            segmentList.stream().forEach(
                    segment -> {
                        ControlModeType controlModeType = ControlModeType.parseCode(segment.getMode());
                        segments.add(com.les.ads.ds.signal.Segment.builder()
                                .segmentNo(atomicInteger.getAndIncrement())
                                .hour(segment.getHour())
                                .minute(segment.getMinute())
                                .mode(controlModeType)
                                .patternNo(segment.getPlan()).build());
                    }
            );
            dayPlan.setSegments(segments);
        }
        return dayPlan;
    }

    @Override
    public String getClazzName() {
        return DayPlanParam.class.getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return dayPlanNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
