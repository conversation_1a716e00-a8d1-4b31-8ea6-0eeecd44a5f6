package com.myweb.daa.areasignal.centralsystem.service;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.DataLookBackground;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: WJ
 * @Description: 调看阶段参数时，后台静默调看所有的系统参数
 * @Date: create in 2023/8/12 9:44
 */
@Service
@Slf4j
public class DataLookBackgroudService {


    @Autowired
    private HtSignalController htSignalController;

    /**
     * 防止前端连续狂点阶段参数调看
     */
    private Map<String, String> inProcessCrossMap = new ConcurrentHashMap<>();


    @Async(GlobalConfigure.DATA_LOOK_PROCESS_EXECUTOR)
    @EventListener
    public void mqMessageProcess(DataLookBackground dataLookBackground) {
        if (inProcessCrossMap.containsKey(dataLookBackground.getCrossId())) {
            log.error("^^^^^^后台当前已经在调看路口{}数据，请不要连续点击", dataLookBackground.getCrossId());
            return;
        }
        inProcessCrossMap.put(dataLookBackground.getCrossId(), dataLookBackground.getCrossId());
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("data-look-" + dataLookBackground.getCrossId());
            log.error("^^^^^^后台静默调看数据开始-{}", dataLookBackground.getCrossId());
            JsonResult jsonResult = htSignalController.loadDataSilent(dataLookBackground.getCrossId());
            log.error("^^^^^^后台静默调看数据结束-{}-数据项-{}", dataLookBackground.getCrossId(), jsonResult);
            stopWatch.stop();
            log.error(stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("异常-{}", e);
        }
        inProcessCrossMap.remove(dataLookBackground.getCrossId());
    }

}
