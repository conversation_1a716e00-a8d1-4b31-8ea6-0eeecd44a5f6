package com.myweb.daa.areasignal.centralsystem.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.les.ads.ds.gis.Crossing;
import com.les.ads.ds.gis.Movement;
import com.les.ads.ds.gis.arm.CrossingDTO;
import com.myweb.daa.areasignal.business.entity.CrossIdMap;
import com.myweb.daa.areasignal.business.entity.QCrossIdMap;
import com.myweb.daa.areasignal.centralsystem.param.CrossFeatureEnum;
import com.myweb.daa.areasignal.centralsystem.utils.SgpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossParam;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/18 14:27
 */
@Service
@Slf4j
public class CrossingService {
    @Getter
    private Map<String, CrossingBaseInfo> crossingBaseInfoMap = new ConcurrentHashMap<>();

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private JPAQueryFactory queryFactory;


    @Data
    public static class CrossingBaseInfo {
        private String crossingId;
        private String controllerId;
        private int noArea;
        private int noJunc;
        private int subJuncNo;

        private String name;

        /**
         * 标记的1049路口ID
         */
        private String crossingId1049;

        /**
         * 信号机所属的信号机IP地址
         */
        private String address1049Ip;

        /**
         * 状态检查时控，如果出现失败，则增加
         */
        private AtomicInteger statusCheckFailedCount = new AtomicInteger(0);
        private AtomicInteger timeCount = new AtomicInteger(0);

        /**
         * 判定路口状态是否正常
         *
         * @return
         */
        public boolean isOnline() {
            {
                return statusCheckFailedCount.get() <= 10;
            }
            //return false;
        }
    }

    /**
     * 生成默认路口1049ID
     *
     * @param crossingBaseInfo
     * @return
     */
    public static String genDefault1049CrossId(CrossingBaseInfo crossingBaseInfo) {
        return String.format("%s%03d%05d",
                GlobalConfigure.cityCode, crossingBaseInfo.getNoArea(), crossingBaseInfo.getNoJunc());
    }

    @PostConstruct
    public void initData() {

        {
            if (testSignalConfigure.isUseTest()) {
                if (testSignalConfigure.getCrossingIds() != null
                        && testSignalConfigure.getSubJuncNos() != null
                        && testSignalConfigure.getCrossingIds().size() == testSignalConfigure.getSubJuncNos().size()) {

                    for (int i = 0; i < testSignalConfigure.getCrossingIds().size(); i++) {
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(testSignalConfigure.getCrossingIds().get(i));
                        crossingBaseInfo.setControllerId(testSignalConfigure.getSignalId());
                        crossingBaseInfo.setNoArea(testSignalConfigure.getNoArea());
                        crossingBaseInfo.setNoJunc(testSignalConfigure.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(testSignalConfigure.getSubJuncNos().get(i));
                        crossingBaseInfo.setCrossingId1049(testSignalConfigure.getCrossingIds_1049().get(i));
                        crossingBaseInfo.setAddress1049Ip(testSignalConfigure.getSystemIp());
                        crossingBaseInfo.setName("调试测试");


                        crossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
                }
            }
        }
    }

    /**
     * 获取路口基本数据项
     *
     * @param signalId
     * @param subJuncNo
     * @return
     */
    public Optional<CrossingBaseInfo> getCrossingInfo(String signalId, int subJuncNo) {
        return crossingBaseInfoMap.values().stream().filter(
                crossingBaseInfo ->
                {
                    if (crossingBaseInfo.getControllerId() == null) {
                        return false;
                    }
                    return crossingBaseInfo.getControllerId().equals(signalId) && crossingBaseInfo.getSubJuncNo() == subJuncNo;
                }
        ).findFirst();
    }


    /**
     * 获取路口基本数据项
     *
     * @param signalId
     * @return
     */
    public Optional<List<CrossingBaseInfo>> getCrossingInfos(String signalId) {
        List<CrossingBaseInfo> crossingBaseInfos = crossingBaseInfoMap.values().stream().filter(
                crossingBaseInfo ->
                {
                    if (crossingBaseInfo.getControllerId() == null) {
                        return false;
                    }
                    return crossingBaseInfo.getControllerId().equals(signalId);
                }
        ).collect(Collectors.toList());
        return Optional.ofNullable(crossingBaseInfos);
    }

    /**
     * 获取路口基本数据项
     *
     * @param crossingId
     * @return
     */
    public Optional<CrossingBaseInfo> getCrossingInfo(String crossingId) {
        return Optional.ofNullable(crossingBaseInfoMap.get(crossingId));
    }


    /**
     * 根据1049路口id获取路口数据
     *
     * @param crossingId1049
     * @return
     */
    public Optional<CrossingBaseInfo> getCrossingInfoBy1049(String crossingId1049) {
        return crossingBaseInfoMap.values().stream().filter(
                crossingBaseInfo -> crossingId1049.compareToIgnoreCase(crossingBaseInfo.getCrossingId1049()) == 0
        ).findAny();
    }

    /**
     * 从sgp读取路口基本参数
     */
    public boolean updateCrossingFromSgp() {
        log.info("准备检查路口数据项,向sgp-url-{}请求路口参数", GlobalConfigure.signalParamIp);
        AtomicBoolean dataChange = new AtomicBoolean(false);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/crossing";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口数据返回异常");
                return dataChange.get();
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回路口的数据项个数是-{}", jsonArray.size());

            Map<String, CrossingBaseInfo> currentCrossingBaseInfoMap = new ConcurrentHashMap<>();
            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        Crossing crossing = jsonArray.getObject(index, Crossing.class);

                        if (crossing.getSignalControllerId() == null || crossing.getId() == null) {
                            return;
                        }

                        //针对非telvent的信号机路口进行数据过滤
                        if (!controllerService.processMqMsg(crossing.getSignalControllerId())) {
                            return;
                        }

                        //构建数据项存入本地
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(crossing.getId());
                        crossingBaseInfo.setControllerId(crossing.getSignalControllerId());
                        crossingBaseInfo.setNoArea(crossing.getNoArea());
                        crossingBaseInfo.setNoJunc(crossing.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(crossing.getNoSubJunc());
                        crossingBaseInfo.setCrossingId1049(genDefault1049CrossId(crossingBaseInfo));
                        crossingBaseInfo.setName(crossing.getName());
                        crossingBaseInfo.setAddress1049Ip("***************");

                        currentCrossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
            );

            //1、检查路口数据是否进行了删除
            crossingBaseInfoMap.keySet().stream().forEach(
                    crossingId -> {
                        if (!currentCrossingBaseInfoMap.containsKey(crossingId)) {
                            log.error("删除路口数据项-{}", crossingId);
                            dataChange.set(true);
                            crossingBaseInfoMap.remove(crossingId);
                        }
                    }
            );
            //2、检查数据更新及插入新数据
            currentCrossingBaseInfoMap.keySet().stream().forEach(
                    crossingId -> {
                        CrossingBaseInfo crossingBaseInfo = crossingBaseInfoMap.get(crossingId);
                        CrossingBaseInfo crossingBaseInfoNew = currentCrossingBaseInfoMap.get(crossingId);
                        if (crossingBaseInfo == null) {
                            dataChange.set(true);
                            log.error("添加路口数据项-{}", crossingBaseInfoNew);
                            crossingBaseInfoMap.put(crossingId, crossingBaseInfoNew);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }

        return dataChange.get();
    }

    /**
     * 从sgp读取路口基本参数
     */
    public void getCrossingFromSgp() {
        log.info("向sgp-url-{}请求路口参数", GlobalConfigure.signalParamIp);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/crossing";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口数据返回异常");
                return;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回路口的数据项个数是-{}", jsonArray.size());

            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        Crossing crossing = jsonArray.getObject(index, Crossing.class);

                        if (crossing.getSignalControllerId() == null || crossing.getId() == null) {
                            return;
                        }

                        //针对非telvent的信号机路口进行数据过滤
                        if (!controllerService.processMqMsg(crossing.getSignalControllerId())) {
                            return;
                        }

                        //构建数据项存入本地
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(crossing.getId());
                        crossingBaseInfo.setControllerId(crossing.getSignalControllerId());
                        crossingBaseInfo.setNoArea(crossing.getNoArea());
                        crossingBaseInfo.setNoJunc(crossing.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(crossing.getNoSubJunc());
                        crossingBaseInfo.setCrossingId1049(genDefault1049CrossId(crossingBaseInfo));
                        crossingBaseInfo.setName(crossing.getName());
                        crossingBaseInfo.setAddress1049Ip("***************");
                        log.error("添加路口数据项-{}", crossingBaseInfo);

                        crossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
            );

        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }
    }

    /**
     * 从本地数据库中读取映射，莱斯路口ID映射到1049路口ID
     */
    public void loadCrossIdMap() {
        //读取数据项
        List<CrossIdMap> crossIdMaps = queryFactory.selectFrom(QCrossIdMap.crossIdMap).fetch();

        //适配更新设置的1049路口ID
        crossIdMaps.stream().forEach(
                crossIdMap -> {
                    String lesCrossId = crossIdMap.getLesCrossId();
                    CrossingBaseInfo crossingBaseInfo = crossingBaseInfoMap.get(lesCrossId);
                    if (crossingBaseInfo != null) {
                        crossingBaseInfo.setCrossingId1049(crossIdMap.getCrossId1049());
                        log.error("设置莱斯路口-{}映射1049路口-{}", lesCrossId, crossIdMap.getCrossId1049());
                    }
                }
        );
    }




    /**
     * 根据1049数据项保存路口数据项
     *
     * @param crossParam
     * @param lesCrossId
     */
    public CrossingDTO genCrossing(CrossParam crossParam, String lesCrossId, String lesSignalId, String officeId) {
        CrossingDTO crossingDTO = new CrossingDTO();
        crossingDTO.setId(lesCrossId);
        crossingDTO.setIsFocus(false);
        crossingDTO.setName(crossParam.getCrossName());
        crossingDTO.setSignalControllerId(lesSignalId);
        //获取路口数据项
        CrossFeatureEnum featureEnum = CrossFeatureEnum.parseType(crossParam.getFeature());
        crossingDTO.setCharacter(featureEnum.crossingCharacter().getCode());
        crossingDTO.setFacility(0);
        crossingDTO.setLatitude(crossParam.getLatitude() != null ? Double.parseDouble(crossParam.getLatitude()) : 0);
        crossingDTO.setLongitude(crossParam.getLongitude() != null ? Double.parseDouble(crossParam.getLongitude()) : 0);

        /*
        //尝试对经纬度进行转换
        {
            try {
                Point point = Point.builder().y(crossingDTO.getLatitude()).x(crossingDTO.getLongitude()).build();
                Point wgs84ToGcj02 = CoordinateUtil.gcj02_To_Wgs84(point);
                crossingDTO.setLatitude(wgs84ToGcj02.getY());
                crossingDTO.setLongitude(wgs84ToGcj02.getX());
            } catch (Exception e) {
                log.error("change error", e);
            }
        }
        */

        crossingDTO.setNoArea(SgpUtils.getNoAreaFromStandSignalId(lesSignalId));
        crossingDTO.setNoJunc(SgpUtils.getNoJuncFromStandSignalId(lesSignalId));
        crossingDTO.setNoSubJunc(1);
        crossingDTO.setName(crossingDTO.getName());
        crossingDTO.setOfficeId(officeId);
        crossingDTO.setCityCode(SgpUtils.getCityCodeFromStandSignalId(lesSignalId));
        crossingDTO.setVariableLane(0);
        crossingDTO.setWaitingArea(0);
        return crossingDTO;
    }


    /**
     * 向sgp读取路口数据项
     * @param crossingId
     * @return
     */
    public Optional<Crossing> getCrossingData(String crossingId){
        String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/crossing/" + crossingId;
        log.info("向sgp-url-{}请求路口参数", queryUrl);
        try {

            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口数据返回异常");
                return Optional.empty();
            }

            Crossing crossing = jsonObject.getObject("data", Crossing.class);
            log.debug("返回路口的数据项是-{}", JSONObject.toJSONString(crossing));

            if(crossing != null && crossing.getLanes() != null) {
                return Optional.of(crossing);
            }

        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }

        return Optional.empty();
    }


    /**
     * 向sgp读取路口流向数据项
     *
     * @param crossingId
     * @return
     */
    public Optional<List<Movement>> getCrossingMovementData(String crossingId) {
        String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/crossing/movement/" + crossingId;
        log.info("向sgp-url-{}请求路口流向参数", queryUrl);
        try {

            List<Movement> movements = new ArrayList<>();
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求流向数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口流向数据返回异常");
                return Optional.empty();
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");

            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        Movement movement = jsonArray.getObject(index, Movement.class);

                        if (movement != null) {
                            movements.add(movement);
                        }
                    });

            log.debug("返回路口流向的数据项是-{}", JSONObject.toJSONString(movements));
            return Optional.of(movements);

        } catch (Exception e) {
            log.error("请求路口流向基础数据的时候出现异常.", e);
        }

        return Optional.empty();
    }


    /**
     * 向sgp读取路口流向数据项
     *
     * @param crossingId
     * @return
     */
    public Optional<Map<Integer, List<Movement>>> getStageMovementData(String crossingId) {
        String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/crossing/stageMovement/" + crossingId;
        log.info("向sgp-url-{}请求路口阶段流向参数", queryUrl);
        try {
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求流向数据,queryUrl-{}", queryUrl);


            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口流向数据返回异常");
                return Optional.empty();
            }

            String dataStr = jsonObject.getString("data");

            Map<Integer, List<Movement>> stageMovementMap = JSONObject.parseObject(dataStr, new TypeReference<Map<Integer, List<Movement>>>() {
            });

            log.debug("返回路口阶段流向的数据项是-{}", JSONObject.toJSONString(stageMovementMap));
            return Optional.of(stageMovementMap);

        } catch (Exception e) {
            log.error("请求阶段流向基础数据的时候出现异常.", e);
        }

        return Optional.empty();
    }

}
