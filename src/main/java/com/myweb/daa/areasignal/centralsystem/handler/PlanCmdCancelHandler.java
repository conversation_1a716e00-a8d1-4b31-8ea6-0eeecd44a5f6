package com.myweb.daa.areasignal.centralsystem.handler;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.P1049CentralSystemMsg;
import com.myweb.daa.areasignal.centralsystem.param.PlanCmdCancel;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.centralsystem.utils.P1049Sender;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/26 15:01
 */
@Component
@Slf4j
public class PlanCmdCancelHandler implements MqMsgBaseHandler {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;
    @Autowired
    private P1049Sender p1049Sender;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private TmpPlanService tmpPlanService;

    @Override
    public String getMqMsgBaseHandlerKey() {
        return getProcessBrand().name() + "###" + getObjectId();
    }

    @Override
    public SignalBrandPort getProcessBrand() {
        return SignalBrandPort.ALL;
    }

    @Override
    public String getObjectId() {
        return PlanCmdCancel.MqObjectId;
    }

    @Override
    public String getRoutingKey() {
        return PlanCmdCancel.class.getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return PlanCmdCancel.class;
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        return Optional.empty();
    }

    @Override
    public Optional<List<P1049CentralSystemMsg>> getConfigRequestMsg(String controllerId, List<Object> datas) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (!signalInfoOp.isPresent()) {
            return Optional.empty();
        }
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(controllerId);

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        //开始时间
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String startTime = simpleDateFormat.format(date);

        List<P1049CentralSystemMsg> requestMsgs = new ArrayList<>();
        datas.stream().forEach(
                data -> {
                    JSONObject jsonObject = (JSONObject) data;
                    PlanCmdCancel planCmdCancel = jsonObject.toJavaObject(PlanCmdCancel.class);
                    planCmdCancel.setSignalControllerID(controllerId);
                    log.error("收到取消指定方案命令-{}", planCmdCancel);

                    {

                        //结束系统控制
                        CrossCtrlInfo crossCtrlInfo = CrossCtrlInfo.builder()
                                .CrossID(crossingInfoOp.get().getCrossingId1049())
                                .controlMode("00")
                                .planNo(0)
                                .time(startTime)
                                .build();
                        List<CrossCtrlInfo> crossCtrlInfos = new ArrayList<>();
                        crossCtrlInfos.add(crossCtrlInfo);

                        P1049CentralSystemMsg p1049CentralSystemMsg = P1049CentralSystemMsg.builder()
                                .messageType(MessageType.REQUEST)
                                .operationName(OperationName.Set)
                                .object(crossCtrlInfos)
                                .signalBrandPort(signalBrandPort)
                                .address(crossingInfoOp.get().getAddress1049Ip())
                                .clazz(CrossCtrlInfo.class).build();
                        requestMsgs.add(p1049CentralSystemMsg);

                        log.error("准备发送控制方式-{}", crossCtrlInfo);
                    }


                }
        );
        if (requestMsgs.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(requestMsgs);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures) {
        JsonResult<?> result = p1049Sender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            return Optional.empty();
        }

        List<PlanCmdCancel> planCmdCancels = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        PlanCmdCancel planCmdCancel = jsonObject.toJavaObject(PlanCmdCancel.class);
                        planCmdCancel.setSignalControllerID(requestMessage.getSignalControllerID());
                        planCmdCancels.add(planCmdCancel);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        log.error("收到取消指定方案应答-{}", itemDatas);
        planCmdCancels.stream().forEach(
                planCmdCancel -> {
                    //主动取消临时方案
                    {
                        int subJuncNo = 1;
                        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(requestMessage.getSignalControllerID(),
                                subJuncNo);
                        if (!crossingInfoOp.isPresent()) {
                            return;
                        }
                        SignalBrandPort signalBrandPort = SignalBrandPort.getType(requestMessage.getSignalControllerID());

                        itemDatas.stream().filter(
                                itemData -> itemData.getData() instanceof BaseMessage1049
                        ).forEach(
                                itemData -> {
                                    BaseMessage1049 baseMessage1049 = (BaseMessage1049) itemData.getData();
                                    Optional<Object> firstMsgData = baseMessage1049.getFirstMsgData();
                                    if (!firstMsgData.isPresent()) {
                                        return;
                                    }

                                    if (firstMsgData.get() instanceof SDO_Error1049) {
                                        log.error("请求应答异常-{}", firstMsgData.get());
                                        return;
                                    }

                                    log.error("路口-{}取消指定方案-应答-{}", crossingInfoOp.get().getCrossingId(),
                                            firstMsgData.get());

                                    tmpPlanService.stopTmpPlan(crossingInfoOp.get().getCrossingId());

                                }
                        );
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());

        return Optional.of(mqMessageResponse);
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        //解析数据项
        List<PlanCmdCancel> controlModeCmds = new ArrayList<>();
        try {
            List<Object> objects = requestMessage.getObjectList();
            objects.forEach(
                    objectId -> {
                        JSONObject jsonObject = (JSONObject) objectId;
                        PlanCmdCancel planCmd = jsonObject.toJavaObject(PlanCmdCancel.class);
                        controlModeCmds.add(planCmd);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常-{}", e);
        }

        List<SystemControlAck> systemControlAcks = new ArrayList<>();
        controlModeCmds.stream().forEach(
                planCmd -> {
                    //指定方案
                    {
                        Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(planCmd.getSignalControllerID(), planCmd.getCrossingSeqNo());

                        systemControlAcks.add(SystemControlAck.builder().signalControllerID(planCmd.getSignalControllerID())
                                .crossingSeqNo(planCmd.getCrossingSeqNo())
                                .ack(1) //crossingInfo.isPresent() ? (crossingInfo.get().isOnline() ? 1 : 2) : 2)
                                .iden(planCmd.getPlanNo())
                                .type(6).build());
                    }
                }
        );

        //构建数据项
        MqMessage mqMessageResponse = P1049HelpUtils.buildAckMqResponseMsg(requestMessage, systemControlAcks);

        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean needSendConfigTogether(String controllerId) {
        return false;
    }

}
