package com.myweb.daa.areasignal.centralsystem.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/31 15:17
 */
public interface SgpTransAble {
    /**
     * 获取sgp数据存储地址
     *
     * @return
     */
    @JsonIgnore
    @JSONField(serialize = false)
    String getUrl();

    /**
     * 转换成sgp数据项
     *
     * @return
     */
    Object transData();

    /**
     * 获取实际的类名称，用于本地数据存储
     *
     * @return
     */
    @JsonIgnore
    @JSONField(serialize = false)
    String getClazzName();

    /**
     * 获取MQ数据ID
     *
     * @return
     */
    @JsonIgnore
    @JSONField(serialize = false)
    String getMqNo();

    /**
     * 获取数据ID，如阶段号、相位号、方案号、调度号等
     *
     * @return
     */
    @JsonIgnore
    @JSONField(serialize = false)
    int getDataNo();

    /**
     * 获取信号机ID
     *
     * @return
     */
    @JsonIgnore
    @JSONField(serialize = false)
    String getSignalId();
}
