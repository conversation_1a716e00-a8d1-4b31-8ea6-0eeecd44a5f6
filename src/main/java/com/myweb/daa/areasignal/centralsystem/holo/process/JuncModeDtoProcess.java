package com.myweb.daa.areasignal.centralsystem.holo.process;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.holo.cmd.JuncModeDto;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.param.PlanCmdCancel;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/23 19:08
 */
@Service
@Slf4j
public class JuncModeDtoProcess {
    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private HtSignalController htSignalController;

    @Autowired
    private MessagePublisher messagePublisher;

    @EventListener
    @Async(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public void juncModeDtoProcess(JuncModeDto juncModeDto) {
        log.error("收到全息指定模式-{}", juncModeDto);

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(juncModeDto.getNoArea(),
                juncModeDto.getNoJunc());

        if (!signalInfoOp.isPresent()) {
            log.error("没有找到信号机进行全息指定模式-{}", juncModeDto);
            return;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalInfoOp.get().getSignalId());

        int subJuncNo = 1;
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalInfoOp.get().getSignalId(), subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            log.error("没有找到路口进行全息指定模式-{}", juncModeDto);
            return;
        }

        if (juncModeDto.getMode() != LesControlMode.OFFLINE.value()) {
            log.error("路口进行全息指定模式-{}只支持恢复", juncModeDto);
            return;
        }


        if (signalBrandPort.brandCode() == SignalBrandPort.YL.brandCode() ||
                signalBrandPort.brandCode() == SignalBrandPort.DW.brandCode()) {

            JsonResult jsonResult = htSignalController.modeStageCental(signalInfoOp.get().getSignalId(), subJuncNo, LesControlMode.OFFLINE.value());

            log.error("指定模式-取消结果-{},原始-{}", jsonResult, juncModeDto);
        } else if (signalBrandPort.brandCode() == SignalBrandPort.HT.brandCode()) {
            //模拟临时方案取消命令
            PlanCmdCancel planCmdCancel = PlanCmdCancel.builder().planNo(1)
                    .signalControllerID(crossingInfoOp.get().getControllerId())
                    .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                    .build();

            List<Object> objectList = new ArrayList<>();
            objectList.add(planCmdCancel);
            MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                    PlanCmdCancel.MqObjectId, objectList);
            messagePublisher.publishMessage(mqMessage);
        } else
        {
            //构建数据项
            JsonResult jsonResult = htSignalController.modeStageCentral(signalInfoOp.get().getSignalId(), subJuncNo, 1, 1,
                    50, false
            );
            log.info("指定模式-转换-取消锁定结果-{},原始-{}", jsonResult, juncModeDto);
        }
    }
}
