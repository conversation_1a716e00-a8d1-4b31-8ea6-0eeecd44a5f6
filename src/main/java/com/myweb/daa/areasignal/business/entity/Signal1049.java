package com.myweb.daa.areasignal.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/2/9 14:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class Signal1049 {

    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    /**
     * 1049信号机id
     */
    private String signal1049;

    /**
     * 1049信号机名称
     */
    private String name;

}
