package com.myweb.daa.areasignal.business.service;

import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;

import java.util.List;

public interface DataLoadService {
    List<CrossingService.CrossingBaseInfo> getCrossings(SignalBrandPort signalBrandPort, String address);

    List<CrossingService.CrossingBaseInfo> getCrossings(SignalBrandPort signalBrandPort);

}
