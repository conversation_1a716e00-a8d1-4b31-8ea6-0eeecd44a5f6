package com.myweb.daa.areasignal.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @ClassName: AreaSignal
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/8 15:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class CrossIdMap {

    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    /**
     * 区域机所处的城市
     */
    private String lesCrossId;

    /**
     * 区域号
     */
    private String crossId1049;

}
