package com.myweb.daa.areasignal.business;


import com.myweb.commondevice.entity.SignalLS;
import com.myweb.daa.areasignal.business.entity.AreaSignal;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: LocalData
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/8 15:11
 */
@Slf4j
@Data
@Component
public class LocalData {

    /**
     * 基础数据是否加载正确
     */
    private boolean flag = true;

    /**
     * 区域机的基础配置
     */
    private AreaSignal areaSignal;

    /**
     * 加载数据告警
     */
    private List<String> errorLog = Collections.synchronizedList(new ArrayList<>());

    /**
     * 加载数据告警
     * 从ops加载过来的数据项
     */
    private ConcurrentHashMap<String, SignalLS> signalLSConcurrentHashMap = new ConcurrentHashMap<>();
}
