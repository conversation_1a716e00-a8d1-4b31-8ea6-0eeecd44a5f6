package com.myweb.daa.areasignal.business.service;

import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/3/30 16:53
 */
@Service
public class DataLoad1049Service implements DataLoadService {

    @Autowired
    private CrossingService crossingService;

    @Override
    public List<CrossingService.CrossingBaseInfo> getCrossings(SignalBrandPort signalBrandPort, String address) {
        return crossingService.getCrossingBaseInfoMap().values().stream().filter(
                crossingBaseInfo -> crossingBaseInfo.getAddress1049Ip().compareToIgnoreCase(address) == 0
        ).collect(Collectors.toList());
    }

    @Override
    public List<CrossingService.CrossingBaseInfo> getCrossings(SignalBrandPort signalBrandPort) {
        return crossingService.getCrossingBaseInfoMap().values().stream().collect(Collectors.toList());
    }
}
