package com.myweb.daa.areasignal.business.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignalStatus {

    /**
     * 信号机ID
     */
    private String signalId;

    /**
     * 设备基础Id
     */
    private String baseId;

    /**
     * 信号机名称
     */
    private String name;

    /**
     * 当前运行的相位
     */
    private Integer active;

    /**
     * 当前运行的相位字符
     */
    private String activeStr;

    /**
     * 当前相位运行的时间
     */
    private int activeTime;

    /**
     * 当前运行的模式
     */
    private String mode;

    /**
     * 信号机当前状态
     */
    private int status;

    /**
     * 当前运行的相序
     */
    private List<Integer> stage;

    /**
     * 当前运行的配时
     */
    private List<Integer> lenStage;

    /**
     * 当前运行的相序名称
     */
    private List<String> stageNames;

    /**
     * 运行时段的类型 参考Segment中的type
     */
    private String type;

    /**
     * 当前走的方案编号
     */
    private int planNo;

    /**
     * 判定信号机运行状态数据是否发生了变化
     *
     * @param signalStatus
     * @return
     */
    public boolean isChange(SignalStatus signalStatus) {
        boolean result = false;
        /**检查数据项是否发生变化
         * 时段类型发生变化
         * 方案编号发生变化
         * 状态发生变化
         * 控制方式发生变化
         */
        if ((!signalStatus.getType().equalsIgnoreCase(this.getType()))
                || (signalStatus.getPlanNo() != this.getPlanNo())
                || (signalStatus.getStatus() != this.getStatus())
                || (signalStatus.getMode() != null && (!signalStatus.getMode().equalsIgnoreCase(this.getMode())))) {
            result = true;
        } else if ((signalStatus.getLenStage() == null && this.getLenStage() != null)
                || (signalStatus.getLenStage() != null && this.getLenStage() == null)) {
            result = true;
        } else if ((signalStatus.getStage() == null && this.getStage() != null)
                || (signalStatus.getStage() != null && this.getStage() == null)) {
            result = true;
        } else if ((signalStatus.getMode() == null && this.getMode() != null)
                || (signalStatus.getMode() != null && this.getMode() == null)) {
            result = true;
        } else {
            if (signalStatus.getStage() != null && this.getStage() != null) {
                //比较相位数据是否发生变化
                //比较长度是否相等
                if (signalStatus.getStage().size() != this.getStage().size()) {
                    result = true;
                } else {
                    //比较相位是否发生变化
                    for (int i = 0; i < signalStatus.getStage().size(); i++) {
                        if (signalStatus.getStage().get(i).intValue() != this.getStage().get(i).intValue()) {
                            result = true;
                            break;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 判定数据项是否正常
     *
     * @return
     */
    public boolean isReady() {
        if (this.getStage() == null) {
            return false;
        } else if (this.getLenStage() == null) {
            return false;
        }
        return true;
    }

    /**
     * 链路状态发生变化
     *
     * @param signalStatus
     * @return
     */
    public boolean isLinkChange(SignalStatus signalStatus) {
        return this.status != signalStatus.status;
    }

}
