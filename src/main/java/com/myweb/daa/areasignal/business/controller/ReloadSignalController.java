package com.myweb.daa.areasignal.business.controller;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.param.PhaseParam;
import com.myweb.daa.areasignal.centralsystem.param.StageManualConfig;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.protocol.p1049.process.P1049Manager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping
@Api(description = "重新加载数据")
public class ReloadSignalController {

    @Autowired
    private HtSignalService htSignalService;

    @Autowired
    private Ht1049SignalCacheService signalStatusController;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private P1049Manager p1049Manager;

    @GetMapping("/resub/{type}/{ip}")
    @Transactional
    @ApiOperation(value = "重新订阅")
    public JsonResult loadData(@PathVariable SignalBrandPort type, @PathVariable String ip) {
        htSignalService.reSubscribe(type, ip);
        return new JsonResult(true, "重新订阅成功");
    }


    @GetMapping("/loadAllData/{crossingId}")
    @Transactional
    @ApiOperation(value = "路口参数")
    public JsonResult loadData(@PathVariable String crossingId) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口");
        }

        if (!SignalBrandPort.is1049System(crossingInfoOp.get().getControllerId())) {
            return new JsonResult(false, "不是1049信号机不支持重新加载");
        }

        //删除手动配置参数数据项,重庆手动配置要求
        try{
            signalCacheService.deleteDataEntity(crossingInfoOp.get().getControllerId(), StageManualConfig.class.getSimpleName());
        }catch (Exception e){
            log.error("删除手动配置参数异常", e);
        }

        JsonResult jsonResult = htSignalService.loadData(crossingInfoOp.get());
        jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
        log.error("reload Data == {}", jsonResult);

        //订阅参数
        List<String> crossings = new ArrayList<>();
        crossings.add(crossingId);
        htSignalService.reportSetting(crossings, "CrossCycle", true);
        htSignalService.reportSetting(crossings, "CrossStage", true);
        htSignalService.reportSetting(crossings, "CrossPlan",true);
        return jsonResult;
    }

    @GetMapping("/loadAllData")
    @Transactional
    @ApiOperation(value = "路口参数")
    public JsonResult loadData2(@RequestParam String crossingId) {
        return loadData(crossingId);
    }


    @GetMapping("/login/{brand}/{address}")
    public JsonResult simLogin(@PathVariable String brand, @PathVariable String address){

        Optional<SignalBrandPort> brandPortOp = Arrays.stream(SignalBrandPort.values()).filter(
                signalBrandPort -> signalBrandPort.name().equalsIgnoreCase(brand)
        ).findAny();
        if(!brandPortOp.isPresent()){
            return new JsonResult(false, "异常的品牌");
        }

        String addToken = p1049Manager.addToken(address, brandPortOp.get());
        return new JsonResult(true, "添加token成功" + addToken);
    }

    @GetMapping("/del1049Phase")
    @Transactional
    @ApiOperation(value = "删除路口内存相位参数")
    public JsonResult del1049Phase(@RequestParam String crossingId) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口");
        }


        signalCacheService.deleteDataEntity(crossingInfoOp.get().getControllerId(), PhaseParam.class.getSimpleName());

        return new JsonResult<>(true, "0", "删除成功", "");
    }


    @GetMapping("/del1049Stage")
    @Transactional
    @ApiOperation(value = "删除路口内存阶段参数")
    public JsonResult del1049Stage(@RequestParam String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "未找到这个路口的数据");
        }

        signalCacheService.deleteDataEntity(crossingInfoOp.get().getControllerId(), StageParam.class.getSimpleName());

        return new JsonResult<>(true, "0", "删除成功", "");
    }


}
