package com.myweb.daa.areasignal.business.bean;

/**
 * @ClassName: NetworkType
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/8 15:45
 */
public enum LinkStatus {

    /**
     * 未联机
     */
    UNLINK(0, "未联机"),
    /**
     * 已联机
     */
    LINKED(1, "已联机"),
    /**
     * 已握手
     */
    HANDSHAKE(2, "已握手"),
    ;

    /**
     * 类型值
     */
    private int value;
    /**
     * 报文类型
     */
    private String description;


    LinkStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int value() {
        return this.value;
    }

    public String description() {
        return description;
    }
}
