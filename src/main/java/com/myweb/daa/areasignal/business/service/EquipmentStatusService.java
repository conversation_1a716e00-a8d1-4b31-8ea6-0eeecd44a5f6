package com.myweb.daa.areasignal.business.service;

import com.myweb.daa.areasignal.business.LocalData;
import com.myweb.daa.areasignal.business.bean.LinkStatus;
import com.myweb.daa.areasignal.business.bean.Ops1049SystemStatus;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.common.OuterProtocolType;
import com.myweb.daa.areasignal.protocol.common.utils.EquipmentStatus;
import com.myweb.daa.areasignal.protocol.p1049.process.Login1049Process;
import com.myweb.daa.areasignal.protocol.p1049.process.MessageSendProcess;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class EquipmentStatusService {

    @Autowired
    private LocalData localData;

    @Autowired
    private MessageOuterPublisher messageOuterPublisher;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private Login1049Process login1049Process;

    @Autowired
    private MessageSendProcess messageSendProcess;


    /**
     * 接收设备状态数据事件
     *
     * @param equipmentStatus
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void equipmentStatus(EquipmentStatus equipmentStatus) {
        if (equipmentStatus.getOuterProtocolType() == OuterProtocolType.P1049_SIGNAL) {
            if (equipmentStatus.isLinkChange()) {
                Optional<SignalBrandPort> signalBrandPort = SignalBrandPort.getType((int) equipmentStatus.getLocalPort(),
                        (int)(equipmentStatus.getPort()));
                log.info("1049协议设备链路变化-{}-本地端口{}-系统类型{}", equipmentStatus, equipmentStatus.getLocalPort(),
                        signalBrandPort);
                if (signalBrandPort.isPresent()) {
                    Ops1049SystemStatus ops1049SystemStatus = Ops1049SystemStatus.builder().linkStatus(
                                    equipmentStatus.isActive() ?
                                            LinkStatus.LINKED : LinkStatus.UNLINK
                            ).signalBrandPort(signalBrandPort.get())
                            .address(equipmentStatus.getIp()).build();
                    messagePublisher.publishMessage(ops1049SystemStatus);
                }
            }
        }
    }


    /**
     * 接收设备状态数据事件
     *
     * @param ops1049SystemStatus
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void equipmentStatus(Ops1049SystemStatus ops1049SystemStatus) {
        log.error("系统链路状态变化-{}", ops1049SystemStatus);

        if (LinkStatus.LINKED.value() == ops1049SystemStatus.getLinkStatus().value()) {
            P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(Optional.of(SignalBrandPort.DH));
            if((sysConfig != null) && (sysConfig.getRemoteIp() != null) && !sysConfig.getRemoteIp().isEmpty()) {
                log.error("系统重新请求登录-{}", ops1049SystemStatus);
                BaseMessage1049 loginMsg = login1049Process.buildLoginMsg(sysConfig);

                loginMsg.setSignalBrandPortOptional(Optional.of(SignalBrandPort.DH));
                //设置头数据项
                P1049Utils.setBaseHeader1049(loginMsg, p1049Configure, "", MessageType.REQUEST, messageSendProcess.getSeq());
                //设置发送目的
                loginMsg.setAddress(sysConfig.getRemoteIp());
                //数据发送
                InterProtocol interProtocol = P1049Utils.buildMessage(loginMsg.getAddress(), InterProtocolType.P1049_MSG
                        , loginMsg, false, false);
                messageOuterPublisher.sendMessageOuter(interProtocol);
            }
        }
    }


}
