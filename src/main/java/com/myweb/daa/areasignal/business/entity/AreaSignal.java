package com.myweb.daa.areasignal.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @ClassName: AreaSignal
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/8 15:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class AreaSignal {

    @Id
    @GeneratedValue
    private long id;

    /**
     * 区域机所处的城市
     */
    private String cityCode;

    /**
     * 区域号
     */
    private long areaNo;

}
