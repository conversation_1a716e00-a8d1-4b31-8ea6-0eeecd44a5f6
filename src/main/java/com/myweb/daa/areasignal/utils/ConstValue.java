package com.myweb.daa.areasignal.utils;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName: ConstValue
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/5 10:12
 */
public class ConstValue {
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 由于报文存在接收和发送的报文id是一样的报文，此处作特殊处理，接收的报文按照原先id处理
     * 发送的报文，内部使用添加区分号
     */
    public static final short MESSAGE_IN_OUT_SPILTER_FLAG = 10000;

    /**
     * 1049协议数据起始
     */
    public static final short MESSAGE_P1049_FLAG = 500;

    /**
     * IP格式
     */
    public static final String IP_PATTERN = "(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})(\\.(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})){3}";

    /**
     * 应答报文关键字区分项
     */
    public static final String ACK_KEY_FLAG = "-";

    /**
     * 一般时段的数据项
     */
    public static final String SEGMENT_TYPE = "0";

    /**
     * 节假日时段的数据项
     */
    public static final String HSEGMENT_TYPE = "8";

    /**
     * 分隔符保存
     */
    public static final String FILTER_GAP = "@";

    /**
     * 节假日时段数据存储前缀
     */
    public static final String HSEMENT_TYPE_SAVE_PREFIX = "8@";

    /**
     * 日计划数据项间隔
     */
    public static final int DAYPLAN_TYPE_GAP = 1000;

    /**
     * 星期时段的数据项
     */
    public static final List<String> WSEGMENT_TYPE = Arrays.asList(new String[]{"1", "2", "3", "4", "5", "6", "7"});


    /**
     * 设备状态
     */
    public static final int TabJuncStatus = 1;

    /**
     * 控制方式编号
     */
    public static final int TabJuncMode = 2;

    /**
     * 路口周期参数
     */
    public static final int TabCycle = 3;

    /**
     * 路口相位
     */
    public static final int TabStage = 4;

    /**
     * 路口优化时机报文数据项
     */
    public static final int TabPriorityMoment = 10001;

    /**
     * 路口优化周期变化
     */
    public static final int TabCycleChanges = 10002;

    /**
     * 路口方案变化
     */
    public static final int TabPlanChanges = 10003;

    /**
     * 灯故障数据项
     */
    public static final int TabLampStatus = 13;

    /**
     * 路口方案变化
     */
    public static final int TabJuncPlan = 206;

    /**
     * 无电缆控制方式
     */
    public static final int M_SINGLE_NONCABLE = 21;

    /**
     * 方案个数从0-19
     */
    public static final int MAX_PLANNO = 19;

    /**
     * 平台数据推送
     */
    public static final String PlatFormSystem = "PlatFormSystem";

    /**
     * 中心机数据推送
     */
    public static final String CenterSystem = "CenterSystem";

    /**
     * 异常的阶段编号
     */
    public static final String ErrorStageNo = "65535";

}
