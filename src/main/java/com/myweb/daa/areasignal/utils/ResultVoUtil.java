package com.myweb.daa.areasignal.utils;


/**
 * <AUTHOR>
 * 结果返回数据工具类
 * Created by lyj on 2017/10/14.
 */
public class ResultVoUtil {

    /**
     * 数据成功返回
     *
     * @param object
     * @return
     */
    public static ResultVo<Object> success(Object object) {
        ResultVo<Object> resultVo = new ResultVo<>();
        resultVo.setCode(ResultCode.SUCCESS.code());
        resultVo.setMsg(ResultCode.SUCCESS.message());
        resultVo.setData(object);
        return resultVo;
    }

    /**
     * 数据成功默认返回
     *
     * @return
     */
    public static ResultVo<Object> success() {
        return success(null);
    }

    /**
     * 根据返回的结果
     *
     * @param resultCode
     * @return
     */
    public static ResultVo<String> result(ResultCode resultCode) {
        ResultVo<String> resultVo = new ResultVo<>();
        resultVo.setCode(resultCode.code());
        resultVo.setMsg(resultCode.message());
        return resultVo;
    }

}
