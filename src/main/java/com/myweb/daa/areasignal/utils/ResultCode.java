package com.myweb.daa.areasignal.utils;

/**
 * The enum Result code.
 *
 * @ClassName: ResultCode
 * @Description: 异常返回code定义
 * @Author: king
 * @CreateDate: 2018 /12/3 10:13
 */
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(0, "成功"),
    /**
     * 失败
     */
    FAILED(-1, "失败"),

    /**
     * Channelholder no equipment connectted result code.
     * 数据发送错误code
     */
    CHANNELHOLDER_NO_EQUIPMENT_CONNECTTED(-2000, "没有对应的IP设备"),

    ;

    private Integer code;
    private String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * Code integer.
     *
     * @return the integer
     */
    public Integer code() {
        return this.code;
    }

    /**
     * Message string.
     *
     * @return the string
     */
    public String message() {
        return this.message;
    }
}
