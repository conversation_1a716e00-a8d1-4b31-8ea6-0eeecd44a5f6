package com.myweb.daa.areasignal.utils;

import io.netty.util.HashedWheelTimer;
import io.netty.util.Timer;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName: TimerHolder
 * @Description: 时间控制类，netty中的时间轮实现
 * @Author: king
 * @CreateDate: 2019/6/17 11:29
 */
public class TimerHolder {

    private final static long defaultTickDuration = 10;

    private static class DefaultInstance {
        static final HashedWheelTimer INSTANCE = new HashedWheelTimer(new NamedThreadFactory(
                "DefaultTimer" + defaultTickDuration, true),
                defaultTickDuration, TimeUnit.MILLISECONDS);
    }

    private TimerHolder() {
    }

    /**
     * Get a singleton instance of {@link Timer}. <br>
     * The tick duration is {@link #defaultTickDuration}.
     *
     * @return Timer
     */
    public static HashedWheelTimer getTimer() {
        return DefaultInstance.INSTANCE;
    }
}
