package com.myweb.daa.areasignal.resttemplate;


import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * @ClassName: LoadBalancedRest
 * @Description:
 * @Author: king
 * @CreateDate: 2019/10/25 8:46
 */
@Configuration
@ConditionalOnMissingBean(CommonRest.class)
public class LoadBalancedRest {
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder.setConnectTimeout(Duration.ofMillis(10000)).setReadTimeout(Duration.ofMillis(10000)).build();
    }
}
