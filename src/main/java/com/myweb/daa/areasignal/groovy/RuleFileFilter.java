package com.myweb.daa.areasignal.groovy;

import java.io.File;
import java.io.FilenameFilter;

/**
 * @ClassName: JbbpFileFilter
 * @Description:
 * @Author: king
 * @CreateDate: 2019/1/10 18:47
 */
public class RuleFileFilter implements FilenameFilter {

    /**
     * 读取的用户定义报文文件名称后缀
     */
    public static final String USER_DEFINED_RULE_TAIL = ".java";

    @Override
    public boolean accept(File dir, String name) {
        return name.endsWith(USER_DEFINED_RULE_TAIL);
    }
}
