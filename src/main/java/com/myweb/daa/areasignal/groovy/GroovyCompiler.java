package com.myweb.daa.areasignal.groovy;

import groovy.lang.GroovyClassLoader;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;

/**
 * Groovy code compiler
 */
@Data
@Slf4j
public class GroovyCompiler implements IDynamicCodeCompiler {

    /**
     * Compiles Groovy code and returns the Class of the compiles code.
     *
     * @param sCode
     * @param sName
     * @return
     */
    @Override
    public Class compile(String sCode, String sName) {
        GroovyClassLoader loader = getGroovyClassLoader();
        log.warn("Compiling filter: " + sName);
        return loader.parseClass(sCode, sName);
    }

    /**
     * @return a new GroovyClassLoader
     */
    GroovyClassLoader getGroovyClassLoader() {
        return new GroovyClassLoader();
    }

    /**
     * Compiles groovy class from a file
     *
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public Class compile(File file) throws IOException {
        GroovyClassLoader loader = getGroovyClassLoader();
        return loader.parseClass(file);
    }

}
