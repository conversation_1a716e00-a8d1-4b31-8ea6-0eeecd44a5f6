package com.myweb.daa.areasignal.monitor;

import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.P1049Manager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/9/28 16:34
 */
@Configuration
@Endpoint(id = "p1049-info")
public class P1049SystemMonitor {

    @Autowired
    private P1049Manager p1049Manager;

    @ReadOperation
    public List<P1049Manager.TokenData> invoke() {
        List<P1049Manager.TokenData> tokenData = new ArrayList<>();
        p1049Manager.getTokenMap().values().stream().forEach(
                data -> data.values().stream().forEach(
                        token -> tokenData.add(token)
                )
        );
        return tokenData;
    }
}
