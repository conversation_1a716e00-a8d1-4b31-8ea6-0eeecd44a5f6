package com.myweb.daa.areasignal.monitor;


import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: ChannelMonitor
 * @Description: 继承AbstractEndpoint实现监控
 * @Author: king
 * @CreateDate: 2018/12/4 8:19
 */
@Configuration
@Endpoint(id = "data-info-ht")
public class HtSignalMonitor {

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @ReadOperation
    public ConcurrentHashMap<String, Map<String, P1049Entity>> invoke() {
        return ht1049SignalCacheService.getSignalInfoMap();
    }
}
