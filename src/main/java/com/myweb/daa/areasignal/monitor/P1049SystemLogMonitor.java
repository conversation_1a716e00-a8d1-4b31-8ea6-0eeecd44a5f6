package com.myweb.daa.areasignal.monitor;

import com.myweb.daa.areasignal.protocol.p1049.process.P1049Manager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Queue;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/9/28 16:34
 */
@Configuration
@Endpoint(id = "p1049-log-info")
public class P1049SystemLogMonitor {

    @Autowired
    private P1049Manager p1049Manager;

    @ReadOperation
    public Queue<P1049Manager.TokenData> invoke() {
        return p1049Manager.getTokenMapLog();
    }
}
