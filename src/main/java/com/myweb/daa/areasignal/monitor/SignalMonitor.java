package com.myweb.daa.areasignal.monitor;


import com.myweb.daa.areasignal.business.LocalData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: ChannelMonitor
 * @Description: 继承AbstractEndpoint实现监控
 * @Author: king
 * @CreateDate: 2018/12/4 8:19
 */
@Configuration
@Endpoint(id = "data-info")
public class SignalMonitor {

    @Autowired
    private LocalData localData;

    @ReadOperation
    public LocalData invoke() {
        return localData;
    }
}
