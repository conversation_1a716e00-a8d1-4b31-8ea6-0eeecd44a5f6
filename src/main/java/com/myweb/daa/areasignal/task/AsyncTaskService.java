package com.myweb.daa.areasignal.task;


import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.netty.NettyClient;
import com.myweb.daa.areasignal.netty.NettyComm3;
import com.myweb.daa.areasignal.netty.NettyServer;
import com.myweb.daa.areasignal.netty.link.ClientType;
import com.myweb.daa.areasignal.netty.link.LinkManager;
import com.myweb.daa.areasignal.netty.link.ServerType;
import com.myweb.daa.areasignal.netty.reconnect.ReconnectAble;
import com.myweb.daa.areasignal.netty.reconnect.ReconnectEvent;
import com.myweb.daa.areasignal.netty.stop.StopAble;
import com.myweb.daa.areasignal.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 初始化监听端口设置
 * @date 2014年3月15日
 */
@Service
@Slf4j
public class AsyncTaskService {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private LinkManager linkManager;

    @Autowired
    private Environment env;

    /**
     * 重连随机数
     */
    private static Random delayRandom = new Random(System.currentTimeMillis());

    @Async
    public void executeServerAsyncTask(ServerType.PortPotocol portPotocol) throws Exception {
        executeServerAsyncTask(portPotocol.getPort(), portPotocol.getProtocol());
    }

    @Async
    public void executeServerAsyncTask(int port, String protocol) throws Exception {
        log.info("开始绑定端口数据port=" + port + ", 协议=" + protocol);
        NettyServer nettyServer = new NettyServer(port, protocol, messagePublisher, env);
        nettyServer.bind();
        linkManager.addLink(nettyServer);
    }

    @Async
    public void executeClientAsyncTask(ClientType.IpPortProtocol ipPortProtocol) throws Exception {
        if (ipPortProtocol.getIp().matches(ConstValue.IP_PATTERN)) {
            /**IP类型的链路*/
            executeClientAsyncTask(ipPortProtocol.getIp(), ipPortProtocol.getPort(),
                    ipPortProtocol.getLocalPort(), ipPortProtocol.getProtocol());
        } else {
            /**串口类型的数据*/
            executeCommtAsyncTask(ipPortProtocol.getIp(), ipPortProtocol.getProtocol());
        }
    }


    @Async
    public void executeClientAsyncTask(String ip, int port, int localPort, String protocol) throws Exception {
        log.info("开始尝试连接端口数据port=" + port + ", ip= " + ip + ", 协议=" + protocol);
        NettyClient nettyClient = new NettyClient(ip, port, localPort, protocol, messagePublisher, env);
        nettyClient.connect();
        linkManager.addLink(nettyClient);
    }

    /**
     * @param address
     * @param protocol
     */
    @Async
    public void executeCommtAsyncTask_old(String address, String protocol) {
        log.info("开始尝试串口数据address=" + address + ", 协议=" + protocol);
        NettyComm3 nettyComm = new NettyComm3(address, protocol, messagePublisher, env);
        nettyComm.connect();
        linkManager.addLink(nettyComm);

    }

    /**
     * @param address
     * @param protocol
     */
    @Async
    public void executeCommtAsyncTask(String address, String protocol) {
        log.info("开始尝试串口数据address=" + address + ", 协议=" + protocol);
        NettyComm3 nettyComm = new NettyComm3(address, protocol, messagePublisher, env);
        nettyComm.connect();
        linkManager.addLink(nettyComm);
    }

    /**
     * @param address
     * @param protocol
     */
    @Async
    public void executeCommtAsyncTaskRandomDelay(String address, String protocol) {
        int delay = delayRandom.nextInt(30) * 1000;
        log.info("延迟" + delay + "ms, 尝试连接串口数据address=" + address + ", 协议=" + protocol);
        NettyComm3 nettyComm = new NettyComm3(address, protocol, messagePublisher, env);
        linkManager.addLink(nettyComm);
        messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(nettyComm).build());
    }


    /**
     * @param reconnectAble
     */
    @Async
    public void executeReconnectEvent(ReconnectAble reconnectAble) {
        log.info("---开始重新连接---");
        reconnectAble.reconnect();
    }


    /**
     * 停止服务
     *
     * @param stopAble
     */
    @Async
    public void executeStopEvent(StopAble stopAble) {
        log.info("---开始停止---");
        stopAble.stop();
    }

}
