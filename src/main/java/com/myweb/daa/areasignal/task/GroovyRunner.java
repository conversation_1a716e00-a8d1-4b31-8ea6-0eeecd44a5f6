package com.myweb.daa.areasignal.task;

import com.myweb.daa.areasignal.centralsystem.service.AcsCharaService;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SystemIpService;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import com.myweb.daa.areasignal.groovy.SelfDefinedRuleManager;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.netty.link.ClientType;
import com.myweb.daa.areasignal.netty.link.LinkManager;
import com.myweb.daa.areasignal.netty.link.ServerType;
import com.myweb.daa.areasignal.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 系统初始化操作类
 * @date 2014年3月15日
 */
@Component
@Order(value = 3)
@Slf4j
public class GroovyRunner implements CommandLineRunner {

    @Autowired
    private SelfDefinedRuleManager selfDefinedRuleManager;


    @Override
    public void run(String... args) throws Exception {
        log.info(">>>>>>>>>>>>>>>自定义规则准备，执行数据准备<<<<<<<<<<<<");
        try {
            selfDefinedRuleManager.loadData();
        } catch (Exception e) {
            log.error("异常", e);
        }
    }
}
