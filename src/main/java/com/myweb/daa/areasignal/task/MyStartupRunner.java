package com.myweb.daa.areasignal.task;

import com.myweb.daa.areasignal.centralsystem.service.*;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.netty.link.ClientType;
import com.myweb.daa.areasignal.netty.link.LinkManager;
import com.myweb.daa.areasignal.netty.link.ServerType;
import com.myweb.daa.areasignal.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 系统初始化操作类
 * @date 2014年3月15日
 */
@Component
@Order(value = 2)
@Slf4j
public class MyStartupRunner implements CommandLineRunner {

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private LinkManager linkManager;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private SystemIpService systemIpService;

    @Autowired
    private AcsCharaService acsCharaService;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;

    @Override
    public void run(String... args) throws Exception {

        log.error(">>>>>>>>>>>>>>>服务启动执行，执行数据准备<<<<<<<<<<<<");

        log.error(">>>step0-特殊方案编号转换");
        tmpPlanDataService.spChangPlanNo();

        log.error(">>>step1-从数据库中加载数据项");
        /** 基础内存数据*/
        ht1049SignalCacheService.loadDataFromDb();
        signalCacheService.loadDataFromDb();

        /**信号机路口数据**/
        if (!testSignalConfigure.isUseTest()) {
            log.error(">>>step1.1-从sgp读取信号机以及路口数据项");
            try {
                controllerService.getControllerFromSgp();
                crossingService.getCrossingFromSgp();
            } catch (Exception e) {
                log.error("step1.1异常-{}", e);
            }

            //配置映射
            log.error(">>>step1.2-设置信号机以及路口的1049编号");
            try {
                controllerService.loadSignalIdMap();
                crossingService.loadCrossIdMap();
            } catch (Exception e) {
                log.error("step1.2异常-{}", e);
            }

            //设置系统ip地址
            log.error(">>>step1.3-设置信号机以及路口的系统ip地址");
            try {
                systemIpService.updateSystemIp();
            } catch (Exception e) {
                log.error("step1.3异常-{}", e);
            }

        }

        {
            log.error(">>>step1.4-初始化进行阶段参数构建");
            crossingService.getCrossingBaseInfoMap().values().forEach(
                    crossingBaseInfo -> {
                        ht1049SignalCacheService.genCentralSystemStage(crossingBaseInfo);
                    }
            );
        }

        //初始化set队列数据
        log.error(">>>step1.5-初始化set消息队列数据项");
        acsCharaService.initKey();

        //显示系统配置参数
        log.error("系统配置参数-{}", p1049Configure.getSysConfigMap());

        log.error(">>>step10-开始监听类协议监听");
        if (GlobalConfigure.listenPortList.size() != GlobalConfigure.listenProtocolList.size()) {
            log.error("attention connect port is larger than protocol size ...");
        } else {
            /**添加从数据库中读取的数据项*/
            List<ServerType.PortPotocol> serverList = linkManager.getServerList();
            serverList.forEach(
                    server ->
                    {
                        GlobalConfigure.listenPortList.add(server.getPort());
                        GlobalConfigure.listenProtocolList.add(server.getProtocol());
                    }
            );

            for (int i = 0; i < GlobalConfigure.listenPortList.size(); i++) {
                if (i < GlobalConfigure.listenProtocolList.size()) {
                    log.error("在端口{}加载协议{} ", GlobalConfigure.listenPortList.get(i),
                            GlobalConfigure.listenProtocolList.get(i));
                    asyncTaskService.executeServerAsyncTask(GlobalConfigure.listenPortList.get(i),
                            GlobalConfigure.listenProtocolList.get(i));
                }
            }
        }

        log.error(">>>step11-服务启动执行，连接远程端口协议加载");
        if ((GlobalConfigure.connectIPList.size() != GlobalConfigure.connectPortList.size())
                || (GlobalConfigure.connectIPList.size() != GlobalConfigure.connectProtocolList.size())) {
            log.error("configured connect ip、port、protocol not equal ... ip =[{}] port=[{}] protocol=[{}]",
                    GlobalConfigure.connectIPList, GlobalConfigure.connectPortList, GlobalConfigure.connectProtocolList);
        } else {

            /**添加从数据库中读取的数据项*/
            List<ClientType.IpPortProtocol> clientList = linkManager.getClientList();
            clientList.stream().filter(
                    client -> client.getIp().matches(ConstValue.IP_PATTERN)
            ).forEach(
                    client ->
                    {
                        GlobalConfigure.connectIPList.add(client.getIp());
                        GlobalConfigure.connectPortList.add(client.getPort());
                        GlobalConfigure.connectLocalPortList.add(client.getLocalPort());
                        GlobalConfigure.connectProtocolList.add(client.getProtocol());
                    }
            );

            for (int i = 0; i < GlobalConfigure.connectIPList.size(); i++) {
                log.error("远程地址{}端口{}加载协议{} ", GlobalConfigure.connectIPList.get(i),
                        GlobalConfigure.connectPortList.get(i), GlobalConfigure.connectProtocolList.get(i));
                asyncTaskService.executeClientAsyncTask(GlobalConfigure.connectIPList.get(i),
                        GlobalConfigure.connectPortList.get(i), GlobalConfigure.connectLocalPortList.get(i),
                        GlobalConfigure.connectProtocolList.get(i));
            }
        }

        log.error(">>>step12-服务启动执行，串口协议加载");
        if ((GlobalConfigure.commProtocolList.size() != GlobalConfigure.commAddressList.size())) {
            log.error("configured comm address not equal,protocol ... address =[{}] protocol=[{}]",
                    GlobalConfigure.commAddressList, GlobalConfigure.commProtocolList);
            return;
        } else {
            /**添加从数据库中读取的数据项*/
            List<ClientType.IpPortProtocol> clientList = linkManager.getClientList();
            clientList.stream().filter(
                    client -> !client.getIp().matches(ConstValue.IP_PATTERN)
            ).forEach(
                    client ->
                    {
                        GlobalConfigure.commAddressList.add(client.getIp());
                        GlobalConfigure.commProtocolList.add(client.getProtocol());
                    }
            );

            for (int i = 0; i < GlobalConfigure.commAddressList.size(); i++) {
                log.error("串口地址{}加载协议{} ", GlobalConfigure.commAddressList,
                        GlobalConfigure.commProtocolList.get(i));

                asyncTaskService.executeCommtAsyncTaskRandomDelay(GlobalConfigure.commAddressList.get(i),
                        GlobalConfigure.commProtocolList.get(i));
            }
        }
    }
}
