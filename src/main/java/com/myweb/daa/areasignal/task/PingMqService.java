package com.myweb.daa.areasignal.task;

import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName: PingMqService
 * @Description:
 * @Author: king
 * @CreateDate: 2020/3/24 11:35
 */
@Component
@Slf4j
public class PingMqService {

    @Autowired
    private MessageSender messageSender;

    @Scheduled(initialDelay = 5000, fixedRate = 5000)
    private void pingMq() {
        log.info("send ping message to MQ");
        InterProtocol interProtocol = new InterProtocol(InterProtocolType.UNKOWN_MESSAGE, "", "", 0, "empty", 0, "", false, 3, "", false);
        messageSender.sendDirect(interProtocol);
    }
}
