package com.myweb.daa.areasignal.task;


import com.myweb.daa.areasignal.config.GlobalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 配置异步线程池
 * @date 2014年3月15日
 */
@Configuration
@EnableAutoConfiguration
@EnableScheduling
@EnableAsync
@Slf4j
public class AsyncTaskConfig implements AsyncConfigurer {
    public static int corePoolSize = 10;
    public static int queueCapacity = 2000;

    static {
        //默认为cpu核心个数，最大为核心个数两倍
        corePoolSize = Runtime.getRuntime().availableProcessors();
    }

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl("default"));
        taskExecutor.initialize();
        return taskExecutor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        log.error("exception caught");
        return null;
    }

    /**
     * 内部消息转换处理线程池
     *
     * @return
     */
    @Bean(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public Executor getMessageAsyncExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.MESSAGE_ASYC_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * 外部消息转换处理线程池
     *
     * @return
     */
    @Bean(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR)
    public Executor getOuterMessageAsyncExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * 报文处理线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public Executor getMessageProcessAsyncExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * 1049报文处理线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.MESSAGE_1049_PROCESS_EXECUTOR)
    public Executor getMessage1049ProcessAsyncExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.MESSAGE_1049_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * MQ系统消息处理线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public Executor getMqMessageExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * MQ系统消息处理线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    public Executor getDbMessageExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * MQ系统消息处理线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR)
    public Executor getOptimizeMessageExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.OPTIMIZE_PROCESS_EXECUTOR));
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.initialize();
        return taskExecutor;
    }


    /**
     * 状态查询现场数据
     *
     * @return
     */
    @Bean(GlobalConfigure.CROSS_STATE_PROCESS_EXECUTOR)
    public Executor getCrossSateMessageExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.CROSS_STATE_PROCESS_EXECUTOR));
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.initialize();
        return taskExecutor;
    }

    /**
     * SGP报文数据存储线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.SGP_PROCESS_EXECUTOR)
    public Executor getSgpExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.SGP_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }


    /**
     * SGP报文数据存储线程池
     * 所有的报文处理标注此线程
     *
     * @return
     */
    @Bean(GlobalConfigure.DATA_LOOK_PROCESS_EXECUTOR)
    public Executor getDataLookExecutor() {
        //使用Spring内置线程池任务对象
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(corePoolSize * 2);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl(GlobalConfigure.DATA_LOOK_PROCESS_EXECUTOR));
        taskExecutor.initialize();
        return taskExecutor;
    }
}
