package com.myweb.daa.areasignal.transport;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpConnectException;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionListener;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The type RequestMessage sender.
 *
 * <AUTHOR>
 * @version 1.0
 * @Description 报文发送类定义
 * @date 2014年3月15日
 */
@Component
@Slf4j
public class MessageSender {

    @Autowired
    private AmqpTemplate rabbitTemplate;

    @Autowired
    private CachingConnectionFactory connectionFactory;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    /**
     * 用来记录当前active的mq连接个数，如果为0表示当前连接均不可用
     * 用此的目的是不要阻塞send发送函数导致处理队列的数据堆积。
     * 同时需要一个线程一直尝试发送报文，否则标记会一直为0，导致数据发送不出去。
     */
    private AtomicInteger activeConnection = new AtomicInteger(0);

    @PostConstruct
    public void init() {
        connectionFactory.addConnectionListener(new RabbitConnectionListener());
    }

    /**
     * 不进行优化直接进行数据的发送，需要注意大量数据不能使用，否则会使得线程阻塞
     *
     * @param interProtocol
     */
    public void sendDirect(InterProtocol interProtocol) {
        String msg = JSONObject.toJSONString(interProtocol);
        try {
            this.rabbitTemplate.convertAndSend(GlobalConfigure.exchangeName, "hb", msg);
        } catch (AmqpConnectException e) {
            log.error("rabbitmq connection is not ready when sending " + interProtocol);
        } catch (AmqpException e) {
            log.error("rabbitmq exception ");
        }
    }

    /**
     * 发送之前判定是否有可用连接，没有则直接失败
     *
     * @param routeKey
     */
    public void send(String routeKey, Object object) {
        if (activeConnection.get() <= 0) {
            log.error("rabbitmq is not ready when sending " + object);
            return;
        }

        String msg = JSONObject.toJSONString(object);
        log.debug("send to mq ... {}", msg);

        try {
            this.rabbitTemplate.convertAndSend(GlobalConfigure.exchangeName, GlobalConfigure.routingKeyPrefix + routeKey, msg);
        } catch (AmqpConnectException e) {
            log.error("rabbitmq connection is not ready when sending " + object);
        } catch (AmqpException e) {
            log.error("rabbitmq exception ");
        }
    }


    class RabbitConnectionListener implements ConnectionListener {
        /**
         * RabbitAdmin auto declaration is not supported with CacheMode.CONNECTION
         * 因为使用了CONNECTION模式，导致不会自己声明exchange，
         * 所以需要自己手动declareExchange,使用rabbitmqadmin的init
         *
         * @param connection
         */
        @Override
        public void onCreate(Connection connection) {
            Integer integer = activeConnection.incrementAndGet();
            log.info("current active " + integer + ",connection is connected ..." + connection.toString());

            if (1 == integer) {
                log.info("rabbitmq admin init  ...");
                rabbitAdmin.initialize();
            }
        }

        @Override
        public void onClose(Connection connection) {
            Integer integer = activeConnection.decrementAndGet();
            log.info("current active " + integer + ",connection is disconnected ..." + connection.toString());
        }


    }

}
