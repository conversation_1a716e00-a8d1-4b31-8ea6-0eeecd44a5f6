package com.myweb.daa.areasignal.transport;


import com.myweb.daa.areasignal.config.GlobalConfigure;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 区域机数据配置
 */
@Configuration
public class SignalConfiguration {

    @Bean(name = "signalExchange")
    public TopicExchange signalExchange() {
        return new TopicExchange(GlobalConfigure.exchangeName, false, false);
    }


    @Bean(name = "scatsCmdQueue")
    public Queue scatsCmdQueue() {
        return new Queue(GlobalConfigure.scatsCmdQueue, true, false, true);
    }


    /**
     * 绑定当前所在区域的控制命令数据项
     *
     * @param scatsCmdQueue
     * @param signalExchange
     * @return
     */
    @Bean
    Binding bindingExchangeMessages(Queue scatsCmdQueue, TopicExchange signalExchange) {
        return BindingBuilder.bind(scatsCmdQueue).to(signalExchange)
                .with(GlobalConfigure.routingKeyPrefixListen + "*");
    }

    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }


    ////针对全息数据项
    @Bean(name = "centralCmdExchange")
    public DirectExchange centralCmdExchange() {
        return new DirectExchange(GlobalConfigure.centralExchange, false, false);
    }

    @Bean(name = "centralCmdQueue")
    public Queue centralCmdQueue() {
        return new Queue(GlobalConfigure.centralCmdQueue, true, false, true);
    }

    /**
     * 绑定当前所在区域的控制命令数据项
     *
     * @param centralCmdQueue
     * @param centralCmdExchange
     * @return
     */
    @Bean
    Declarables bindingCentralExchangeMessages(Queue centralCmdQueue, DirectExchange centralCmdExchange) {
        Declarables declarables = new Declarables();
        for (int i = 0; i < GlobalConfigure.areaNos.size(); i++) {
            int areaNo = GlobalConfigure.areaNos.get(i);
            declarables.getDeclarables().add(BindingBuilder.bind(centralCmdQueue).to(centralCmdExchange)
                    .with(GlobalConfigure.centralExchangeRouting + areaNo));
        }
        return declarables;
    }

}
