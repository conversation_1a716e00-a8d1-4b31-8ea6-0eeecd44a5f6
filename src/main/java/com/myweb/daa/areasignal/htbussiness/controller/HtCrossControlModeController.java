package com.myweb.daa.areasignal.htbussiness.controller;


import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossControlModePublish;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/ht/notifyAllMode")
@Api(description = "华通信号机数据加载")
public class HtCrossControlModeController {

    @Autowired
    private CrossControlModePublish crossControlModePublish;

    @ApiOperation(value = "解锁流向")
    public JsonResult notifyAllMode() {
        crossControlModePublish.notifyAllMode();
        return new JsonResult<>(true, "发布所有路口控制方式成功");
    }

}
