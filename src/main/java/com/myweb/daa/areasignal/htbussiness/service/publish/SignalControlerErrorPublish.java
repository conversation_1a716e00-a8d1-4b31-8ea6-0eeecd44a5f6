package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Log;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SignalControllerError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Component
@Slf4j
public class SignalControlerErrorPublish {

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private RestTemplate restTemplate;

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processSignalControlerError(SignalControllerError signalControlerError) {
        String signalId = signalControlerError.getSignalControllerId();

        if (p1049Configure.isSavedb()) {
            repositoryDao.save(P1049Log.builder().type(SignalControllerError.class.getSimpleName())
                    .recvDate(new Date()).signalId(signalId)
                    .data(JSONObject.toJSONString(signalControlerError)).build(), P1049Log.class);
        }
    }


}
