package com.myweb.daa.areasignal.htbussiness.bean;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Optional;

@Slf4j
public enum SignalBrandPort {
    LS(8888, "莱斯信号机", 0 ),
    HT(2112, "华通信号机", 1),
    LP(2113, "洛普信号机", 2),
    QF(2114, "千方信号机", 3),
    /**
     * 海信信号系统查询路口参数，发送ID为任何数据时，返回所有的数据
     */
    HX(2115, "海信信号机", 4),

    /**
     * 中策信号系统查询路口参数，发送ID为"0"，返回所有的数据
     */
    ZC(2116, "中策信号机", 5),

    YL(2117, "攸亮信号机", 6),

    DW(2118, "航天大为信号机", 7),

    DH(2119, "大华信号机", 8),

    ZK(2120, "中控信号机(私有)", 9),

    HK(2121, "海康信号机", 10),
    TH(2122, "泰和信号机", 11),

    HD(2123, "华录德信号机", 12),

    FJ(2124, "富骏信号机", 13),
    SC(2125, "中控信号机(莱斯1049)", 14),

    ALL(65535, "标记通用厂家", 65535),
    ;

    private int port;
    private String des;
    private int brandCode;


    SignalBrandPort(int port, String des, int brandCode) {
        this.port = port;
        this.des = des;
        this.brandCode = brandCode;
    }

    /**
     * 获取品牌id
     *
     * @return
     */
    public int brandCode() {
        return brandCode;
    }

    /**
     * 根据端口获取系统类型
     *
     * @param port
     * @return
     */
    public static Optional<SignalBrandPort> getType(int port, int remotePort) {
        return  Arrays.stream(SignalBrandPort.values()).filter(signalBrandPort -> signalBrandPort.port == port).findAny();
    }

    /**
     * 判定是否是1049协议
     *
     * @param signalId
     * @return
     */
    public static boolean is1049System(String signalId) {
        return true;
    }

    /**
     * 根据信号机ID获取系统类型
     *
     * @param signalId
     * @return
     */
    public static SignalBrandPort getType(String signalId) {
        String brandStr = signalId.substring(6, 8);
        Optional<SignalBrandPort> brandPort = Arrays.stream(SignalBrandPort.values()).filter(signalBrandPort -> signalBrandPort.name().equalsIgnoreCase(brandStr)).findAny();
        if (!brandPort.isPresent()) {
            log.error("没有找到信号机对应的类型-{}", signalId);
            return HT;
        }
        return brandPort.get();
    }

    /**
     * 验证是否是扩展协议
     * @param signalBrandPort
     * @return
     */
    public static boolean isBrandLesExtProtocol(SignalBrandPort signalBrandPort){
           if(SignalBrandPort.FJ == signalBrandPort){
               return true;
           } else if (SignalBrandPort.SC == signalBrandPort) {
               return true;
           }
           return false;
    }
}
