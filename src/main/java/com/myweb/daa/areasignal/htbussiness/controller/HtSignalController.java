package com.myweb.daa.areasignal.htbussiness.controller;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.handler.DataInternalNotify;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.DayPlanParam;
import com.myweb.daa.areasignal.centralsystem.param.LockFlowDirection;
import com.myweb.daa.areasignal.centralsystem.param.PhaseParam;
import com.myweb.daa.areasignal.centralsystem.param.PlanParam;
import com.myweb.daa.areasignal.centralsystem.param.ScheduleParam;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import com.myweb.daa.areasignal.centralsystem.param.UnLockFlowDirection;
import com.myweb.daa.areasignal.centralsystem.param.*;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.groovy.ParseInfo;
import com.myweb.daa.areasignal.groovy.RuleInterface;
import com.myweb.daa.areasignal.groovy.SelfDefinedRuleManager;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.process.P1049InfosProcess;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.htbussiness.service.publish.CrossCtrlInfoPublish;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @ClassName: HtSignalController
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 14:01
 */
@Slf4j
@RestController
@RequestMapping(value = "/ht")
@Api(description = "华通信号机数据加载")
public class HtSignalController {
    @Autowired
    private HtSignalService htSignalService;
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private SelfDefinedRuleManager selfDefinedRuleManager;

    @Autowired
    private P1049InfosProcess p1049InfosProcess;

    @Autowired
    private CrossCtrlInfoPublish crossCtrlInfoPublish;

    @Autowired
    private DataInternalNotify dataInternalNotify;


    @GetMapping("/simu/{crossId}/{type}")
    @Transactional
    @ApiOperation(value = "信号机参数")
    public JsonResult simuData(@PathVariable String crossId,
                               @PathVariable String type, @RequestBody String content) {

        MessageType messageType = MessageType.parse(type);

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "没有找到路口" + crossId, "");
        }
        Optional<ControllerService.SignalBaseInfo> signalInfoOp =
                controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalInfoOp.isPresent()) {
            return new JsonResult(false, "没有找到路口关联信号机" + crossId, "");
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());

        JsonResult jsonResult = htSignalService.simuData(crossId, signalBrandPort, crossingInfoOp.get().getAddress1049Ip(),
                messageType, content);
        jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
        return jsonResult;
    }


    @GetMapping("/loadSignalData/{crossingId}")
    @Transactional
    @ApiOperation(value = "路口参数")
    public JsonResult loadData(@PathVariable String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "异常的路口编号", "");
        }
        //删除原先阶段参数数据项,需要重新阶段数据项
        try {
            p1049InfosProcess.deleteP1049Infos(crossingInfoOp.get().getCrossingId(),
                    crossingInfoOp.get().getSubJuncNo(),
                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName());
        } catch (Exception e) {
            log.error("数据操作异常5", e);
        }

        //删除手动配置参数数据项
        try{
            signalCacheService.deleteDataEntity(crossingInfoOp.get().getControllerId(), StageManualConfig.class.getSimpleName());
        }catch (Exception e) {
            log.error("删除手动配置参数异常", e);
        }

        return htSignalService.loadData(crossingInfoOp.get());
    }

    /**
     * 静默调看参数
     * @param crossingId
     * @return
     */
    public JsonResult loadDataSilent(@PathVariable String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "异常的路口编号", "");
        }
        //删除原先阶段参数数据项,需要重新阶段数据项
        try
        {
            p1049InfosProcess.deleteP1049Infos(crossingInfoOp.get().getCrossingId(),
                    crossingInfoOp.get().getSubJuncNo(),
                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageParam.class.getSimpleName());
        } catch (Exception e) {
            log.error("数据操作异常", e);
        }

        return htSignalService.loadData(crossingInfoOp.get());
    }

    @GetMapping("/loadSystemData/{crossingId}/{type}/{id}/{no}")
    @Transactional
    @ApiOperation(value = "系统参数")
    public JsonResult loadSystemData(
            @PathVariable String crossingId,
            @PathVariable String type,
            @PathVariable String id,
            @PathVariable String no) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "异常的路口编号", "");
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());

        JsonResult jsonResult = htSignalService.loadSystemData(signalBrandPort, type, id, no, crossingInfoOp.get().getAddress1049Ip());
        jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
        return jsonResult;
    }

    @GetMapping("/loadSignalData/{lessignalControlerID}/{type}/{no}")
    @Transactional
    @ApiOperation(value = "信号机参数")
    public JsonResult loadSignalData(@PathVariable String lessignalControlerID,
                                     @PathVariable String type,
                                     @PathVariable String no) {

        if (no.equalsIgnoreCase("ffff")) {
            no = "";
        }

        JsonResult jsonResult = htSignalService.loadSignalData(lessignalControlerID, 0, type, no);
        jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
        return jsonResult;
    }


    @GetMapping("/loadSignalDataSp/{lessignalControlerID}/{type}/{no}")
    @Transactional
    @ApiOperation(value = "信号机参数")
    public JsonResult loadSignalDataSp(@PathVariable String lessignalControlerID,
                                       @PathVariable String type,
                                       @PathVariable String no) {
        if (!type.equalsIgnoreCase(SignalController.class.getSimpleName())) {
            return new JsonResult(false, "暂时只支持信号参数", "");
        }


        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(lessignalControlerID);
        if (!signalInfoOp.isPresent()) {
            return new JsonResult(false, "未知的信号机id" + lessignalControlerID, "");
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(lessignalControlerID);

        JsonResult dataWithIdResult = htSignalService.loadDataWithId(signalBrandPort, no, type,
                "0", 0, signalInfoOp.get().getAddress1049Ip());
        dataWithIdResult.setErrorCode(dataWithIdResult.isSuccess() ? "0" : "10001");
        return dataWithIdResult;
    }

    @GetMapping("/loadCrossData/{lesCrossingId}/{type}/{no}")
    @Transactional
    @ApiOperation(value = "路口参数")
    public JsonResult loadCrossData(@PathVariable String lesCrossingId,
                                    @PathVariable String type,
                                    @PathVariable String no) {

        JsonResult jsonResult = htSignalService.loadCrossData(lesCrossingId, type, no, 1);
        jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");

        //检查是否是CrossPlan参数，强制发送当前内存中数据项
        {
            if (type.equalsIgnoreCase(CrossPlan.class.getSimpleName())) {
                crossCtrlInfoPublish.publishCrossPlan(lesCrossingId);
            } else if (type.equalsIgnoreCase(StageParam.class.getSimpleName())) {

                Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(lesCrossingId);
                if (crossingInfoOp.isPresent()) {
                    //删除手动配置参数数据项,重庆手动配置要求
                    try {
                        signalCacheService.deleteDataEntity(crossingInfoOp.get().getControllerId(), StageParam.class.getSimpleName());
                    } catch (Exception e) {
                        log.error("主动删除阶段参数异常", e);
                    }
                }
            }
        }

        return jsonResult;
    }

    @GetMapping("/loadCrossDataSp/{lesCrossingId}/{type}/{no}")
    @Transactional
    @ApiOperation(value = "路口参数")
    public JsonResult loadCrossDataSp(@PathVariable String lesCrossingId,
                                      @PathVariable String type,
                                      @PathVariable String no) {

        if (!type.equalsIgnoreCase(CrossParam.class.getSimpleName())) {
            return new JsonResult(false, "暂时只支持路口参数", "");
        }


        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(lesCrossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "异常的路口编号", "");
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());

        JsonResult dataWithIdResult = htSignalService.loadDataWithId(signalBrandPort, no, type,
                "0", 1, crossingInfoOp.get().getAddress1049Ip());
        dataWithIdResult.setErrorCode(dataWithIdResult.isSuccess() ? "0" : "10001");
        return dataWithIdResult;

    }

    /**
     * 加载数据项
     *
     * @return
     */
    @GetMapping("/loadData/{type}/{address}")
    @Transactional
    @ApiOperation(value = "调看系统参数以及路口参数")
    public JsonResult loadData(@PathVariable SignalBrandPort type, @PathVariable String address) {
        return htSignalService.loadDataWhenLoginSuccess(type, address);
    }

    /**
     * 加载数据项
     *
     * @return
     */
    @GetMapping("/loadDataNoIp")
    @Transactional
    @ApiOperation(value = "调看系统参数以及路口参数")
    public JsonResult loadData(@RequestParam SignalBrandPort type) {
        List<String> ips = new ArrayList<>();
        crossingService.getCrossingBaseInfoMap().values().stream().filter(
                crossingBaseInfo ->
                {
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingBaseInfo.getControllerId());
                    if (signalBrandPort.brandCode() != type.brandCode()) {
                        return false;
                    }

                    return (!crossingBaseInfo.getAddress1049Ip().isEmpty())
                            && (crossingBaseInfo.getAddress1049Ip().compareToIgnoreCase("***************") != 0);
                }
        ).forEach(
                crossingBaseInfo -> {
                    if (!ips.contains(crossingBaseInfo.getAddress1049Ip())) {
                        ips.add(crossingBaseInfo.getAddress1049Ip());
                    }
                });


        if (ips.isEmpty()) {
            return new JsonResult(false, "10001", "没有找到可用的系统ip", "");
        }

        AtomicBoolean success = new AtomicBoolean(true);
        List<JsonResult> results = new ArrayList<>();
        ips.stream().forEach(
                ip -> {
                    JsonResult jsonResult = htSignalService.loadDataWhenLoginSuccess(type, ip);
                    jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
                    if (!jsonResult.isSuccess()) {
                        success.set(false);
                    }
                    results.add(jsonResult);
                }
        );
        JsonResult jsonResult;
        if (!success.get()) {
            jsonResult = new JsonResult(false, "10001", "部分数据请求异常", results);
        } else {
            jsonResult = new JsonResult(true, "0", "请求正常", results);
        }

        return jsonResult;
    }


    /**
     * 加载数据项
     *
     * @return
     */
    @GetMapping("/loadDataNoIpBasic")
    @Transactional
    @ApiOperation(value = "调看系统参数以及路口参数")
    public JsonResult loadDataBasic(@RequestParam SignalBrandPort type) {

        if (null == type) {
            return new JsonResult(false, "10001", "请输入信号机厂家", "");
        }

        List<String> ips = new ArrayList<>();
        crossingService.getCrossingBaseInfoMap().values().stream().filter(
                crossingBaseInfo ->
                {
                    SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingBaseInfo.getControllerId());
                    if (signalBrandPort.brandCode() != type.brandCode()) {
                        return false;
                    }

                    return (!crossingBaseInfo.getAddress1049Ip().isEmpty())
                            && (crossingBaseInfo.getAddress1049Ip().compareToIgnoreCase("***************") != 0);
                }
        ).forEach(
                crossingBaseInfo -> {
                    if (!ips.contains(crossingBaseInfo.getAddress1049Ip())) {
                        ips.add(crossingBaseInfo.getAddress1049Ip());
                    }
                });


        if (ips.isEmpty()) {
            return new JsonResult(false, "10001", "没有找到可用的系统ip", "");
        }

        AtomicBoolean success = new AtomicBoolean(true);
        List<JsonResult> results = new ArrayList<>();
        ips.stream().forEach(
                ip -> {
                    JsonResult jsonResult = htSignalService.loadData(type, ip);
                    jsonResult.setErrorCode(jsonResult.isSuccess() ? "0" : "10001");
                    if (!jsonResult.isSuccess()) {
                        success.set(false);
                    }
                    results.add(jsonResult);
                }
        );
        JsonResult jsonResult;
        if (!success.get()) {
            jsonResult = new JsonResult(false, "10001", "部分数据请求异常", results);
        } else {
            jsonResult = new JsonResult(true, "0", "请求正常", results);
        }

        return jsonResult;
    }

    @PostMapping("/modeStage")
    @ApiOperation(value = "指定阶段")
    public JsonResult lockFlow(@RequestParam String crossId, @RequestParam int subJuncNo, @RequestParam int stageNo, @RequestParam int stageLen, @RequestParam boolean lock) {
        return htSignalService.lockStage(crossId, subJuncNo, String.valueOf(stageNo), stageLen, lock);
    }


    @PostMapping("/lockFlow")
    @ApiOperation(value = "锁定流向")
    public JsonResult modeStage(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int direction, @RequestParam int outDirection
            , @RequestParam int len, @RequestParam boolean lock) {
        return htSignalService.lockFlowDirection(signalId, subJuncNo, direction, outDirection, len, lock);
    }

    @PostMapping("/setMode")
    @ApiOperation(value = "指定模式")
    public JsonResult modeStage(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int mode) {
        return htSignalService.setSignalMode(signalId, subJuncNo, mode);
    }


    @PostMapping("/optimizeCentral")
    @ApiOperation(value = "优化-中心机")
    public JsonResult optimizeCentral(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestBody CentralPlanParam centralPlanParam) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        List<Object> objectList = new ArrayList<>();
        objectList.add(centralPlanParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), CentralPlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    @PostMapping("/setPlan")
    @ApiOperation(value = "指定方案")
    public JsonResult setPlan(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int planNo) {
        return htSignalService.setPlanNo(signalId, subJuncNo, planNo);
    }

    @PostMapping("/report/{type}/{start}")
    @ApiOperation(value = "数据上传设置")
    public JsonResult reportSetting(@RequestBody List<String> crossingIds, @PathVariable String type
            , @PathVariable boolean start) {
        return htSignalService.reportSetting(crossingIds, type, start);
    }

    /**
     * 获取方案数据项
     *
     * @return
     */
    @GetMapping("/planparam/{crossingId}/{planNo}")
    public JsonResult<?> getPlanParam(@PathVariable String crossingId, @PathVariable int planNo) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(planNo);
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), PlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    /**
     * 获取方案数据项
     *
     * @return
     */
    @GetMapping("/planparamall/{crossingId}")
    public JsonResult<?> getPlanParam(@PathVariable String crossingId) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), PlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取方案数据项
     *
     * @return
     */
    @PostMapping("/planparam/{crossingId}/{planNo}")
    public JsonResult<?> postPlanParam(@PathVariable String crossingId, @PathVariable int planNo, @RequestBody PlanParam planParam) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(planParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), PlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取方案数据项
     *
     * @return
     */
    @PostMapping("/planparamOrg/{crossingId}/{planNo}")
    public JsonResult<?> postPlanParamOrg(@PathVariable String crossingId, @PathVariable int planNo, @RequestBody PlanParam planParam) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        planParam.setOrg1049StageNo(true);
        List<Object> objectList = new ArrayList<>();
        objectList.add(planParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), PlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    /**
     * 获取阶段数据项
     *
     * @return
     */
    @GetMapping("/stageparam/{crossingId}/{stageNo}")
    public JsonResult<?> getStageParam(@PathVariable String crossingId, @PathVariable int stageNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(stageNo);
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), StageParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取阶段数据项
     *
     * @return
     */
    @GetMapping("/phaseparam/{crossingId}/{stageNo}")
    public JsonResult<?> getPhaseParam(@PathVariable String crossingId, @PathVariable int stageNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        if(0 != stageNo) {
            objectList.add(stageNo);
        }
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), PhaseParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 设置阶段数据项
     *
     * @return
     */
    @PostMapping("/phaseparam/{crossingId}/{phaseNo}")
    public JsonResult<?> postStageParam(@PathVariable String crossingId, @PathVariable int phaseNo, @RequestBody PhaseParam phaseParam) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(phaseParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), PhaseParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取阶段数据项
     *
     * @return
     */
    @GetMapping("/stageparamall/{crossingId}")
    public JsonResult<?> getStageParamAll(@PathVariable String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), StageParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    /**
     * 设置阶段数据项
     *
     * @return
     */
    @PostMapping("/stageparam/{crossingId}/{stageNo}")
    public JsonResult<?> postStageParam(@PathVariable String crossingId, @PathVariable int stageNo, @RequestBody StageParam stageParam) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(stageParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), StageParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取日计划数据项
     *
     * @return
     */
    @GetMapping("/dayplanparam/{crossingId}/{dayPlanNo}")
    public JsonResult<?> getDayPlanParam(@PathVariable String crossingId, @PathVariable int dayPlanNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(dayPlanNo);
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), DayPlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取所有日计划数据项
     *
     * @return
     */
    @GetMapping("/dayplanparamall/{crossingId}")
    public JsonResult<?> getDayPlanParam(@PathVariable String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), DayPlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    /**
     * 获取阶段数据项
     *
     * @return
     */
    @PostMapping("/dayplanparam/{crossingId}/{dayPlanNo}")
    public JsonResult<?> postDayPlanParam(@PathVariable String crossingId, @PathVariable int dayPlanNo, @RequestBody DayPlanParam dayPlanParam) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(dayPlanParam);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), DayPlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    /**
     * 获取调度计划数据项
     *
     * @return
     */
    @GetMapping("/scheduleparam/{crossingId}/{scheduleNo}")
    public JsonResult<?> getScheduleParam(@PathVariable String crossingId, @PathVariable int scheduleNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        objectList.add(scheduleNo);
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), ScheduleParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    /**
     * 获取所有调度计划数据项
     *
     * @return
     */
    @GetMapping("/scheduleparamall/{crossingId}")
    public JsonResult<?> getScheduleParam(@PathVariable String crossingId) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + crossingId, "");
        }
        List<Object> objectList = new ArrayList<>();
        MqMessage mqMessage = P1049HelpUtils.buildSimuQueryMqMsg(crossingInfoOp.get().getControllerId(), ScheduleParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    @PostMapping("/setMode/central")
    @ApiOperation(value = "指定模式")
    public JsonResult modeStageCental(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int mode) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + subJuncNo, "");
        }

        ControlModeCmd controlModeCmd = ControlModeCmd.builder()
                .signalControllerID(signalId)
                .crossingSeqNo(subJuncNo)
                .mode(mode).build();

        List<Object> objectList = new ArrayList<>();
        objectList.add(controlModeCmd);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), ControlModeCmd.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    @PostMapping("/setPlan/central")
    @ApiOperation(value = "指定方案")
    public JsonResult setPlanCentral(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int planNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + subJuncNo, "");
        }

        PlanCmd planCmd = PlanCmd.builder()
                .signalControllerID(signalId)
                .crossingSeqNo(subJuncNo)
                .planNo(planNo).build();

        List<Object> objectList = new ArrayList<>();
        objectList.add(planCmd);
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(), PlanCmd.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    @PostMapping("/lockFlow/central")
    @ApiOperation(value = "锁定流向")
    public JsonResult modeStageCentral(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestParam int direction, @RequestParam int outDirection
            , @RequestParam int len, @RequestParam boolean lock) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + subJuncNo, "");
        }

        List<Object> objectList = new ArrayList<>();
        if (lock) {
            LockFlowDirection lockFlowDirection = LockFlowDirection.builder()
                    .signalControllerID(signalId)
                    .crossingSeqNo(subJuncNo)
                    .noStage(direction)
                    .lenStage(len)
                    .build();
            objectList.add(lockFlowDirection);
        } else {
            UnLockFlowDirection lockFlowDirection = UnLockFlowDirection.builder()
                    .signalControllerID(signalId)
                    .crossingSeqNo(subJuncNo)
                    .noStage(direction)
                    .build();
            objectList.add(lockFlowDirection);
        }
        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                lock ? LockFlowDirection.MqObjectId : UnLockFlowDirection.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    @PostMapping("/manual/central")
    @ApiOperation(value = "系统手动")
    public JsonResult manualCentral(@RequestParam String signalId, @RequestParam int code) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, 1);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + 1, "");
        }

        List<Object> objectList = new ArrayList<>();
        ManualControl manualControl = ManualControl.builder()
                .signalControllerID(signalId)
                .crossingSeqNo(1)
                .iden(code).build();
        objectList.add(manualControl);

        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                ManualControl.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }


    @PostMapping("/alg/central/{signalId}")
    @ApiOperation(value = "算法")
    public JsonResult alg(@PathVariable String signalId, @RequestBody CentralPlanParam centralPlanParam) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, 1);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + 1, "");
        }

        List<Object> objectList = new ArrayList<>();
        objectList.add(centralPlanParam);

        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                CentralPlanParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求");
    }

    @GetMapping("/time")
    @ApiOperation(value = "时间同步")
    public JsonResult timeSync(@RequestParam String signalId) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, 1);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + 1, "");
        }

        TimeParam timeParam = TimeParam.builder()
                .signalControllerID(crossingInfoOp.get().getControllerId())
                .crossingSeqNo(1)
                .noArea(crossingInfoOp.get().getNoArea())
                .noJunc(crossingInfoOp.get().getNoJunc())
                .globalTime(System.currentTimeMillis() / 1000).build();

        List<Object> objectList = new ArrayList<>();
        objectList.add(timeParam);

        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                TimeParam.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求时间同步");
    }


    @GetMapping("/rule/{ruleId}/{cross1049}")
    @ApiOperation(value = "规则引擎测试")
    public JsonResult<?> rule(@PathVariable String ruleId, @PathVariable String cross1049) {
        Optional<ParseInfo> ruleOp = selfDefinedRuleManager.getRule(ruleId);
        if (!ruleOp.isPresent()) {
            return new JsonResult<>(false, "没有找到规则");
        }
        try {
            Object ruleObject = ReflectUtils.newInstance(ruleOp.get().getClazz());
            if (ruleObject instanceof RuleInterface) {
                RuleInterface ruleInterface = (RuleInterface) ruleObject;
                Optional<String> optionalS = ruleInterface.change1049CrossToLesSignalId(cross1049);
                if (optionalS.isPresent()) {
                    return new JsonResult<>(true, "生成莱斯信号机编号是" + optionalS.get());
                } else {
                    return new JsonResult<>(true, "使用规则引擎生成莱斯信号机编号失败");
                }
            } else {
                return new JsonResult<>(false, "异常的规则解析器，需要继承RuleInterface");
            }
        } catch (Exception e) {
            return new JsonResult<>(false, "生成规则解析器异常" + e.getMessage());
        }

    }


    @PostMapping("/setStageCtrlList")
    @ApiOperation(value = "周期优化")
    public JsonResult setStageCtrlList(@RequestParam String signalId, @RequestParam int subJuncNo,
                                       @RequestBody StageCtrlList stageCtrlList) {
        return htSignalService.setStageCtrlList(signalId, subJuncNo, stageCtrlList);
    }


    @PostMapping("/setStageCtrl")
    @ApiOperation(value = "阶段优化")
    public JsonResult setStageCtrl(@RequestParam String signalId, @RequestParam int subJuncNo, @RequestBody StageCtrl stageCtrl) {
        return htSignalService.setStageCtrl(signalId, subJuncNo, stageCtrl);
    }

    @GetMapping("/lockDirection/{signalId}/{length}")
    @ApiOperation(value = "锁定流向")
    public JsonResult lockDirection(@PathVariable String signalId, @PathVariable int length, @RequestBody List<Integer> flows) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, 1);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + 1, "");
        }

        LockFlow lockFlow = LockFlow.builder()
                .signalControllerID(crossingInfoOp.get().getControllerId())
                .crossingSeqNo(1)
                .flowNos(flows)
                .length(length)
                .build();

        List<Object> objectList = new ArrayList<>();
        objectList.add(lockFlow);

        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                LockFlow.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求锁定流向");
    }

    @GetMapping("/unLockDirection/{signalId}")
    @ApiOperation(value = "解锁流向")
    public JsonResult unLockDirection(@PathVariable String signalId) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, 1);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口对应的信号机" + signalId + ",子路口" + 1, "");
        }

        UnLockFlow lockFlow = UnLockFlow.builder()
                .signalControllerID(crossingInfoOp.get().getControllerId())
                .crossingSeqNo(1)
                .build();

        List<Object> objectList = new ArrayList<>();
        objectList.add(lockFlow);

        MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                UnLockFlow.MqObjectId, objectList);
        messagePublisher.publishMessage(mqMessage);
        return new JsonResult<>(true, "开始请求解除锁定流向");
    }


}
