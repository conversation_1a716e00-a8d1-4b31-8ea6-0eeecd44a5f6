package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.StageTrafficData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 16:52
 */
@Component
@Slf4j
public class StageTrafficDataPublish {

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processStageTrafficData(StageTrafficData stageTrafficData) {
        log.debug("收到相位流量数据项-{}", stageTrafficData);
    }

}
