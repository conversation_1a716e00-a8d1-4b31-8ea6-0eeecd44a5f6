package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCycle;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class CrossCyclePublish {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private CrossCtrlInfoPublish crossCtrlInfoPublish;

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void process(CrossCycle crossCycle) {
        //接收周期优化数据项
        String crossID = crossCycle.getCrossID();
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());

        if (crossCycle.getStartTime() != null) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime dateTime = LocalDateTime.parse(crossCycle.getStartTime(), formatter);
                LocalDateTime now = LocalDateTime.now();

                Duration duration = Duration.between(dateTime, now);
                log.debug("路口{}与本地时间间隔{}", crossingInfoOp.get().getCrossingId(), duration);
            } catch (Exception e) {
                log.debug("时间转换异常-{}", crossCycle);
            }
        }

        log.debug("crossCycle-{} -{}", crossCycle.getCrossID(), crossCycle);

        {
            LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
            // LocalDateTime to epoch seconds
            long miliseconds = now.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
            com.myweb.daa.areasignal.centralsystem.param.CrossCycle crossCycleCentral =
                    com.myweb.daa.areasignal.centralsystem.param.CrossCycle.builder()
                            .noArea(crossingInfoOp.get().getNoArea())
                            .noJunc(crossingInfoOp.get().getNoJunc())
                            .signalControllerID(crossingInfoOp.get().getControllerId())
                            .lenCycle(Integer.parseInt(crossCycle.getLastCycleLen()))
                            .lastCycleLen(Integer.parseInt(crossCycle.getLastCycleLen()))
                            .startTime(miliseconds)
                            .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                            .build();

            List<Object> objectList = new ArrayList<>();
            objectList.add(crossCycleCentral);
            MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfoOp.get().getControllerId(),
                    com.myweb.daa.areasignal.centralsystem.param.CrossCycle.MqObjectId, objectList);
            messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossCycle.class.getSimpleName().toLowerCase(), mqMessage);
            messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossCycleCentral)
                    .build());

            //周期切换时发送方案切换数据项
            {
                crossCtrlInfoPublish.publishCrossPlan(crossID);
            }
        }
    }
}
