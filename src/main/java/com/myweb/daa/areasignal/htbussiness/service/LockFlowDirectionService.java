package com.myweb.daa.areasignal.htbussiness.service;

import com.les.ads.ds.enums.LaneMovementType;
import com.les.ads.ds.gis.Movement;
import com.myweb.daa.areasignal.centralsystem.param.PhaseParam;
import com.myweb.daa.areasignal.centralsystem.param.StageParam;
import com.myweb.daa.areasignal.centralsystem.param.v2.DirectionType39900;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 接收延迟的取消相位锁定的原先锁定的接口进行相位的解锁定
 */
@Slf4j
@Data
@Service
public class LockFlowDirectionService {


    @Autowired
    private CrossingService crossingService;

    @Autowired
    private SignalCacheService signalCacheService;


    /**
     * 根据路口数据获取 阶段关联的流向
     * @param crossingBaseInfo
     * @param stageNoLes
     * @return
     */
    public List<Movement> getMovementsByStage(CrossingService.CrossingBaseInfo crossingBaseInfo, int stageNoLes){

        List<Movement> toUseMovement = new ArrayList<>();
        Map<Integer, Movement> stageMovements = new HashMap<>();

        Optional<StageParam> stageParamOp = signalCacheService.getData(crossingBaseInfo.getControllerId(), stageNoLes, StageParam.class);
        if(!stageParamOp.isPresent())
        {
            log.error("从缓存读取到路口{}阶段数据失败-{}", crossingBaseInfo.getCrossingId(),
                    stageNoLes);
            return toUseMovement;
        }

        if(stageParamOp.get().getPhaseNoList() == null || stageParamOp.get().getPhaseNoList().isEmpty()){
            log.error("从缓存读取到路口{}阶段{}数据没有相位信息-{}", crossingBaseInfo.getCrossingId(),
                    stageNoLes, stageParamOp.get());
            return toUseMovement;
        }

        List<Integer> phaseNoList = stageParamOp.get().getPhaseNoList();

        Optional<Map<String, PhaseParam>> phaseParamMapOp
                = signalCacheService.getDataMap(crossingBaseInfo.getControllerId(), PhaseParam.class);
        if(!phaseParamMapOp.isPresent())
        {
            log.error("从缓存读取到路口{}相位数据失败", crossingBaseInfo.getCrossingId());
            return toUseMovement;
        }

        Map<String, PhaseParam> phaseParamMap = phaseParamMapOp.get();

        Set<Integer> laneNoSet = new HashSet<>();
        phaseNoList.forEach(phaseNo -> {
            PhaseParam phaseParam = phaseParamMap.get(String.valueOf(phaseNo));
            if(phaseParam == null){
                log.error("从缓存读取到路口{}相位{}数据失败", crossingBaseInfo.getCrossingId(), phaseNo);
                return;
            }

            if(phaseParam.getLaneNoList() == null || phaseParam.getLaneNoList().isEmpty()){
                log.error("从缓存读取到路口{}相位{}没有车道信息", crossingBaseInfo.getCrossingId(), phaseNo);
                return;
            }

            laneNoSet.addAll(phaseParam.getLaneNoList());
        });

        log.error("锁定阶段时,路口{}阶段{}关联的车道是{}", crossingBaseInfo.getCrossingId(), stageNoLes, laneNoSet);

        if(laneNoSet.isEmpty()){
            log.error("锁定阶段时,路口{}阶段{}没有关联车道", crossingBaseInfo.getCrossingId(), stageNoLes);
            return toUseMovement;
        }

        //生成莱斯默认相位信息返回
        Optional<List<Movement>> crossingMovementOp = crossingService.getCrossingMovementData(crossingBaseInfo.getCrossingId());
        if (crossingMovementOp.isPresent()) {
            log.error("从sgp读取到路口{}流向数据-{}", crossingBaseInfo.getCrossingId(),
                    crossingMovementOp.get());
        } else {
            log.error("从sgp读取到路口流向数据失败");
            return toUseMovement;
        }

        crossingMovementOp.get().forEach(
                movement -> {
                    List<Integer> laneNos = movement.getLaneNos();
                    if(laneNos != null){
                        laneNos.forEach(laneNo -> {
                            if(laneNoSet.contains(laneNo)){
                                stageMovements.put(movement.getNo(), movement);
                            }
                        });
                    }
                }
        );


        log.error("使用路口{}阶段{}流向数据项{}", crossingBaseInfo.getCrossingId(), stageNoLes, stageMovements);

        if(stageMovements.isEmpty()){
            log.error("使用路口{}阶段{}没有流向数据项", crossingBaseInfo.getCrossingId(), stageNoLes);
            return toUseMovement;
        }

        Collection<Movement> movements = stageMovements.values();

        //优先直行流向
        List<Movement> straightMovements = movements.stream().filter(movement -> !movement.isPedestrian()).
                filter(movement -> movement.getMovementType() == LaneMovementType.RF_STRAIGHT).collect(Collectors.toList());

        //再次左转流向
        List<Movement> leftMovements = movements.stream()
                .filter(movement -> !movement.isPedestrian()).
                filter(movement -> movement.getMovementType() == LaneMovementType.RF_LEFT).collect(Collectors.toList());

        //最后右转
        List<Movement> rightMovements = movements.stream()
                .filter(movement -> !movement.isPedestrian()).
                filter(movement -> movement.getMovementType() == LaneMovementType.RF_RIGHT).collect(Collectors.toList());

        if(!straightMovements.isEmpty()){
            toUseMovement.addAll(straightMovements);
        }else if(!leftMovements.isEmpty()){
            toUseMovement.addAll(leftMovements);
        } else {
            toUseMovement.addAll(rightMovements);
        }

        log.error("锁定路口{}阶段{}使用流向数据项{}", crossingBaseInfo.getCrossingId(), stageNoLes, toUseMovement);
        return toUseMovement;

    }

    /**
     * 生成锁定的数据
     * @param crossingBaseInfo
     * @param stageNoLes
     * @param time
     * @return
     */
    public List<LockFlowDirection>
    genLockFlowDirectionMsg(CrossingService.CrossingBaseInfo crossingBaseInfo, int stageNoLes, int time) {

        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection> lockFlowDirections = new ArrayList<>();
        if( crossingBaseInfo == null){
            return lockFlowDirections;
        }

        List<Movement> toUseMovements = getMovementsByStage(crossingBaseInfo, stageNoLes);

        if(toUseMovements.isEmpty()){
            log.error("路口{}获取阶段{}流向为空", crossingBaseInfo.getCrossingId(), stageNoLes);
            return lockFlowDirections;
        }

        toUseMovements.forEach(
                movement -> {
                    DirectionType39900 inDir = DirectionType39900.trans(movement.getDirection());
                    DirectionType39900 outDir = DirectionType39900.trans(movement.getOutDir());
                    if(inDir == DirectionType39900.UNKNOWN || outDir == DirectionType39900.UNKNOWN){
                        log.error("路口{}阶段{}流向{}-{}转换成1049方向异常", crossingBaseInfo.getCrossingId(), stageNoLes,
                                movement.getDirection(), movement.getOutDir());
                        return;
                    }

                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection lockFlowDirectionMsg
                            = com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.LockFlowDirection.builder()
                            .CrossID(crossingBaseInfo.getCrossingId1049())
                            .Type("1")
                            .Entrance(String.valueOf(inDir.getCode()))
                            .Exit(String.valueOf(outDir.getCode()))
                            .LockType("1")
                            .Duration(String.valueOf(time)).build();
                    lockFlowDirections.add(lockFlowDirectionMsg);
                }
        );

        log.error("路口{}锁定阶段{}生成锁定流向数据项-{}", crossingBaseInfo.getCrossingId(), stageNoLes, lockFlowDirections);

        return lockFlowDirections;
    }




}
