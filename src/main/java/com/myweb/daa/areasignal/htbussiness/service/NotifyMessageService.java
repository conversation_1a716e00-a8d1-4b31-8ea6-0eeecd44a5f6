package com.myweb.daa.areasignal.htbussiness.service;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.NotifyMesageType;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.myweb.daa.areasignal.htbussiness.service.HtSignalService.DEFAULT_ID;

@Component
@Slf4j
public class NotifyMessageService {

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private CrossingService crossingService;


    @Autowired
    private NotifyMessageDbService notifyMessageDbService;

    /**
     * 数据处理对象设置
     */
    private Map<String, NotifyMesageType> notifyMesageTypeMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void initMap() {
        NotifyMesageType[] notifyMesageTypes = NotifyMesageType.values();
        for (int i = 0; i < notifyMesageTypes.length; i++) {
            NotifyMesageType notifyMesageType = notifyMesageTypes[i];
            if (notifyMesageType.isArray()) {
                continue;
            }
            //数据处理
            notifyMesageTypeMap.put(notifyMesageType.type().getSimpleName(), notifyMesageType);
        }
    }


    public void processPush(Object objectOrg, BaseMessage1049 baseMessage1049) {
        //数据项
        List<Object> datas = new ArrayList<>();
        if (objectOrg instanceof ArrayList) {
            ArrayList<Object> dataOrgs = (ArrayList<Object>) objectOrg;
            datas.addAll(dataOrgs);
        } else {
            datas.add(objectOrg);
        }

        if (datas.isEmpty()) {
            return;
        }

        NotifyMesageType notifyMesageType = notifyMesageTypeMap.get(datas.get(0).getClass().getSimpleName());
        if (notifyMesageType != null) {
            //针对数据项处理
            datas.stream().forEach(
                    object -> {
                        {
                            try {
                                //通过反射获取crossid数据项
                                Field crossIDField = object.getClass().getDeclaredField(notifyMesageType == NotifyMesageType.Notify_SignalControllerError ?
                                        "SignalControlerID" : "CrossID");
                                crossIDField.setAccessible(true);
                                String crossID = (String) (crossIDField.get(object));
                                if (crossID == null) { //320100 001 00001
                                    log.error("error cross/SignalControlerID id{} object {}", crossID, object);
                                    return;
                                }

                                Optional<CrossingService.CrossingBaseInfo> crossingInfoBy1049Op = crossingService.getCrossingInfoBy1049(crossID);
                                if (!crossingInfoBy1049Op.isPresent()) {
                                    log.warn("没有找到1049路口对应的莱斯路口-原始路口编号{}，数据项-{}", crossID, object);
                                    return;
                                }

                                String signalId = crossingInfoBy1049Op.get().getControllerId();
                                String lesCrossId = crossingInfoBy1049Op.get().getCrossingId();
                                P1049Entity p1049Entity = P1049Entity.builder().type(object.getClass().getSimpleName())
                                        .no(DEFAULT_ID)
                                        .subJuncNo(crossingInfoBy1049Op.get().getSubJuncNo())
                                        .signalId(lesCrossId)
                                        .updateDate(new Date())
                                        .ip(baseMessage1049.getAddress())
                                        .data(JSONObject.toJSONString(object)).build();
                                ht1049SignalCacheService.updateData(p1049Entity);

                                //判定数据项是否需要存储
                                if (notifyMesageType.needSave()) {
                                    //删除旧的数据项
                                    notifyMessageDbService.deleteAndSave(lesCrossId, object.getClass().getSimpleName(),
                                            p1049Entity);

                                }

                                /**如果数据项需要推动到mq中*/
                                if (notifyMesageType.publish()) {
                                    //将原始ID设置变为les内部ID
                                    crossIDField.set(object, lesCrossId);

                                    messagePublisher.publishMessage(object);
                                }

                                return;
                            } catch (NoSuchFieldException e) {
                                log.error("处理异常1-{}", baseMessage1049, e);
                            } catch (IllegalAccessException e) {
                                log.error("处理异常2-{}", baseMessage1049, e);
                            }
                        }
                    }
            );
        }
    }
}
