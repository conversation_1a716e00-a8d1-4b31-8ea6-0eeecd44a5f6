package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.myweb.daa.areasignal.htbussiness.service.HtSignalService.DEFAULT_ID;

@Component
@Slf4j
public class CrossCtrlInfoPublish {
    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    /**
     * 存储路口最新的控制状态数据项
     */
    private Map<String, CrossCtrlInfo> crossPlanMap = new ConcurrentHashMap<>();

    /**
     * 获取当前信号机运行的方案数据
     * @param crossID
     * @return
     */
    public Optional<Integer> getCurPlanNo(String crossID){
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return Optional.empty();
        }

        {   ///20230413 获取当前运行的方案编号
            Map<String, P1049Entity> signalInfo = ht1049SignalCacheService.getSignalInfoMap().get(crossingInfoOp.get().getCrossingId());
            if (signalInfo != null) {
                try {
                    //获取当前运行的方案
                    String planParaKey = P1049Entity.get1049DataKey(crossingInfoOp.get().getCrossingId(),
                            crossingInfoOp.get().getSubJuncNo(),
                            CrossCtrlInfo.class.getSimpleName(),
                            DEFAULT_ID);
                    if (signalInfo.containsKey(planParaKey)) {
                        //获取相序数据项
                        CrossCtrlInfo crossPlan = JSONObject.parseObject(signalInfo.get(planParaKey).getData(), CrossCtrlInfo.class);
                        if (crossPlan != null && crossPlan.getPlanNo() != null) {
                            return Optional.of(crossPlan.getPlanNo());
                        }
                    }
                } catch (Exception e) {
                    log.error("数据处理异常", e);
                }
            }
        }

        return Optional.empty();
    }


    /**
     * 发送当前信号机运行的方案
     *
     * @param crossID
     */
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void publishCrossPlan(String crossID) {
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
            if (!crossingInfoOp.isPresent()) {
                return;
            }
            Optional<Integer> curPlanNoOp = getCurPlanNo(crossID);
            //发布当前运行的方案
            if(curPlanNoOp.isPresent()) {
                notifyCrossPlan(curPlanNoOp.get(), crossingInfoOp.get(), false);
            }

    }


    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processCrossPlan(CrossCtrlInfo crossCtrlInfo) {
        String crossID = crossCtrlInfo.getCrossID();
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        //检查路口状态是否发生变化
        boolean needNotify = false;
        {
            CrossCtrlInfo crossCtrlInfoOld = crossPlanMap.get(crossingInfoOp.get().getCrossingId());
            //第一次获取路口状态
            if (crossCtrlInfoOld == null) {
                needNotify = true;
            } else {
                Integer valueNew = crossCtrlInfo.getPlanNo();
                Integer valueOld = crossCtrlInfoOld.getPlanNo();
                //如果数据均不为null
                if (valueNew != null && valueOld != null) {
                    needNotify = !(valueNew.equals(valueOld));
                } else if (valueNew != null) {
                    needNotify = true;
                }
            }
        }

        if (needNotify) {
            notifyCrossPlan((crossCtrlInfo.getPlanNo()), crossingInfoOp.get(), true);
        }

        crossPlanMap.put(crossID, crossCtrlInfo);
    }

    /**
     * 发布
     *
     * @param planNo
     * @param crossingInfo
     */
    public void notifyCrossPlan(int planNo, CrossingService.CrossingBaseInfo crossingInfo, boolean needSgp) {

        com.myweb.daa.areasignal.centralsystem.param.CrossPlan crossPlanCentral =
                com.myweb.daa.areasignal.centralsystem.param.CrossPlan.builder()
                        .noArea(crossingInfo.getNoArea())
                        .noJunc(crossingInfo.getNoJunc())
                        .signalControllerID(crossingInfo.getControllerId())
                        .planNo(planNo)
                        .crossingSeqNo(crossingInfo.getSubJuncNo())
                        .build();
        List<Object> objectList = new ArrayList<>();
        objectList.add(crossPlanCentral);
        MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfo.getControllerId(),
                com.myweb.daa.areasignal.centralsystem.param.CrossPlan.MqObjectId, objectList);
        messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossPlan.class.getSimpleName().toLowerCase(),
                mqMessage);
        if (needSgp) {
            messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossPlanCentral)
                    .build());
        }
    }
}
