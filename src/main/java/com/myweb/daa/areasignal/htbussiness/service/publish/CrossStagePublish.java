package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.CrossPlan;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.myweb.daa.areasignal.htbussiness.service.HtSignalService.DEFAULT_ID;

@Component
@Slf4j
public class CrossStagePublish {
    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;


    /**
     * 记录周期切换数据项
     */
    @Data
    public static class CycleInfo {
        private com.myweb.daa.areasignal.centralsystem.param.CrossCycle crossCycle;
        private LocalDateTime localDateTime;
        private long localTime;
    }

    private Map<String, CycleInfo> cycleInfoMap = new ConcurrentHashMap<>();


    /**
     * 周期切换数据推送
     *
     * @param crossingBaseInfo
     */
    private void notifyCrossCycle(CrossingService.CrossingBaseInfo crossingBaseInfo) {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        // LocalDateTime to epoch seconds
        long miliseconds = now.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
        com.myweb.daa.areasignal.centralsystem.param.CrossCycle crossCycle = com.myweb.daa.areasignal.centralsystem.param.CrossCycle.builder()
                .noArea(crossingBaseInfo.getNoArea())
                .noJunc(crossingBaseInfo.getNoJunc())
                .signalControllerID(crossingBaseInfo.getControllerId())
                .lenCycle((int) 0)
                .lastCycleLen(0)
                .startTime(miliseconds)
                .crossingSeqNo(crossingBaseInfo.getSubJuncNo())
                .build();

        //查看是否需要修改为真实的周期运行时间
        CycleInfo cycleInfo = cycleInfoMap.get(crossingBaseInfo.getCrossingId());
        if (cycleInfo != null) {
            double time = ((int) (System.currentTimeMillis() - cycleInfo.getLocalTime())) / 1000.0;
            BigDecimal d = new BigDecimal(time);
            BigDecimal bigDecimal = d.setScale(0, RoundingMode.HALF_UP);
            log.debug("周期运行时间-{},设置为-{}", time, bigDecimal.intValue());
            crossCycle.setLenCycle(Math.abs(bigDecimal.intValue()));
            crossCycle.setLastCycleLen(Math.abs(bigDecimal.intValue()));
            cycleInfo.setLocalDateTime(LocalDateTime.now());
            cycleInfo.setLocalTime(System.currentTimeMillis());
            cycleInfo.setCrossCycle(crossCycle);
        } else {
            CycleInfo cycleInfo1 = new CycleInfo();
            cycleInfo1.setLocalDateTime(LocalDateTime.now());
            cycleInfo1.setCrossCycle(crossCycle);
            cycleInfo1.setLocalTime(System.currentTimeMillis());
            cycleInfoMap.put(crossingBaseInfo.getCrossingId(), cycleInfo1);
        }

        List<Object> objectList = new ArrayList<>();
        objectList.add(crossCycle);
        MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingBaseInfo.getControllerId(),
                com.myweb.daa.areasignal.centralsystem.param.CrossCycle.MqObjectId, objectList);
        messageSender.send(CrossCycle.class.getSimpleName().toLowerCase(), mqMessage);
        messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossCycle)
                .build());
    }

    /**
     * 存储路口最新的状态切换数据项
     */
    private Map<String, CrossStage> crossStageMap = new ConcurrentHashMap<>();

    /**
     * 下面所有的crossId均已经通过反射修改成了lesSignalId，直接进行数据的使用即可
     *
     * @param crossStage
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processCrossStage(CrossStage crossStage) {
        String crossID = crossStage.getCrossID();

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }
        String signalId = crossingInfoOp.get().getControllerId();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);

        //根据莱斯阶段编号，转换成1049阶段编号
        Optional<String> p1049StageNoOp = ht1049SignalCacheService.getCentralStageNo(signalId, crossingInfoOp.get().getSubJuncNo(),
                String.valueOf(crossStage.getCurStageNo()));
        if (!p1049StageNoOp.isPresent()) {
            log.warn("{}未找到1049阶段编号{}对应的中心机阶段编号", signalId, crossStage);
            return;
        }

        Optional<String> p1049StageNoOpOld = ht1049SignalCacheService.getCentralStageNo(signalId, crossingInfoOp.get().getSubJuncNo(),
                String.valueOf(crossStage.getLastStageNo()));
        if (!p1049StageNoOpOld.isPresent()) {
            log.warn("{}未找到1049上一个阶段编号{}对应的中心机阶段编号", signalId, crossStage);
            //尝试从旧数据中获取
            CrossStage crossStageOld = crossStageMap.get(crossingInfoOp.get().getCrossingId());
            if (crossStageOld != null) {
                p1049StageNoOpOld = ht1049SignalCacheService.getCentralStageNo(signalId, crossingInfoOp.get().getSubJuncNo(),
                        String.valueOf(crossStageOld.getCurStageNo()));
            } else {
                p1049StageNoOpOld = Optional.of(String.valueOf(crossStage.getLastStageNo()));
            }
        }

        //内存中保存当前运行的阶段
        crossStageMap.put(crossID, crossStage);

        {


            com.myweb.daa.areasignal.centralsystem.param.CrossStage crossStageCentral =
                    com.myweb.daa.areasignal.centralsystem.param.CrossStage.builder()
                            .noArea(crossingInfoOp.get().getNoArea())
                            .noJunc(crossingInfoOp.get().getNoJunc())
                            .signalControllerID(crossingInfoOp.get().getControllerId())
                            .noOldStage(Integer.parseInt(p1049StageNoOpOld.get()))
                            .noNewStage(Integer.parseInt(p1049StageNoOp.get()))
                            .lenOldStage(Integer.parseInt((crossStage.getLastStageLen() == null || crossStage.getLastStageLen().isEmpty()) ? "0" : crossStage.getLastStageLen()))
                            .lenNewStage(0)
                            .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                            .build();


            {   ///20230413 获取当前运行的方案编号
                Map<String, P1049Entity> signalInfo = ht1049SignalCacheService.getSignalInfoMap().get(crossingInfoOp.get().getCrossingId());
                if (signalInfo != null) {
                    try {
                        //获取当前运行的方案
                        String planParaKey = P1049Entity.get1049DataKey(crossingInfoOp.get().getCrossingId(),
                                crossingInfoOp.get().getSubJuncNo(),
                                CrossCtrlInfo.class.getSimpleName(),
                                DEFAULT_ID);
                        if (signalInfo.containsKey(planParaKey)) {
                            //获取相序数据项
                            CrossCtrlInfo crossCtrlInfo = JSONObject.parseObject(signalInfo.get(planParaKey).getData(), CrossCtrlInfo.class);
                            int planNo = (crossCtrlInfo.getPlanNo());
                            crossStageCentral.setCurPlanNo(planNo);
                        }
                    } catch (Exception e) {
                        log.error("数据处理异常", e);
                    }
                }
            }
            List<Object> objectList = new ArrayList<>();
            objectList.add(crossStageCentral);
            MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfoOp.get().getControllerId(),
                    com.myweb.daa.areasignal.centralsystem.param.CrossStage.MqObjectId, objectList);
            messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossStage.class.getSimpleName().toLowerCase(),
                    mqMessage);
            messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossStageCentral)
                    .build());
        }
    }


    /**
     * 获取路口当前运行的阶段
     * @param crossIdLes
     * @return
     */
    public Optional<String> getCurrentStageNo1049_2(String crossIdLes){
        CrossStage crossStage = crossStageMap.get(crossIdLes);
        if(crossStage == null || crossStage.getCurStageNo() == null){
            return Optional.empty();
        }
        return Optional.of(crossStage.getCurStageNo());
    }

    /**
     * 从内存数据中获取当运行的阶段
     * @param crossIdLes
     * @return
     */
    public Optional<String> getCurrentStageNo1049(String crossIdLes)
    {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossIdLes);
        if (!crossingInfoOp.isPresent()) {
            log.error("获取路口{}运行的下一个阶段时，没有找到路口数据", crossIdLes);
            return Optional.empty();
        }

        Map<String, P1049Entity> signalInfo = ht1049SignalCacheService.getSignalInfoMap().get(crossIdLes);
        if (signalInfo != null) {
            //获取当前运行的方案
            String crossStageKey = P1049Entity.get1049DataKey(crossIdLes,
                    crossingInfoOp.get().getSubJuncNo(),
                    CrossStage.class.getSimpleName(),
                    DEFAULT_ID);
            if (signalInfo.containsKey(crossStageKey)) {
                //获取相序数据项
                CrossStage crossStage = JSONObject.parseObject(signalInfo.get(crossStageKey).getData(), CrossStage.class);
                return Optional.of(crossStage.getCurStageNo());
            } else {
                return Optional.empty();
            }
        }else {
            return Optional.empty();
        }
    }


    /**
     * 根据当前运行的阶段以及运行的方案编号，获取下一个阶段id
     * @param crossIdLes
     * @return
     */
    public Optional<String> getNextStageNo1049(String crossIdLes){

        Optional<String> currentStageNo1049Op = getCurrentStageNo1049(crossIdLes);
        if(!currentStageNo1049Op.isPresent()){
            log.error("获取路口{}运行的下一个阶段时，没有找到路口当前路口运行的阶段", crossIdLes);
            return Optional.empty();
        }

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossIdLes);
        if (!crossingInfoOp.isPresent()) {
            log.error("获取路口{}运行的下一个阶段时，没有找到路口数据", crossIdLes);
            return Optional.empty();
        }

        Map<String, P1049Entity> signalInfo = ht1049SignalCacheService.getSignalInfoMap().get(crossIdLes);
        if (signalInfo != null) {
            //获取当前运行的方案
            String planParaKey = P1049Entity.get1049DataKey(crossIdLes,
                    crossingInfoOp.get().getSubJuncNo(),
                    CrossPlan.class.getSimpleName(),
                    DEFAULT_ID);
            if (signalInfo.containsKey(planParaKey)) {
                //获取相序数据项
                CrossCtrlInfo crossPlan = JSONObject.parseObject(signalInfo.get(planParaKey).getData(), CrossCtrlInfo.class);
                int planNo = (crossPlan.getPlanNo());
                log.debug("信号机-{}当前方案-{}", crossingInfoOp.get().getControllerId(), planNo);

                //当前已经进入方案优化阶段
                {
                    Optional<PlanParam> planParamOptional = ht1049SignalCacheService.getData(crossingInfoOp.get().getCrossingId(),
                            crossingInfoOp.get().getSubJuncNo(), String.valueOf(planNo), PlanParam.class);

                    if (planParamOptional.isPresent()) {

                        StageNoList stageNoList = new StageNoList(); //planParamOptional.get().getStageNoList();
                        if (stageNoList == null || stageNoList.getStageNo() == null || stageNoList.getStageNo().isEmpty()) {
                            log.error("获取路口{}运行的下一个阶段时，当前运行的方案{}数据异常-{}", crossIdLes, planNo, planParamOptional.get());
                            return Optional.empty();
                        }

                        List<String> stageNoInPlan = stageNoList.getStageNo();

                        int index = stageNoInPlan.indexOf(currentStageNo1049Op.get());
                        if( -1 == index){
                            log.error("获取路口{}运行的下一个阶段时，当前运行的方案{}中没有找到运行的阶段{}-方案数据{}", crossIdLes, planNo,
                                    currentStageNo1049Op.get(), planParamOptional.get());
                            return Optional.empty();
                        }else{
                            int nextIndex = ((index + 1) % stageNoInPlan.size());
                            String nextStageNo = stageNoInPlan.get(nextIndex);
                            log.error("路口{}当前运行阶段是{},下一个阶段时{}", crossIdLes, currentStageNo1049Op.get(), nextStageNo);
                            return Optional.of(nextStageNo);
                        }

                    }else{
                        log.error("获取路口{}运行的下一个阶段时，没有找到当前运行的方案{}的数据", crossIdLes, planNo);
                        return Optional.empty();
                    }
                }
            }else{
                log.error("获取路口运行{}的下一个阶段时，没有找到当前运行的方案", crossIdLes);
                return Optional.empty();
            }
        }
        else{
            log.error("获取路口运行的下一个阶段时，没有找到内存数据-{}", crossIdLes);
            return Optional.empty();
        }
    }

}
