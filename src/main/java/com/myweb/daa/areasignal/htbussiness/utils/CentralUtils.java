package com.myweb.daa.areasignal.htbussiness.utils;


import com.les.ads.ds.enums.DirectionType;
import com.myweb.daa.areasignal.centralsystem.param.DirectionType1049;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class CentralUtils {
    private static Map<Integer, Integer> modeMap = new ConcurrentHashMap<>();
    public static String DEFAULT_OFFLINE_MODE = String.valueOf(LesControlMode.OFFLINE.value());
    public static int DEFAULT_SYSTEM_PLANNO = 123;
    public static int DEFAULT_OPTIMIZE_PLANNO = 124;
    public static int DEFAULT_TMP_PLANNO = 125;





    /**
     * 根据莱斯内部转换成1049方向数据
     * @param directionType
     * @return
     */
    public static Optional<DirectionType1049> getDirectionType1049(DirectionType directionType){
        return Arrays.stream(DirectionType1049.values()).filter(
                directionType1049 -> directionType1049.directionType().getCode() == directionType.getCode()
        ).findAny();
    }

}
