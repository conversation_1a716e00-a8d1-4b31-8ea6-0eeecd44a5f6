package com.myweb.daa.areasignal.htbussiness.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;

/**
 * 用于测试时候记录洛普数据状态
 */
@Entity
@Data
@Table(name = "P1049Log")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class P1049Log {

    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(length = 20, nullable = false)
    private String signalId;

    /**
     * 类型名称
     */
    private String type;

    /**
     * 接收数据时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recvDate;

    /**
     * json数据项
     */
    @Column(length = 5000, nullable = false)
    private String data;
}
