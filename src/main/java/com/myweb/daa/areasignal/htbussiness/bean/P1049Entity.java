package com.myweb.daa.areasignal.htbussiness.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;

@Entity
@Data
@Table(name = "P1049Entity")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class P1049Entity {
    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(length = 50, nullable = false)
    private String signalId;

    private int subJuncNo;

    /**
     * 类型名称
     */
    private String type;

    /**
     * 查询数据no
     */
    private String no;

    /**
     * 接收数据时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 数据源IP
     */
    @Column(length = 30)
    private String ip;

    /**
     * json数据项
     */
    @Column(length = 5000000, nullable = false)
    private String data;

    /**
     * 获取内存关键字数据项
     *
     * @return
     */
    public String getKey() {
        return get1049DataKey(signalId, subJuncNo, type, no);
    }

    public static String get1049DataKey(String signalId, int subJuncNo, String type, String no) {
        return signalId + "###" + subJuncNo + "###" + type + "###" + no;
    }

    public static String get1049DataKeyPrefix(String signalId, int subJuncNo, String type) {
        return signalId + "###" + subJuncNo + "###" + type + "###";
    }
}
