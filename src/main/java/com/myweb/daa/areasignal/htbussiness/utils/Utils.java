package com.myweb.daa.areasignal.htbussiness.utils;

import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.param.P1049ControlMode;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class Utils {
    private static Map<Integer, Integer> modeMap = new ConcurrentHashMap<>();
    public static String DEFAULT_MODE = String.valueOf(LesControlMode.FIXED_CYCLE.value());


    private static Map<Integer, Integer> mode1049Map = new ConcurrentHashMap<>();

    static {
        /**
         *      * ht
         *      * 1		11	特殊控制-关灯   -》 25
         *      * 2		12	特殊控制-全红   -》 26
         *      * 3		13	特殊控制-全部黄闪  -》 24
         *              14  步进   -》 5      //中控
         *              15  跳相   -》 4      //中控
         *      * 4		21	单点多时段定时控制  -》 23
         *      * 5		22	单点感应控制  -》 22
         *      * 6		23	单点自适应控制  -》 22
         *      * 7		31	线协调控制 -》 21
         *      * 8		41	区域协调控制 -》 21
         *      * 9		51	干预控制-手动控制 -》 4
         *      * 10		52	干预控制-锁定阶段控制 -》 4
         *      * 11		53	干预控制-指定方案 -》4
         *           54 锁定流向 -》 7 锁定流向
         *      60 - 公交优先周期优化
         *      61 -阶段优化
         */
        modeMap.put(P1049ControlMode.CLOSE_LAMP.value(), LesControlMode.CLOSE_LAMP.value());
        modeMap.put(P1049ControlMode.ALL_RED.value(), LesControlMode.ALL_RED.value());
        modeMap.put(P1049ControlMode.YELLOW_FLASH.value(), LesControlMode.YELLOW_FLASH.value());
        modeMap.put(P1049ControlMode.SYSTEM_MANUAL.value(), LesControlMode.SYSTEM_MANUAL.value());
        modeMap.put(P1049ControlMode.LOCK_FLOW.value(), LesControlMode.LOCK_FLOW.value());
        modeMap.put(P1049ControlMode.ADAPTIVE_CONTROL.value(), LesControlMode.ADAPTIVE_CONTROL.value());
        modeMap.put(P1049ControlMode.SINGLE_CONTROL.value(), LesControlMode.OFFLINE.value());
        modeMap.put(P1049ControlMode.FIXED_CYCLE.value(), LesControlMode.FIXED_CYCLE.value());
        modeMap.put(P1049ControlMode.ACTUATED_LOCAL_1.value(), LesControlMode.ACTUATED_LOCAL.value());
        modeMap.put(P1049ControlMode.ACTUATED_LOCAL_2.value(), LesControlMode.OPTIMIZE_LOCAL.value());
        modeMap.put(P1049ControlMode.BUS_PRI.value(), LesControlMode.BUS_PRIORITY_CONTROL.value());
        modeMap.put(P1049ControlMode.PEDESTRIAN_CROSSING.value(), LesControlMode.PEDESTRIAN_CROSSING.value());
        modeMap.put(P1049ControlMode.COORDINATE_LINE.value(), LesControlMode.COORDINATE_LOCAL.value());
        modeMap.put(P1049ControlMode.COORDINATE_AREA.value(), LesControlMode.ADAPTIVE_CONTROL.value());
        modeMap.put(P1049ControlMode.LOCK_FLOW_MANUAL.value(), LesControlMode.SYSTEM_MANUAL.value());
        modeMap.put(P1049ControlMode.LOCK_FLOW_STAGE.value(), LesControlMode.LOCK_FLOW.value());
        modeMap.put(P1049ControlMode.LOCK_FLOW_PLAN.value(), LesControlMode.TEMPORARY_PATTERN.value());
        modeMap.put(P1049ControlMode.LOCK_FLOWS.value(), LesControlMode.LOCK_FLOWS.value());
        modeMap.put(P1049ControlMode.HOLO.value(), LesControlMode.ADAPTIVE_CONTROL.value());
        modeMap.put(99, LesControlMode.COORDINATE_LOCAL.value());

        //将les控制方式反转为1049
        mode1049Map.put(LesControlMode.YELLOW_FLASH.value(), P1049ControlMode.YELLOW_FLASH.value());
        mode1049Map.put(LesControlMode.ALL_RED.value(), P1049ControlMode.ALL_RED.value());
        mode1049Map.put(LesControlMode.CLOSE_LAMP.value(), P1049ControlMode.CLOSE_LAMP.value());
        mode1049Map.put(LesControlMode.OFFLINE.value(), P1049ControlMode.FIXED_CYCLE.value());
        mode1049Map.put(LesControlMode.COORDINATE_LOCAL.value(), P1049ControlMode.COORDINATE_LINE.value());
        mode1049Map.put(LesControlMode.FIXED_CYCLE.value(), P1049ControlMode.FIXED_CYCLE.value());
        mode1049Map.put(LesControlMode.PEDESTRIAN_CROSSING.value(), P1049ControlMode.PEDESTRIAN_CROSSING.value());
    }


    /**
     * 特殊控制方式需要进行修复
     *
     * @param crossingBaseInfo
     * @param p1049Mode
     * @return
     */
    public static String specialChange(CrossingService.CrossingBaseInfo crossingBaseInfo, String p1049Mode) {
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingBaseInfo.getControllerId());
        if (signalBrandPort == null) {
            return p1049Mode;
        }

        //海康53号为定周期
        if ((signalBrandPort.brandCode() == SignalBrandPort.HK.brandCode())
                && String.valueOf(P1049ControlMode.LOCK_FLOW_PLAN.value()).equalsIgnoreCase(p1049Mode)) {
            return String.valueOf(P1049ControlMode.FIXED_CYCLE.value());
        } else if ((signalBrandPort.brandCode() == SignalBrandPort.HK.brandCode())
                && String.valueOf(P1049ControlMode.LOCK_FLOW_MANUAL.value()).equalsIgnoreCase(p1049Mode)) {
            return String.valueOf(P1049ControlMode.LOCK_FLOWS.value());
        } else if ((signalBrandPort.brandCode() == SignalBrandPort.HD.brandCode())
                && String.valueOf(P1049ControlMode.LOCK_FLOW_MANUAL.value()).equalsIgnoreCase(p1049Mode)) {
            return String.valueOf(P1049ControlMode.LOCK_FLOW_STAGE.value());
        } else if ((signalBrandPort.brandCode() == SignalBrandPort.FJ.brandCode())
                && String.valueOf(P1049ControlMode.FIXED_CYCLE.value()).equalsIgnoreCase(p1049Mode)) {
            return String.valueOf(P1049ControlMode.COORDINATE_LINE.value());
        } else {
            return p1049Mode;
        }

    }

    /**
     * 将mode转换为les的mode
     *
     * @param modeOrg
     * @return
     */
    public static String change2LesMode(CrossingService.CrossingBaseInfo crossingBaseInfo, String modeOrg) {
        //控制方式转换
        String mode = specialChange(crossingBaseInfo, modeOrg);
        if (modeMap.containsKey(Integer.valueOf(mode))) {
            return String.valueOf(modeMap.get(Integer.valueOf(mode)));
        } else {
            //恢复定周期
            log.error("控制方式转换失败-原始{}", mode);
            return DEFAULT_MODE;
        }
    }


    /**
     * 将les的mode转换为l049的mode
     *
     * @param mode
     * @return
     */
    public static String change21049Mode(String mode) {
        if (mode1049Map.containsKey(Integer.valueOf(mode))) {
            return String.valueOf(mode1049Map.get(Integer.valueOf(mode)));
        } else {
            //恢复定周期
            log.error("控制方式转换1049失败-莱斯控制方式{}", mode);
            return String.valueOf(LesControlMode.COORDINATE_LOCAL.value());
        }
    }


    /**
     * 判定下发的控制方式是否是特殊的控制方式
     *
     * @param mode
     * @return
     */
    public static boolean isSpecialMode(int mode) {
        if (LesControlMode.YELLOW_FLASH.value() == mode) {
            return true;
        }
        if (LesControlMode.ALL_RED.value() == mode) {
            return true;
        }
        if (LesControlMode.CLOSE_LAMP.value() == mode) {
            return true;
        }
        return false;
    }

}
