package com.myweb.daa.areasignal.htbussiness.bean;

import com.les.ads.ds.signal.LampGroup;
import com.myweb.daa.areasignal.centralsystem.param.PhaseParam;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;

/**
 * 推送消息报文处理
 */
public enum NotifyMesageType {
    Notify_CrossParam(CrossParam.class,  false),
    Notify_PlanParam(PlanParam.class,  false),
    Notify_StageParam(StageParam.class,  false),
    Notify_PhaseParam(PhaseParam.class,  false),
    Notify_CrossCtrlInfo(CrossCtrlInfo.class,   true),
    Notify_LampGroup(LampGroup.class,  false),
    Notify_CrossState(CrossState.class,  true, true),
    Notify_CrossStage(CrossStage.class,  true),
    Notify_CrossCycle(CrossCycle.class,  true),
    Notify_CrossTrafficData(CrossTrafficData.class,  false),

    Notify_SignalControllerError(SignalControllerError.class,  true),
    Notify_VarLaneStatus(VarLaneStatus.class,  true),
    Notify_StageTrafficData(StageTrafficData.class,  true),
    ;
    private Class type; //数据类型
    private boolean isArray; //数据是否是数组
    private boolean publish; //是否需要发布到消息总线

    private boolean needSave; //是否需要保存

    NotifyMesageType(Class type, boolean publish) {
        this.type = type;
        this.isArray = isArray;
        this.publish = publish;
        this.needSave = false;
    }

    NotifyMesageType(Class type, boolean publish, boolean needSave) {
        this.type = type;
        this.isArray = isArray;
        this.publish = publish;
        this.needSave = needSave;
    }

    public Class type() {
        return this.type;
    }

    public boolean isArray() {
        return this.isArray;
    }

    public boolean publish() {
        return this.publish;
    }

    public boolean needSave() {
        return this.needSave;
    }
}
