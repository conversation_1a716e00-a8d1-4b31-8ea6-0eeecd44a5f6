package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.VarLaneStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 16:51
 */
@Component
@Slf4j
public class VarLaneStatusPublish {

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processVarLaneStatus(VarLaneStatus varLaneStatus) {
        log.debug("收到可变车道状态数据项-{}", varLaneStatus);
    }

}
