package com.myweb.daa.areasignal.htbussiness.process;

import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Infos;
import com.myweb.daa.areasignal.htbussiness.bean.QP1049Entity;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Component
@Slf4j
public class P1049InfosProcess {
    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    /**
     * 删除特定的数据项指定no
     *
     * @param signalId
     * @param type
     * @param no
     */
    @Transactional
    public void deleteP1049Infos(String signalId, int subJuncNo, String type, String no) {

        //移除内存数据项
        ht1049SignalCacheService.deleteData(signalId, subJuncNo, type, no);

        //移除数据库数据项
        queryFactory.delete(QP1049Entity.p1049Entity).where(
                QP1049Entity.p1049Entity.signalId.eq(signalId)
                        .and(QP1049Entity.p1049Entity.type.eq(type))
                        .and(QP1049Entity.p1049Entity.no.eq(no))).execute();
    }


    /**
     * 删除特定的数据项指定
     *
     * @param signalId
     * @param type
     */
    @Transactional
    public void deleteP1049Infos(String signalId, int subJuncNo, String type) {
        //移除内存数据项
        ht1049SignalCacheService.deleteData(signalId, subJuncNo, type);

        //移除数据库数据项
        queryFactory.delete(QP1049Entity.p1049Entity).where(
                QP1049Entity.p1049Entity.signalId.eq(signalId)
                        .and(QP1049Entity.p1049Entity.type.eq(type))
        ).execute();
    }

    /**
     * 数据处理
     *
     * @param p1049Infos
     */

    @Transactional
    public void p1049InfoProcessSync(P1049Infos p1049Infos) {
        p1049InfoProcess(p1049Infos);
    }

    @EventListener
    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    @Transactional
    public void p1049InfoProcess(P1049Infos p1049Infos) {
        log.trace("准备保存数据项-{}", p1049Infos);

        String signalId = p1049Infos.getSignalId();
        //保存新的数据项
        List<P1049Entity> controlerInfos = p1049Infos.getControlerInfos();
        controlerInfos.stream().forEach(
                controlerInfo -> {
                    controlerInfo.setSignalId(signalId);
                    controlerInfo.setUpdateDate(new Date());
                }
        );

        //更新本地内存数据项
        ht1049SignalCacheService.updateData(p1049Infos);

        //删除原先数据项
        Map<String, List<String>> typeDataMap = new HashMap<>();
        for (int i = 0; i < controlerInfos.size(); i++) {
            List<String> nos = typeDataMap.get(controlerInfos.get(i).getType());
            if (nos == null) {
                nos = new ArrayList<>();
                typeDataMap.put(controlerInfos.get(i).getType(), nos);
                nos.add(controlerInfos.get(i).getNo());
            } else {
                if (!nos.contains(controlerInfos.get(i).getNo())) {
                    nos.add(controlerInfos.get(i).getNo());
                }
            }
        }
        //优化删除数据对象
        for (String type : typeDataMap.keySet()) {
            queryFactory.delete(QP1049Entity.p1049Entity).where(
                    QP1049Entity.p1049Entity.signalId.eq(signalId)
                            .and(QP1049Entity.p1049Entity.type.eq(type))
                            .and(QP1049Entity.p1049Entity.no.in(typeDataMap.get(type)))).execute();
        }


        Iterable<P1049Entity> p1049EntityIterable = () -> controlerInfos.iterator();
        repositoryDao.save(p1049EntityIterable, P1049Entity.class);
    }
}
