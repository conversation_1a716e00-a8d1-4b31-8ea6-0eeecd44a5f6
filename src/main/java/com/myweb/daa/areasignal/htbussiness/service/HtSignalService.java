package com.myweb.daa.areasignal.htbussiness.service;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.business.bean.LinkStatus;
import com.myweb.daa.areasignal.business.service.DataLoadService;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgOperator;
import com.myweb.daa.areasignal.centralsystem.mq.MqMsgType;
import com.myweb.daa.areasignal.centralsystem.param.CrossControlMode;
import com.myweb.daa.areasignal.centralsystem.param.CrossPlan;
import com.myweb.daa.areasignal.centralsystem.param.SystemControlAck;
import com.myweb.daa.areasignal.centralsystem.service.ControllerService;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.config.TestSignalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Infos;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.utils.CentralUtils;
import com.myweb.daa.areasignal.htbussiness.utils.Utils;
import com.myweb.daa.areasignal.protocol.p1049.process.MessageSendProcess;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
@Component
public class HtSignalService {

    /**
     * 默认数据项
     */
    public static final String DEFAULT_ID = "0";
    /**
     * 发送控制器
     */
    @Autowired
    private MessageSendProcess messageSendProcess;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private NotifyMessageService notifyMessageService;

    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private DataLoadService dataLoadService;

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private TestSignalConfigure testSignalConfigure;


    /**
     * 从应答中获取数据项
     */
    public static <T> Optional<T> getDataFromResult(JsonResult jsonResult, Object tscCmd, StringBuilder info) {
        //获取路口参数
        Object resultData = jsonResult.getData();
        if (!(resultData instanceof BaseMessage1049)) {
            info.append("应答数据项异常-请求" + tscCmd + "，应答-" + resultData);
            return Optional.empty();
        }

        BaseMessage1049 baseMessage1049_sysInfo = (BaseMessage1049) resultData;
        Optional<Object> firstMsgData = baseMessage1049_sysInfo.getFirstMsgData();
        if (!firstMsgData.isPresent()) {
            info.append("没有找到需求的应答数据项-请求" + tscCmd + "，应答-" + resultData);
            return Optional.empty();
        }

        if (firstMsgData.get() instanceof SDO_Error1049) {
            info.append("应答数据项-" + tscCmd + ",错误-" + firstMsgData.get());
            return Optional.empty();
        }


        T t = (T) (firstMsgData.get());
        if (t == null) {
            info.append("应答数据项类型异常-请求" + tscCmd + "，应答-" + resultData);
            return Optional.empty();
        }

        log.debug("获取到数据项-{}", t);
        return Optional.of(t);
    }

    public <T> JsonResult getTSCCmd(String signalControlerID, Class<T> clazz, SignalBrandPort signalBrandPort, String address) {
        return getTSCCmd(signalControlerID, clazz, "0", signalBrandPort, address);
    }


    /**
     * 获取信号机数据项
     *
     * @param signalControlerID 信号机数据时为信号机编号；路口数据时为路口编号
     * @param clazz             数据类型
     * @param no                数据NO
     * @param signalBrandPort
     * @param <T>
     * @return
     */
    public <T> JsonResult getTSCCmd(String signalControlerID, Class<T> clazz, String no,
                                    SignalBrandPort signalBrandPort, String address) {
        StringBuilder info = new StringBuilder();
        MessageType messageType_crossPara = MessageType.REQUEST;
        OperationName operationName_crossPara = OperationName.Get;
        boolean waitAck_crossPara = true;
        TSCCmd tscCmd_crossPara = TSCCmd.builder().ObjName(clazz.getSimpleName()).ID(signalControlerID).No(no).build();
        List<TSCCmd> tscCmds = new ArrayList<>();
        tscCmds.add(tscCmd_crossPara);
        JsonResult jsonResult_crossPara = messageSendProcess.processSend(messageType_crossPara,
                operationName_crossPara,
                tscCmds,
                waitAck_crossPara, signalBrandPort, TSCCmd.class, address);
        if (!jsonResult_crossPara.isSuccess()) {
            info.append(signalControlerID + "获取数据异常" + clazz.getSimpleName());
            return JsonResult.error(jsonResult_crossPara.getMessage());
        }

        //获取数据项
        StringBuilder info_crossPara = new StringBuilder();
        {
            Optional<List<T>> sysInfoOptional_crossPara = getDataFromResult(jsonResult_crossPara, tscCmd_crossPara, info_crossPara);
            if (!sysInfoOptional_crossPara.isPresent()) {
                return JsonResult.error(info.toString());
            }
            return new JsonResult(true, sysInfoOptional_crossPara.get());
        }
    }



    /**
     * 获取路口的所有参数，数据项请求一条返回多条
     *
     * @param crossingBaseInfo
     * @return
     */
    public JsonResult loadData(CrossingService.CrossingBaseInfo crossingBaseInfo) {
        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(crossingBaseInfo.getControllerId());
        if (!signalInfoOp.isPresent()) {
            return new JsonResult(false, "未知的信号机id" + crossingBaseInfo.getControllerId(), "");
        }

        //获取信号机数据项
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalInfoOp.get().getSignalId());

        HashMap<String, Object> dataResult = new HashMap<>();
        List<P1049Entity> controlerInfos = new ArrayList<>();
        {
            JsonResult jsonResult = loadDataByOrgSignalId(signalInfoOp.get().getSignalId1049(), signalBrandPort,
                    signalInfoOp.get().getSignalId(),
                    crossingBaseInfo.getAddress1049Ip());
            if (!jsonResult.isSuccess()) {
                log.error("获取信号机参数异常-{}", jsonResult);
                dataResult.put("signalDataResult", jsonResult);
            } else {
                controlerInfos.addAll((List<P1049Entity>) (jsonResult.getData()));
            }
        }

        //获取路口数据项
        {
            JsonResult jsonResult2 = loadDataByOrgCrossingId(crossingBaseInfo.getCrossingId1049(),
                    signalBrandPort, crossingBaseInfo.getCrossingId(), crossingBaseInfo.getSubJuncNo(),
                    crossingBaseInfo.getAddress1049Ip());
            if (!jsonResult2.isSuccess()) {
                log.error("获取路口参数异常-{}-当前路口参数-{}", jsonResult2, crossingBaseInfo);
                dataResult.put("crossDataResult", jsonResult2);
            } else {
                controlerInfos.addAll((List<P1049Entity>) (jsonResult2.getData()));
            }
        }

        dataResult.put("allData", controlerInfos);
        return new JsonResult(controlerInfos.isEmpty() ? false : true, dataResult);
    }


    /**
     * 支持的路口请求数据项
     */
    private static List<Class> avaliableDataClazz = new ArrayList<>();

    /**
     * 支持的信号机请求数据项
     */
    private static List<Class> avaliableDataClazz_Signal = new ArrayList<>();

    /**
     * 支持的系统参数调看
     */
    private static List<Class> avaliableDataClazz_System = new ArrayList<>();


    static {
        avaliableDataClazz.add(SysInfo.class);
        avaliableDataClazz.add(RegionParam.class);
        avaliableDataClazz.add(RouteParam.class);
        avaliableDataClazz.add(SubRegionParam.class);
        avaliableDataClazz.add(CrossParam.class);
        avaliableDataClazz.add(LampGroupParam.class);
        avaliableDataClazz.add(DetectorParam.class);
        avaliableDataClazz.add(LaneParam.class);
        avaliableDataClazz.add(PedestrianParam.class);
        avaliableDataClazz.add(SignalGroupParam.class);
        avaliableDataClazz.add(StageParam.class);
        avaliableDataClazz.add(PlanParam.class);
        avaliableDataClazz.add(DayPlanParam.class);
        avaliableDataClazz.add(ScheduleParam.class);
        avaliableDataClazz.add(SysState.class);
        avaliableDataClazz.add(CrossState.class);
        avaliableDataClazz.add(CrossCtrlInfo.class);
        avaliableDataClazz.add(CrossCycle.class);
        avaliableDataClazz.add(CrossStage.class);
        avaliableDataClazz.add(CrossSignalGroupStatus.class);
        avaliableDataClazz.add(CrossTrafficData.class);
        avaliableDataClazz.add(StageTrafficData.class);
        avaliableDataClazz.add(VarLaneStatus.class);
        avaliableDataClazz.add(RouteCtrlInfo.class);
        avaliableDataClazz.add(RouteSpeed.class);
        avaliableDataClazz.add(SCDoorStatus.class);

        avaliableDataClazz_Signal.add(SignalControllerError.class);
        avaliableDataClazz_Signal.add(SignalController.class);


        avaliableDataClazz_System.add(SysInfo.class);
        avaliableDataClazz_System.add(RegionParam.class);
        avaliableDataClazz_System.add(RouteList.class);
        avaliableDataClazz_System.add(SubRegionParam.class);
        avaliableDataClazz_System.add(RouteParam.class);

    }

    /**
     * 从数据中获取数据索引
     *
     * @param object
     * @param no
     * @return
     */
    private String getNoFromObject(Object object, String no) {
        if(object instanceof DataIndexAble){
            DataIndexAble dataIndexAble = (DataIndexAble) object;
            return dataIndexAble.dataNo();
        }
        return no;
    }


    /**
     * 获取路口的指定参数
     *
     * @param signalBrandPort 信号机类型
     * @param id              可能是信号机id也可能是路口id
     * @param type            数据类型
     * @param no              数据索引项
     * @param subJuncNo       子路口编号，有些数据项的子路口号是没有意义的
     * @return
     */
    public JsonResult loadDataWithId(SignalBrandPort signalBrandPort, String id, String type, String no, int subJuncNo, String address) {
        Optional<Class> optionalClass = avaliableDataClazz.stream().filter(clazz -> type.equalsIgnoreCase(clazz.getSimpleName())).findAny();
        if (!optionalClass.isPresent()) {
            optionalClass = avaliableDataClazz_Signal.stream().filter(clazz -> type.equalsIgnoreCase(clazz.getSimpleName())).findAny();
            if (!optionalClass.isPresent()) {
                return JsonResult.error("尚不支持请求数据项,支持的是 " +
                        avaliableDataClazz.stream().map(clazz -> clazz.getSimpleName()).collect(Collectors.joining(","))
                        +
                        avaliableDataClazz_Signal.stream().map(clazz -> clazz.getSimpleName()).collect(Collectors.joining(",")));
            }
        }

        List<P1049Entity> controlerInfos = new ArrayList<>();
        //路口基本参数
        JsonResult crossPraJR = getTSCCmd(id, optionalClass.get(), no, signalBrandPort, address);
        if (crossPraJR.isSuccess()) {

            {
                List<Object> datas = (List<Object>) (crossPraJR.getData());
                datas.stream().forEach(
                        data -> {
                            //获取真正的数据项
                            String noFromObject = getNoFromObject(data, no);
                            //获取基础数据项
                            controlerInfos.add(
                                    P1049Entity.builder().type(type)
                                            .no(noFromObject)
                                            .subJuncNo(subJuncNo)
                                            .ip(address)
                                            .data(JSONObject.toJSONString(data)).build());
                        }
                );
            }

            //发布数据项进行异步数据保存
            P1049Infos p1049Infos = P1049Infos.builder().signalId(
                    id
            ).controlerInfos(controlerInfos).build();
            messagePublisher.publishMessage(p1049Infos);
        }

        return crossPraJR;
    }

    public JsonResult loadCrossData(String lesCrossingId, String type, String no, int subJuncNo) {
        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(lesCrossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未知的路口id" + lesCrossingId);
        }
        List<P1049Entity> p1049Entities = new ArrayList<>();
        return loadCrossData(lesCrossingId, type, no, subJuncNo, crossingInfoOp.get().getAddress1049Ip(), p1049Entities);
    }

    /**
     * 获取路口的指定参数
     *
     * @param lesCrossingId
     * @return
     */
    public JsonResult loadCrossData(String lesCrossingId, String type, String no, int subJuncNo, String address, List<P1049Entity> controlerInfos) {
        Optional<Class> optionalClass = avaliableDataClazz.stream().filter(clazz -> type.equalsIgnoreCase(clazz.getSimpleName())).findAny();

        if (!optionalClass.isPresent()) {
            return JsonResult.error("尚不支持请求数据项,支持的是 " + avaliableDataClazz.stream().map(clazz -> clazz.getSimpleName()).collect(Collectors.joining(",")));
        }

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(lesCrossingId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未知的路口id" + lesCrossingId);
        }

        String crossId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());

        //路口基本参数
        JsonResult crossPraJR = getTSCCmd(crossId, optionalClass.get(), no, signalBrandPort, address);
        if (crossPraJR.isSuccess()) {
           {
                List<Object> datas = (List<Object>) (crossPraJR.getData());
                datas.stream().forEach(
                        data -> {
                            String noFromObject = getNoFromObject(data, no);
                            //获取基础数据项
                            controlerInfos.add(
                                    P1049Entity.builder().type(type)
                                            .no(noFromObject)
                                            .subJuncNo(subJuncNo)
                                            .ip(address)
                                            .data(JSONObject.toJSONString(data)).build());
                        }
                );
            }

            //发布数据项进行异步数据保存
            P1049Infos p1049Infos = P1049Infos.builder().signalId(
                    lesCrossingId
            ).controlerInfos(controlerInfos).build();
            messagePublisher.publishMessage(p1049Infos);
        }

        return crossPraJR;
    }


    public JsonResult simuData(String crossId, SignalBrandPort signalBrandPort, String address,
                               MessageType messageType, String content) {
        StringBuilder info = new StringBuilder();
        OperationName operationName = OperationName.Get;
        JsonResult jsonResult_crossPara = messageSendProcess.processSendSimu(messageType,
                operationName, signalBrandPort, address, content);
        if (!jsonResult_crossPara.isSuccess()) {
            return JsonResult.error(jsonResult_crossPara.getMessage());
        }
        return new JsonResult(true, "发送成功");
    }

    public JsonResult loadSignalData(String lesSignalId, int subJuncNo, String type, String no) {
        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(lesSignalId);
        if (!signalInfoOp.isPresent()) {
            return new JsonResult(false, "未知的信号机id" + lesSignalId);
        }
        return loadSignalData(lesSignalId, subJuncNo, type, no, signalInfoOp.get().getAddress1049Ip());
    }

    /**
     * 获取路口的指定参数
     *
     * @param lesSignalId
     * @return
     */
    public JsonResult loadSignalData(String lesSignalId, int subJuncNo, String type, String no, String address) {
        Optional<Class> optionalClass = avaliableDataClazz_Signal.stream().filter(clazz -> type.equalsIgnoreCase(clazz.getSimpleName())).findAny();

        if (!optionalClass.isPresent()) {
            return JsonResult.error("尚不支持请求数据项,支持的是 " + avaliableDataClazz_Signal.stream().map(clazz -> clazz.getSimpleName()).collect(Collectors.joining(",")));
        }

        Optional<ControllerService.SignalBaseInfo> signalInfoOp = controllerService.getSignalInfo(lesSignalId);
        if (!signalInfoOp.isPresent()) {
            return new JsonResult(false, "未知的信号机id" + lesSignalId);
        }

        String signalId = signalInfoOp.get().getSignalId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(lesSignalId);

        List<P1049Entity> controlerInfos = new ArrayList<>();

        //路口基本参数
        JsonResult crossPraJR = getTSCCmd(signalId, optionalClass.get(), no, signalBrandPort, address);
        if (crossPraJR.isSuccess()) {
             {
                List<Object> datas = (List<Object>) (crossPraJR.getData());
                datas.stream().forEach(
                        data -> {
                            String noFromObject = getNoFromObject(data, no);
                            //获取基础数据项
                            controlerInfos.add(
                                    P1049Entity.builder().type(type)
                                            .no(noFromObject)
                                            .subJuncNo(subJuncNo)
                                            .ip(address)
                                            .data(JSONObject.toJSONString(data)).build());
                        }
                );
            }

            //发布数据项进行异步数据保存
            P1049Infos p1049Infos = P1049Infos.builder().signalId(
                    lesSignalId
            ).controlerInfos(controlerInfos).build();
            messagePublisher.publishMessage(p1049Infos);
        }

        return crossPraJR;
    }


    /**
     * 根据原始信号机ID向外系统获取数据项
     *
     * @param signalControlerID
     * @param signalBrandPort
     * @param lesSignalID
     * @return
     */
    public JsonResult loadDataByOrgSignalId(String signalControlerID, SignalBrandPort signalBrandPort, String lesSignalID,
                                            String addressIp) {

        //获取当前内存中数据项
        Optional<SignalController> signalControllerOp = ht1049SignalCacheService.getData(signalControlerID, 0, DEFAULT_ID, SignalController.class);
        if (!signalControllerOp.isPresent()) {
            return new JsonResult(false, "没有找到信号机-" + signalControlerID + "原始数据项");
        }

        List<P1049Entity> controlerInfos = new ArrayList<>();
        //存储一份lessignalID的数据项
        {
            //获取基础数据项
            controlerInfos.add(
                    P1049Entity.builder().type(SignalController.class.getSimpleName())
                            .no(DEFAULT_ID)
                            .subJuncNo(0)
                            .ip(addressIp)
                            .data(JSONObject.toJSONString(signalControllerOp.get())).build());
        }

        //信号机异常数据项
         {
            try {
                JsonResult signalControlerErrorJR = getTSCCmd(signalControlerID, SignalControllerError.class, signalBrandPort, addressIp);
                if (signalControlerErrorJR.isSuccess()) {
                    //获取基础数据项

                    List<Object> datas = (List<Object>) (signalControlerErrorJR.getData());
                    datas.stream().forEach(
                            data -> {
                                //获取基础数据项
                                controlerInfos.add(
                                        P1049Entity.builder().type(SignalControllerError.class.getSimpleName())
                                                .no(DEFAULT_ID)
                                                .subJuncNo(0)
                                                .ip(addressIp)
                                                .data(JSONObject.toJSONString(signalControlerErrorJR.getData())).build());

                                {
                                    //发送状态用于数据状态更新,需要将crossId修改为LesSignalId
                                    try {
                                        SignalControllerError signalControlerError = (SignalControllerError) BeanUtils.cloneBean((signalControlerErrorJR.getData()));
                                        signalControlerError.setSignalControllerId(lesSignalID);
                                        String errorDesc = signalControlerError.getErrorDesc();
                                        String errorDescGbk = new String(errorDesc.getBytes("UTF-8"));
                                        log.error("{}故障数据项描述-{}", signalControlerID, errorDescGbk);
                                        messagePublisher.publishMessage(signalControlerError);
                                    } catch (Exception e) {
                                        log.error("拷贝对象SignalControlerError出现异常");
                                    }
                                }
                            }
                    );

                } else {
                    log.error("获取信号机{}故障数据异常", signalControlerID);
                }
            } catch (Exception e) {
                log.error("出现异常-{}", e);
            }
        }

        //发布数据项进行异步数据保存
        P1049Infos p1049Infos = P1049Infos.builder().signalId(lesSignalID
        ).controlerInfos(controlerInfos).build();
        messagePublisher.publishMessage(p1049Infos);

        return new JsonResult(true, "获取信号机" + signalControlerID + "数据成功", controlerInfos);
    }

    /**
     * 根据原始路口ID向外系统获取数据项
     *
     * @param crossingId
     * @param signalBrandPort
     * @param lesCrossingID
     * @return
     */
    public JsonResult loadDataByOrgCrossingId(String crossingId, SignalBrandPort signalBrandPort, String lesCrossingID,
                                              int subJuncNo, String addressIp) {
        List<P1049Entity> controlerInfos = new ArrayList<>();

        //路口基本参数
        JsonResult crossPraJR = getTSCCmd(crossingId, CrossParam.class, signalBrandPort, addressIp);
        if (!crossPraJR.isSuccess()) {
            return crossPraJR;
        }


        //所有的方案数据项
        List<CrossParam> crossParams = (List<CrossParam>) crossPraJR.getData();
        if(crossParams == null || crossParams.isEmpty()){
            String logStr = (crossingId + "获取数据异常" + CrossParam.class.getSimpleName());
            log.error(logStr);
            return JsonResult.error(logStr);
        }
        CrossParam crossParam = crossParams.get(0);

        //获取基础数据项
        controlerInfos.add(
                P1049Entity.builder().type(CrossParam.class.getSimpleName())
                        .no(DEFAULT_ID)
                        .subJuncNo(subJuncNo)
                        .ip(addressIp)
                        .data(JSONObject.toJSONString(crossParam)).build());

        PlanNoList planNoList = crossParam.getPlanNoList();

        if (planNoList == null) {
            crossPraJR.setSuccess(false);
            crossPraJR.setMessage("异常的方案数据" + lesCrossingID);
            return crossPraJR;
        }

        //方案参数-QF查询数据的时候，需要分开数据查询
        List<String> planNos = planNoList.getPlanNo();
        if (planNos == null) {
            crossPraJR.setSuccess(false);
            crossPraJR.setMessage("异常的方案数据" + lesCrossingID);
            return crossPraJR;
        }

        //信号灯组参数
        List<LampGroupParam> lampGroupParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, LampGroupParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<LampGroupParam> planParamList = (List<LampGroupParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        lampGroupParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                lampGroupParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(LampGroupParam.class.getSimpleName())
                                                    .no(getNoFromObject(lampGroupParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(lampGroupParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //车道参数
        List<LaneParam> laneParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, LaneParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<LaneParam> planParamList = (List<LaneParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        laneParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                laneParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(LaneParam.class.getSimpleName())
                                                    .no(getNoFromObject(laneParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(laneParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //人行车道参数
        List<PedestrianParam> pedestrianParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, PedestrianParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<PedestrianParam> planParamList = (List<PedestrianParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        pedestrianParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                pedestrianParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(PedestrianParam.class.getSimpleName())
                                                    .no(getNoFromObject(pedestrianParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(pedestrianParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //信号组参数
        List<SignalGroupParam> signalGroupParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, SignalGroupParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<SignalGroupParam> planParamList = (List<SignalGroupParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        signalGroupParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                signalGroupParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(SignalGroupParam.class.getSimpleName())
                                                    .no(getNoFromObject(signalGroupParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(signalGroupParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //阶段参数
        List<StageParam> stageParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, StageParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<StageParam> planParamList = (List<StageParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        stageParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                stageParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(StageParam.class.getSimpleName())
                                                    .no(getNoFromObject(stageParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(stageParam)).build());
                                }
                        );
                    }
                }
            }
        }


        //获取方案数据项
        List<PlanParam> planParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, PlanParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<PlanParam> planParamList = (List<PlanParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        planParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                planParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(PlanParam.class.getSimpleName())
                                                    .no(getNoFromObject(planParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(planParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //获取日计划数据项
        List<DayPlanParam> dayPlanParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, DayPlanParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<DayPlanParam> planParamList = (List<DayPlanParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        dayPlanParams.addAll(planParamList);


                        //数据存储
                        planParamList.forEach(
                                dayPlanParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(DayPlanParam.class.getSimpleName())
                                                    .no(getNoFromObject(dayPlanParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(dayPlanParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //获取调度计划数据项
        List<ScheduleParam> scheduleParams = new ArrayList<>();
        {
            JsonResult planParamR = getTSCCmd(crossingId, ScheduleParam.class, "0", signalBrandPort, addressIp);
            if (planParamR.isSuccess()) {
                //获取数据项
                List<ScheduleParam> planParamList = (List<ScheduleParam>) planParamR.getData();
                if (planParamList != null && !planParamList.isEmpty()) {
                    {
                        scheduleParams.addAll(planParamList);

                        //数据存储
                        planParamList.forEach(
                                scheduleParam -> {
                                    controlerInfos.add(
                                            P1049Entity.builder().type(ScheduleParam.class.getSimpleName())
                                                    .no(getNoFromObject(scheduleParam, DEFAULT_ID))
                                                    .subJuncNo(subJuncNo)
                                                    .ip(addressIp)
                                                    .data(JSONObject.toJSONString(scheduleParam)).build());
                                }
                        );
                    }
                }
            }
        }

        //路口状态
        JsonResult crossStateJR = getTSCCmd(crossingId, CrossState.class,  signalBrandPort, addressIp);
        if (!crossStateJR.isSuccess()) {
            log.error("获取CrossState异常-{}", crossStateJR);
        } else {
            List<CrossState> crossStates = (List<CrossState>) (crossStateJR.getData());

            if (crossStates != null && !crossStates.isEmpty()) {
                //获取基础数据项
                controlerInfos.add(
                        P1049Entity.builder().type(CrossState.class.getSimpleName())
                                .no(DEFAULT_ID)
                                .subJuncNo(subJuncNo)
                                .ip(addressIp)
                                .data(JSONObject.toJSONString(crossStates.get(0))).build());
                {
                    //发送状态用于数据状态更新,需要将crossId修改为LesSignalId
                    try {
                        CrossState crossState = (CrossState) BeanUtils.cloneBean(crossStates.get(0));
                        crossState.setCrossID(lesCrossingID);
                        crossState.setManual(true);
                        messagePublisher.publishMessage(crossState);

                        if (P1049Utils.getLinkStatus(crossState.getValue()) == LinkStatus.HANDSHAKE) {
                            //针对在线的获取当前运行方案
                            //运行方案
                            {
                                JsonResult crossPlanJR = getTSCCmd(crossingId, CrossCtrlInfo.class, signalBrandPort, addressIp);
                                if (!crossPlanJR.isSuccess()) {
                                    log.error("获取CrossCtrlInfo异常-{}", crossPlanJR);
                                } else {

                                    List<CrossCtrlInfo> crossPlans = (List<CrossCtrlInfo>) (crossPlanJR.getData());

                                    if (crossPlans != null && !crossPlans.isEmpty()) {
                                        //获取基础数据项
                                        controlerInfos.add(
                                                P1049Entity.builder().type(CrossCtrlInfo.class.getSimpleName())
                                                        .no(DEFAULT_ID)
                                                        .subJuncNo(subJuncNo)
                                                        .ip(addressIp)
                                                        .data(JSONObject.toJSONString(crossPlans.get(0))).build());

                                        {
                                            //发送状态用于数据状态更新,需要将crossId修改为LesSignalId
                                            try {
                                                CrossCtrlInfo crossPlan = (CrossCtrlInfo) BeanUtils.cloneBean((crossPlans.get(0)));
                                                crossPlan.setCrossID(lesCrossingID);
                                                messagePublisher.publishMessage(crossPlan);
                                            } catch (Exception e) {
                                                log.error("拷贝对象CrossState出现异常");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("拷贝对象CrossState出现异常");
                    }
                }
            }
        }

        //发布数据项进行异步数据保存
        P1049Infos p1049Infos = P1049Infos.builder().signalId(
                lesCrossingID
        ).controlerInfos(controlerInfos).build();
        messagePublisher.publishMessage(p1049Infos);

        return new JsonResult(true, "获取路口" + lesCrossingID + "数据成功", controlerInfos);
    }


    /**
     * 当登录成功之后，请求所有的基础数据项
     *
     * @param signalBrandPort
     * @return
     */
    public void reSubscribe(SignalBrandPort signalBrandPort, String address) {
        log.error("获取基础数据正常，准备获取信号机数据项");
        List<CrossingService.CrossingBaseInfo> crossingBaseInfos = dataLoadService.getCrossings(signalBrandPort, address);
        log.error("!!!!!!!!!!!!! 开始订阅数据类型-{}信号机数据项-ip地址-{}", signalBrandPort.name(), address);
        crossingBaseInfos.parallelStream().forEach(
                crossingBaseInfo -> {
                    log.error("准备订阅数据项-{}", crossingBaseInfo.getCrossingId());
                    {
                        List<String> crossingIds = new ArrayList<>();
                        crossingIds.add(crossingBaseInfo.getCrossingId());
                        reportSetting(crossingIds, "CrossStage", true, address);
                        reportSetting(crossingIds, "CrossCycle", true, address);

                        if (signalBrandPort.brandCode() == SignalBrandPort.QF.brandCode()) {
                            reportSetting(crossingIds, "CrossState", true, address);
                            reportSetting(crossingIds, "CrossPhaseLampStatus", true, address);
                            reportSetting(crossingIds, "CrossControlMode", true, address);
                            reportSetting(crossingIds, "CrossPlan", true, address);
                        }

                        //针对大华系统 路口方案、路口灯态、路口状态、路口控制模式均需要订阅
                        if(signalBrandPort.brandCode() == SignalBrandPort.DH.brandCode()){
                            reportSetting(crossingIds, "CrossPlan ", true, address);
                            reportSetting(crossingIds, "CrossPhaseLampStatus", true, address);
                            reportSetting(crossingIds, "CrossState", true, address);
                            reportSetting(crossingIds, "CrossControlMode", true, address);
                        }
                        //海康订阅数据项
                        if(signalBrandPort.brandCode() == SignalBrandPort.HK.brandCode()){
                            reportSetting(crossingIds, "CrossControlMode", true, address);
                            reportSetting(crossingIds, "CrossPlan ", true, address);
                            reportSetting(crossingIds, "CrossState", true, address);
                        }

                        //上海富骏码
                        if (signalBrandPort.brandCode() == SignalBrandPort.FJ.brandCode()) {
                            reportSetting(crossingIds, "CrossPhaseLampStatus", true, address);
                        }

                    }
                }
        );
        log.error("!!!!!!!!!!!!! 结束订阅数据类型-{}信号机数据项", signalBrandPort.name());
    }

    /**
     * 当登录成功之后，请求所有的基础数据项
     *
     * @param signalBrandPort
     * @return
     */
    public JsonResult<?> loadDataWhenLoginSuccess(SignalBrandPort signalBrandPort, String address) {

        //准备请求数据项
        JsonResult<?> basicDataResult = loadData(signalBrandPort, address);
        if (basicDataResult.isSuccess()) {
            log.error("获取基础数据正常，准备获取信号机数据项");
            List<CrossingService.CrossingBaseInfo> crossingBaseInfos = dataLoadService.getCrossings(signalBrandPort, address);
            log.error("!!!!!!!!!!!!! 开始获取类型-{}信号机数据项-ip-{}", signalBrandPort.name(), address);
            crossingBaseInfos.parallelStream().forEach(
                    crossingBaseInfo -> {
                        log.error("准备获取信号机数据项-{}", crossingBaseInfo.getCrossingId());
                        try {
                            loadData(crossingBaseInfo);
                        } catch (Exception e) {
                            log.error("获取数据异常", e);
                        }
                        log.error("准备订阅数据项");
                        {
                            List<String> crossingIds = new ArrayList<>();
                            crossingIds.add(crossingBaseInfo.getCrossingId());
                            reportSetting(crossingIds, CrossStage.class.getSimpleName(), true, address);
                            reportSetting(crossingIds, CrossCycle.class.getSimpleName(), true, address);
                        }
                    }
            );
            log.error("!!!!!!!!!!!!! 结束获取类型-{}信号机数据项", signalBrandPort.name());
        } else {
            log.error("获取基础数据异常-{}", basicDataResult);
            return basicDataResult;
        }
        return basicDataResult;
    }


    /**
     * 调看系统参数
     *
     * @param signalBrandPort
     * @param type
     * @param id
     * @param no
     * @param address
     * @return
     */
    public JsonResult loadSystemData(SignalBrandPort signalBrandPort, String type, String id, String no, String address) {

        Optional<Class> optionalClass = avaliableDataClazz_System.stream().filter(clazz -> type.equalsIgnoreCase(clazz.getSimpleName())).findAny();

        if (!optionalClass.isPresent()) {
            return JsonResult.error("尚不支持请求数据项,支持的是 " + avaliableDataClazz_System.stream().map(clazz -> clazz.getSimpleName()).collect(Collectors.joining(",")));
        }

        JsonResult dataWithIdResult = loadDataWithId(signalBrandPort, id, optionalClass.get().getSimpleName(),
                no, 0, address);
        return dataWithIdResult;
    }


    /**
     * 获取基础路口信息数据项
     *
     * @return
     */
    public JsonResult<?> loadData(SignalBrandPort signalBrandPort, String address) {
        log.error("****** 开始加载数据 start loadData -- {} of 品牌-{} IP地址-{}", LocalDateTime.now(), signalBrandPort, address);

        //获取SysInfo数据项
        List<SysInfo> sysInfos = new ArrayList<>();
        {
            JsonResult dataWithIdResult = loadDataWithId(signalBrandPort, "", SysInfo.class.getSimpleName(),
                    DEFAULT_ID, 0, address);
            if (dataWithIdResult.isSuccess()) {
                sysInfos.addAll((List<SysInfo>) dataWithIdResult.getData());
            } else {
                log.error("获取区域数据异常-{}", dataWithIdResult);
                return JsonResult.error("获取异常区域数据");
            }
        }

        if(sysInfos.isEmpty()){
            return JsonResult.error("获取区域数据为空");
        }

        final List<String> logs = Collections.synchronizedList(new ArrayList<>());

        final List<RegionParam> regionParams = Collections.synchronizedList(new ArrayList<>());

        //根据区域编号查询区域参数
        {

             {
                //暂时使用stream顺序查询数据项
                 {
                        JsonResult dataWithIdResult = loadDataWithId(signalBrandPort, "", RegionParam.class.getSimpleName(),
                                DEFAULT_ID, 0, address);
                        if (dataWithIdResult.isSuccess()) {
                            regionParams.addAll((List<RegionParam>) dataWithIdResult.getData());
                        } else {
                            log.error("获取区域数据异常-{}", dataWithIdResult);
                            logs.add("获取区域数据异常" + dataWithIdResult);
                        }
                 }

                log.error("获取数据项-{}", regionParams);
            }
        }

        //查询信号机参数
        final List<SignalController> signalControlers = Collections.synchronizedList(new ArrayList<>());
        {
            //暂时使用stream顺序查询数据项
             {

                        //测试的时候只请求测试的1049数据
                        String signalControllerId = "";
                        if(testSignalConfigure.isUseTest()){
                            signalControllerId = testSignalConfigure.getSignalId_1049();
                        }

                        JsonResult dataWithIdResult = loadDataWithId(signalBrandPort, signalControllerId,
                                SignalController.class.getSimpleName(),
                                DEFAULT_ID, 0, address);
                        if (dataWithIdResult.isSuccess()) {
                            signalControlers.addAll((List<SignalController>) dataWithIdResult.getData());
                        } else {
                            log.error("获取信号机数据异常-{}", dataWithIdResult);
                            logs.add("获取信号机" + signalControllerId + "数据异常" + dataWithIdResult);
                        }
             }
            log.error("获取数据项-{}", signalControlers);
        }


        //查询路口数据项
        final List<CrossParam> crossParams = Collections.synchronizedList(new ArrayList<>());
        //暂时使用stream顺序查询数据项
        {

                     String crossId = "0";
                    //测试的时候只请求测试的1049数据
                    if(testSignalConfigure.isUseTest()){
                        if( ! testSignalConfigure.getCrossingIds_1049().contains(crossId)){
                            crossId = testSignalConfigure.getCrossingIds_1049().get(0);
                        }
                    }

                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossId);
                    JsonResult dataWithIdResult = loadDataWithId(signalBrandPort, crossId,
                            CrossParam.class.getSimpleName(), DEFAULT_ID,
                            (crossingInfoOp.isPresent() ? crossingInfoOp.get().getSubJuncNo() : 1),
                            address);
                    if (dataWithIdResult.isSuccess()) {
                        crossParams.addAll((List<CrossParam>) dataWithIdResult.getData());
                    } else {
                        log.error("获路口数据异常-{}", dataWithIdResult);
                        logs.add("获路口" + crossId + "数据异常" + dataWithIdResult);
                    }
        }

        HashMap<String, Object> dataResult = new HashMap<>();
        dataResult.put("CrossParam", crossParams);
        dataResult.put("SignalController", signalControlers);
        dataResult.put("RegionParam", regionParams);
        dataResult.put("SysInfo", sysInfos.get(0));
        dataResult.put("logs", logs);

        return new JsonResult(true, "获取信号机数据成功", dataResult);

    }


    /**
     * 指定阶段
     *
     * @return
     */
    public JsonResult lockStage(String crossId, int subJuncNo, String stageNo, int stageLength, boolean lock) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossId);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的路口-[" + crossId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        if (lock) {
            //内存中保留最新的一条数据项
            ht1049SignalCacheService.updateLockStage(crossId, stageNo);
        } else {
            ht1049SignalCacheService.rmLockStage(crossId);
        }

        if (stageLength > 250 || stageLength < 0) {
            return new JsonResult(false, "锁定相位时间太长了或者异常:" + stageLength, "");
        }

        ConcurrentHashMap<String, Map<String, P1049Entity>> signalInfoMap = ht1049SignalCacheService.getSignalInfoMap();
        if (!signalInfoMap.containsKey(crossId)) {
            return new JsonResult(false, "没有找到路口" + crossId, "");
        }

        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalBaseInfoOp.get().getSignalId());

        return JsonResult.error("不支持的信号机型号-{}", crossingInfoOp.get().getControllerId());
    }


    /**
     * 锁定流向
     *
     * @return
     */
    public JsonResult lockFlowDirection(String signalId, int subJuncNo, int direction, int outDirection, int lockTime, boolean lock) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }


        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);

        if (signalBrandPort == SignalBrandPort.QF) {
            MessageType messageType = MessageType.REQUEST;
            OperationName operationName = OperationName.Set;
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            if (lock) {
                LockFlowDirection lockFlowDirection = LockFlowDirection.builder()
                        .CrossID(p1049CrossingId)
                        .Type("1")
                        .Entrance(String.valueOf(direction))
                        .Exit(String.valueOf(outDirection))
                        .Duration(String.valueOf(lockTime)).build();

                List<LockFlowDirection> lockFlowDirections = new ArrayList<>();
                lockFlowDirections.add(lockFlowDirection);

                JsonResult jsonResult = messageSendProcess.processSend(messageType, operationName, lockFlowDirections, false,
                        SignalBrandPort.getType(signalId), LockFlowDirection.class, crossingInfoOp.get().getAddress1049Ip());
                return jsonResult;
            } else {
                UnLockFlowDirection unlockFlowDirection = UnLockFlowDirection.builder()
                        .CrossID(p1049CrossingId)
                        .Type("1")
                        .Entrance(String.valueOf(direction))
                        .Exit(String.valueOf(outDirection)).build();

                List<UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
                unLockFlowDirections.add(unlockFlowDirection);

                JsonResult jsonResult = messageSendProcess.processSend(messageType, operationName, unLockFlowDirections, false,
                        SignalBrandPort.getType(signalId), UnLockFlowDirection.class, crossingInfoOp.get().getAddress1049Ip());
                return jsonResult;
            }
        }

        return JsonResult.error("不支持的信号机型号-{}", signalId);
    }

    /**
     * 设置信号机控制方式
     *
     * @param signalId
     * @param lesMode
     * @return
     */
    public JsonResult<?> setSignalMode(String signalId, int subJuncNo, int lesMode) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);

        String p1049Mode = Utils.change21049Mode(String.valueOf(lesMode));

        //恢复控制
        if (CentralUtils.DEFAULT_OFFLINE_MODE.equals(p1049Mode)) {
            String lockStageNo = ht1049SignalCacheService.getLockStageNo(signalId);
            // return lockStageNo(signalId, subJuncNo, lockStageNo, 0, false);
        }

        //指定控制方式
        ConcurrentHashMap<String, Map<String, P1049Entity>> signalInfoMap = ht1049SignalCacheService.getSignalInfoMap();

        if (!signalInfoMap.containsKey(signalId)) {
            return new JsonResult<>(false, "没有找到信号机" + signalId, null);
        }

        MessageType messageType = MessageType.REQUEST;
        OperationName operationName = OperationName.Set;

        CrossControlMode crossControlMode = CrossControlMode.builder()
                .build();

        JsonResult<?> jsonResult = messageSendProcess.processSend(messageType, operationName, crossControlMode, false,
                signalBrandPort, CrossControlMode.class, crossingInfoOp.get().getAddress1049Ip());
        return jsonResult;
    }


    /**
     * 设置信号机控制方式
     *
     * @param signalId
     * @param planNo
     * @return
     */
    public JsonResult<?> setPlanNo(String signalId, int subJuncNo, int planNo) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);


        MessageType messageType = MessageType.REQUEST;
        OperationName operationName = OperationName.Set;

        CrossPlan crossPlan = CrossPlan.builder()
                .build();
        List<CrossPlan> crossPlans = new ArrayList<>();
        crossPlans.add(crossPlan);

        JsonResult<?> jsonResult = messageSendProcess.processSend(messageType, operationName, crossPlans, false,
                signalBrandPort, CrossPlan.class, crossingInfoOp.get().getAddress1049Ip());
        return jsonResult;
    }

    /**
     * 发送异常的时候进行发送
     *
     * @param signalBaseInfo
     * @param subJuncNo
     * @param msg
     */
    public void notifyHoloAck(ControllerService.SignalBaseInfo signalBaseInfo, int subJuncNo, String msg) {
        SystemControlAck systemControlAck = SystemControlAck.builder().signalControllerID(signalBaseInfo.getSignalId())
                .crossingSeqNo(subJuncNo)
                .ack(2)
                .iden(0)
                .type(7)
                .msg(msg)
                .build();

        List<Object> objects = new ArrayList<>();
        objects.add(systemControlAck);

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("111")
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(MqMsgOperator.MqMsgOperator_Set.value())
                .sequenceCode(111)
                .objectId(SystemControlAck.MqObjectId)
                .signalControllerID(signalBaseInfo.getSignalId())
                .errorCode("0")
                .errorInfo("")
                .objectList(objects)
                .build();

        //发送MQ数据
        messageSender.send(SystemControlAck.class.getSimpleName().toLowerCase(),
                mqMessageResponse);
    }


    public JsonResult<?> reportSetting(List<String> crossingIds, String type, boolean start) {
        if (crossingIds == null || crossingIds.isEmpty()) {
            return new JsonResult<>(false, "没有设置路口");
        }

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingIds.get(0));
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult<>(false, "没有找到路口" + crossingIds.get(0));
        }

        return reportSetting(crossingIds, type, start, crossingInfoOp.get().getAddress1049Ip());
    }

    /**
     * 订阅以及取消
     *
     * @param crossingIds
     * @param type
     * @param start
     * @return
     */
    public JsonResult<?> reportSetting(List<String> crossingIds, String type, boolean start, String address) {
        if (crossingIds == null || crossingIds.isEmpty()) {
            return new JsonResult<>(false, "没有设置路口");
        }
        SignalBrandPort signalBrandPort = null;
        List<String> crossIds1049 = new ArrayList<>();
        for (int i = 0; i < crossingIds.size(); i++) {
            String crossingId = crossingIds.get(i);
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
            if (!crossingInfoOp.isPresent()) {
                continue;
            }
            //原始数据项
            String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
            if (signalBrandPort == null) {
                signalBrandPort = SignalBrandPort.getType(crossingInfoOp.get().getControllerId());
            }
            crossIds1049.add(p1049CrossingId);
        }

        MessageType messageType = MessageType.REQUEST;
        OperationName operationName = OperationName.Set;

        CrossIDList crossIDList = new CrossIDList();
        crossIDList.setCrossID(crossIds1049);

        List<CrossReportCtrl> crossReportCtrls = new ArrayList<>();
        CrossReportCtrl crossReportCtrl = CrossReportCtrl.builder()
                .Cmd(start ? "Start" : "Stop")
                .Type(type)
                .CrossIDList(crossIDList).build();
        crossReportCtrls.add(crossReportCtrl);

        JsonResult<?> jsonResult = messageSendProcess.processSend(messageType, operationName,
                crossReportCtrls, false,
                signalBrandPort, CrossReportCtrl.class, address);
        return jsonResult;
    }


    /**
     * 设置信号机莱斯周期优化接口
     *
     * @param signalId
     * @return
     */
    public JsonResult<?> setStageCtrlList(String signalId, int subJuncNo, StageCtrlList stageCtrlList) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);


        MessageType messageType = MessageType.REQUEST;
        OperationName operationName = OperationName.Set;

        stageCtrlList.setCrossID(p1049CrossingId);

        JsonResult<?> jsonResult = messageSendProcess.processSend(messageType, operationName, stageCtrlList, true,
                signalBrandPort, StageCtrlList.class, crossingInfoOp.get().getAddress1049Ip());
        return jsonResult;
    }

    /**
     * 设置信号机莱斯周期优化接口
     *
     * @param signalId
     * @return
     */
    public JsonResult<?> setStageCtrl(String signalId, int subJuncNo, StageCtrl stageCtrl) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId, subJuncNo);
        if (!crossingInfoOp.isPresent()) {
            return new JsonResult(false, "未找到路口,异常的信号机-[" + signalId + "],子路口号-[" + subJuncNo + "]");
        }

        Optional<ControllerService.SignalBaseInfo> signalBaseInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if (!signalBaseInfoOp.isPresent()) {
            return new JsonResult(false, "未找到信号机-[" + crossingInfoOp.get().getControllerId() + "]");
        }

        //原始数据项
        String p1049CrossingId = crossingInfoOp.get().getCrossingId1049();
        SignalBrandPort signalBrandPort = SignalBrandPort.getType(signalId);


        MessageType messageType = MessageType.REQUEST;
        OperationName operationName = OperationName.Set;

        stageCtrl.setCrossID(p1049CrossingId);

        JsonResult<?> jsonResult = messageSendProcess.processSend(messageType, operationName, stageCtrl, true,
                signalBrandPort, StageCtrl.class, crossingInfoOp.get().getAddress1049Ip());
        return jsonResult;
    }

}
