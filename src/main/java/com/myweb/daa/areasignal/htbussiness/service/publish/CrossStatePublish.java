package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.business.service.DataLoadService;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossState;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class CrossStatePublish {
    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private DataLoadService dataLoadService;

    @Autowired
    private HtSignalController htSignalController;


    @Autowired
    private CrossControlModePublish crossControlModePublish;

    /**
     * 重连随机数
     */
    private static Random nextRandomTime = new Random(System.currentTimeMillis());


    /**
     * 防止出现大规模数据同时请求
     * 重连间隔在1 - 60 随机
     *
     * @return
     */
    private int getRandomStatusTime() {
        return (1 + nextRandomTime.nextInt(59)) * 1000;
    }

    @Value("#{'${global.checkStatus:false}'}")
    private boolean checkStatus;

    @Data
    public static class CrossStateCheck {
        private String crossId;
    }

    @Scheduled(initialDelay = 60000, fixedRate = 60000)
    public void checkCrossState() {
        if (!checkStatus) {
            return;
        }

        List<CrossingService.CrossingBaseInfo> crossingBaseInfos = dataLoadService.getCrossings(SignalBrandPort.QF);
        crossingBaseInfos.parallelStream().forEach(
                crossingBaseInfo -> {
                    log.error("准备检查路口状态-{}", crossingBaseInfo);
                    {

                        CrossStateCheck crossStateCheck = new CrossStateCheck();
                        crossStateCheck.setCrossId(crossingBaseInfo.getCrossingId());
                        messagePublisher.publishDelayMessage(getRandomStatusTime(), crossStateCheck);
                    }
                }
        );
    }

    @EventListener
    @Async(GlobalConfigure.CROSS_STATE_PROCESS_EXECUTOR)
    public void processCrossStateCheck(CrossStateCheck crossStateCheck) {

        if (crossStateCheck == null || crossStateCheck.getCrossId() == null) {
            return;
        }

        JsonResult crossStateJr = htSignalController.loadCrossData(crossStateCheck.getCrossId(), "CrossState", "0");
        if (crossStateJr.isSuccess()) {
            Object data = crossStateJr.getData();
            if (data != null && (data instanceof List)) {
                List<Object> datas = (List<Object>) data;
                if (datas != null && !datas.isEmpty()
                        && datas.get(0) instanceof CrossState) {
                    CrossState crossState = (CrossState) (datas.get(0));
                    crossState.setCrossID(crossStateCheck.getCrossId());
                    messagePublisher.publishMessage(crossState);
                }
            }
        }
    }


    /**
     * 存储路口最新的控制状态数据项
     */
    private Map<String, CrossState> crossStateMap = new ConcurrentHashMap<>();

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processCrossState(CrossState crossState) {
        log.debug("收到路口状态-{}", crossState);
        String crossID = crossState.getCrossID();

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        //检查路口状态是否发生变化
        boolean needNotify = false;
        CrossState crossStateOld = crossStateMap.get(crossingInfoOp.get().getCrossingId());
        //第一次获取路口状态
        if (crossStateOld == null) {
            needNotify = true;
        } else {
            String valueNew = crossState.getValue();
            String valueOld = crossStateOld.getValue();
            //如果数据均不为null
            if (valueNew != null && valueOld != null) {
                needNotify = !(valueNew.equalsIgnoreCase(valueOld));
            } else if (valueNew != null) {
                needNotify = true;
            }
        }

        if (crossState.isManual()) {
            needNotify = true;
        }

        if(!needNotify){
            //检查是否存在控制方式，用于修复同时发送 CrossState 以及 CrossCtrlInfo
            if("Offline".equalsIgnoreCase(crossState.getValue())){
                boolean isExist = crossControlModePublish.isCrossControlModeExist(crossID);
                if(isExist){
                   needNotify = true;
                }
            }
        }

        //控制状态数据发送
        if (needNotify) {
            com.myweb.daa.areasignal.centralsystem.param.CrossState crossStateCentral =
                    com.myweb.daa.areasignal.centralsystem.param.CrossState.builder()
                            .noArea(crossingInfoOp.get().getNoArea())
                            .noJunc(crossingInfoOp.get().getNoJunc())
                            .signalControllerID(crossingInfoOp.get().getControllerId())
                            .juncStatus("Offline".equalsIgnoreCase(crossState.getValue()) ? 2 : 1).build();
            List<Object> objectList = new ArrayList<>();
            objectList.add(crossStateCentral);
            MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfoOp.get().getControllerId(),
                    com.myweb.daa.areasignal.centralsystem.param.CrossState.MqObjectId, objectList);
            messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossState.class.getSimpleName().toLowerCase(), mqMessage);
            messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossStateCentral)
                    .build());
        }

        //如果信号机离线，设置控制状态为20
        if (("Offline".equalsIgnoreCase(crossState.getValue())) && needNotify) {
            com.myweb.daa.areasignal.centralsystem.param.CrossControlMode controlMode =
                    com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.builder()
                            .signalControllerID(crossingInfoOp.get().getControllerId())
                            .noArea(crossingInfoOp.get().getNoArea())
                            .noJunc(crossingInfoOp.get().getNoJunc())
                            .mode(LesControlMode.OFFLINE.value())
                            .crossingSeqNo(crossingInfoOp.get().getSubJuncNo()).build();

            List<Object> objectList = new ArrayList<>();
            objectList.add(controlMode);
            MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfoOp.get().getControllerId(),
                    com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.MqObjectId, objectList);
            messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.class.getSimpleName().toLowerCase(), mqMessage);
            messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(controlMode)
                    .build());

            /**
             * 通知清除旧控制方式20240626
             */
            CrossControlModePublish.CrossOffline crossOffline = new CrossControlModePublish.CrossOffline();
            crossOffline.setCrossId(crossID);
            messagePublisher.publishMessage(crossOffline);

        } else if (("Online".equalsIgnoreCase(crossState.getValue())) && needNotify) {
            //如果信号机重新上线，发送当前控制方式
            CrossControlModePublish.CrossControlModeCheck crossControlModeCheck
                    = new CrossControlModePublish.CrossControlModeCheck();
            crossControlModeCheck.setCrossId(crossID);
            messagePublisher.publishMessage(crossControlModeCheck);
        }

        crossStateMap.put(crossID, crossState);

    }
}
