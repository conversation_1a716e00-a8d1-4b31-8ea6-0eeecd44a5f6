package com.myweb.daa.areasignal.htbussiness.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.les.ads.ds.enums.DefaultMovementType;
import com.les.ads.ds.enums.DefaultPhase;
import com.les.ads.ds.enums.DirectionType;
import com.les.ads.ds.enums.LaneMovementType;
import com.myweb.commons.dao.RepositoryDao;
import com.myweb.daa.areasignal.centralsystem.param.v2.DirectionType39900;
import com.myweb.daa.areasignal.centralsystem.param.v2.LampGroupParamType;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Entity;
import com.myweb.daa.areasignal.htbussiness.bean.P1049Infos;
import com.myweb.daa.areasignal.htbussiness.bean.QP1049Entity;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import com.myweb.daa.areasignal.utils.ConstValue;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
public class Ht1049SignalCacheService {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private P1049Configure p1049Configure;
    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private CrossingService crossingService;

    /**
     * 1049信号机数据项
     */
    private ConcurrentHashMap<String, Map<String, P1049Entity>> signalInfoMap = new ConcurrentHashMap<>();


    /**
     * 从1049的阶段转换到中心机的阶段
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, String>> p1049Stage2CentralStageMap = new ConcurrentHashMap<>();

    /**
     * 记录锁定的状态数据项
     */
    private ConcurrentHashMap<String, LockSatgeInfo> p1049LockInfo = new ConcurrentHashMap<>();


    /**
     * 从数据库中加载基础数据项
     */
    public void loadDataFromDb() {

        StopWatch stopWatch = new StopWatch("读取1049原始数据");

        stopWatch.start("数据库读取数据");
        //读取数据项
        List<P1049Entity> p1049Entities = queryFactory.selectFrom(QP1049Entity.p1049Entity).fetch();
        stopWatch.stop();

        log.error("从数据库读取{}条数据花费时间——{}", p1049Entities.size(), stopWatch.prettyPrint());


        stopWatch.start("装载到内存");
        AtomicInteger dataCount = new AtomicInteger(0);
        //保存到当前内存中
        p1049Entities.forEach(
                p1049Entity -> {
                    Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(p1049Entity.getSignalId());
                    //此信号机的第一条数据项
                    if (stringP1049EntityMap == null) {
                        stringP1049EntityMap = new ConcurrentHashMap<>();
                        signalInfoMap.put(p1049Entity.getSignalId(), stringP1049EntityMap);
                    }

                    if (p1049Entity.getIp() == null || p1049Entity.getIp().isEmpty()) {
                        //临时设置数据项
                        p1049Entity.setIp("***************");
                    }

                    stringP1049EntityMap.put(p1049Entity.getKey(), p1049Entity);

                    int count = dataCount.getAndIncrement();
                    if (count % 5000 == 0) {
                        log.error("加载内存数据-{}", count);
                    }
                }
        );
        log.error("加载内存数据-{}", dataCount.get());
        log.error("加载到内存花费时间——{}", stopWatch.prettyPrint());

        //生成莱斯中心机映射数据项，根据1049阶段号映射到中心机阶段号
        signalInfoMap.keySet().forEach(
                crossId -> {
                    Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossId);
                    if (crossingInfoOp.isPresent()) {
                        genCentralSystemStage(crossingInfoOp.get());
                    }
                }
        );
    }


    /**
     * 调看信号机数据项时候更新本地缓存
     *
     * @param p1049Infos
     */
    public void updateData(P1049Infos p1049Infos) {

        String signalId = (p1049Infos.getSignalId());

        //查询是否已经有数据项
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
        if (stringP1049EntityMap == null) {
            stringP1049EntityMap = new ConcurrentHashMap<>();
            signalInfoMap.put(p1049Infos.getSignalId(), stringP1049EntityMap);
        }

        //更新数据项
        final Map<String, P1049Entity> stringP1049EntityMapData = stringP1049EntityMap;
        AtomicBoolean containsStageParam = new AtomicBoolean(false);
        p1049Infos.getControlerInfos().forEach(
                p1049Entity -> {
                    stringP1049EntityMapData.put(p1049Entity.getKey(), p1049Entity);

                    if (StageParam.class.getSimpleName().compareToIgnoreCase(p1049Entity.getType()) == 0) {
                        containsStageParam.set(true);
                    }
                }
        );

        //针对包含阶段参数时，生成莱斯中心机映射数据项
        if (containsStageParam.get()) {
            //数据项存储时大部分为信号机数据项
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(signalId);
            if (crossingInfoOp.isPresent()) {
                genCentralSystemStage(crossingInfoOp.get());
            }
        }
    }

    /**
     * 移除特定的数据项
     *
     * @param signalId
     * @param subJuncNo
     * @param type
     * @param no
     */
    public void deleteData(String signalId, int subJuncNo, String type, String no) {
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
        if (null == stringP1049EntityMap) {
            return;
        }

        stringP1049EntityMap.remove(P1049Entity.get1049DataKey(signalId, subJuncNo, type, no));
    }

    /**
     * 移除特定的数据项
     *
     * @param signalId
     * @param subJuncNo
     * @param type
     */
    public void deleteData(String signalId, int subJuncNo, String type) {
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
        if (null == stringP1049EntityMap) {
            return;
        }

        String dataKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, type);
        List<String> keys = stringP1049EntityMap.keySet().stream().filter(
                key -> key.startsWith(dataKeyPrefix)
        ).collect(Collectors.toList());

        keys.stream().forEach(
                key -> stringP1049EntityMap.remove(key)
        );
    }


    /**
     * 更新一条数据项
     *
     * @param p1049Entity
     */
    public void updateData(P1049Entity p1049Entity) {
        //查询是否已经有数据项
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(p1049Entity.getSignalId());
        if (stringP1049EntityMap == null) {
            stringP1049EntityMap = new ConcurrentHashMap<>();
            signalInfoMap.put(p1049Entity.getSignalId(), stringP1049EntityMap);
        }
        //log.info("updateData key-{}info-{}", p1049Entity.getKey(), p1049Entity);
        stringP1049EntityMap.put(p1049Entity.getKey(), p1049Entity);
    }

    /**
     * 从缓存中获取一种数据项，指定no
     *
     * @param signalId
     * @param no
     * @param <T>
     * @return
     */
    public <T> Optional<T> getData(String signalId, int subJuncNo, String no, Class clazz) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = P1049Entity.get1049DataKey(signalId, subJuncNo, clazz.getSimpleName(), no);
        if (signalInfo.containsKey(nameKey)) {
            T data = (T) JSONObject.parseObject(signalInfo.get(nameKey).getData(), clazz);
            return Optional.of(data);
        } else {
            return Optional.empty();
        }
    }

    /**
     * 从缓存中获取一类数据项
     *
     * @param signalId
     * @param <T>
     * @return
     */
    public <T> Optional<Map<String, T>> getData(String signalId, int subJuncNo, Class clazz) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        Map<String, T> datas = new HashMap<>();
        String nameKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, clazz.getSimpleName());

        signalInfo.keySet().stream().filter(
                key -> key.startsWith(nameKeyPrefix)
        ).forEach(
                key -> {
                    try {
                        T eachData = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                        datas.put(key.replace(nameKeyPrefix, ""), (eachData));
                    }catch (Exception e){
                        log.error("异常-{}解析类型{}", signalInfo.get(key).getData(), clazz.getSimpleName());
                    }
                }
        );

        if (datas.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(datas);
        }
    }


    /**
     * 从缓存中获取一类所有的数据项
     *
     * @param <T>
     * @return
     */
    public <T> Optional<Map<String, Map<String, T>>> getData(int subJuncNo, Class clazz) {
        Map<String, Map<String, T>> allData = new ConcurrentHashMap<>();
        signalInfoMap.keySet().stream().forEach(
                signalId -> {
                    Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
                    if (signalInfo == null) {
                        return;
                    }

                    Map<String, T> datas = new HashMap<>();
                    String nameKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, clazz.getSimpleName());

                    signalInfo.keySet().stream().filter(
                            key -> key.startsWith(nameKeyPrefix)
                    ).forEach(
                            key -> {
                                T eachData = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                                datas.put(key.replace(nameKeyPrefix, ""), (eachData));
                            }
                    );

                    if (!datas.isEmpty()) {
                        allData.put(signalId, datas);
                    }
                }
        );

        if (allData.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(allData);
        }
    }


    /**
     * 获取信号机所在的系统ip地址
     *
     * @param signalId
     * @return
     */
    public Optional<String> getSignalSystemIp(String signalId) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = P1049Entity.get1049DataKey(signalId, 0, SignalController.class.getSimpleName(), "0");
        if (signalInfo.containsKey(nameKey)) {
            String ip = signalInfo.get(nameKey).getIp();
            return Optional.ofNullable(ip);
        } else {
            return Optional.empty();
        }
    }

    /**
     * 获取信号机所在的系统ip地址
     *
     * @param crossingId
     * @return
     */
    public Optional<String> getCrossSystemIp(String crossingId) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(crossingId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = P1049Entity.get1049DataKey(crossingId, 1, CrossParam.class.getSimpleName(), "0");
        if (signalInfo.containsKey(nameKey)) {
            String ip = signalInfo.get(nameKey).getIp();
            return Optional.ofNullable(ip);
        } else {
            return Optional.empty();
        }
    }

    ////////////////////////用于1049信号机阶段与中心机阶段之间的映射关系

    /**
     * 生成中心机信号的阶段，根据阶段所在的各个方案中的索引
     *
     * @return
     */
    public void genCentralSystemStage(CrossingService.CrossingBaseInfo crossingBaseInfo) {
        String signalId = crossingBaseInfo.getControllerId();
        int subJuncNo = crossingBaseInfo.getSubJuncNo();
        log.debug("@@@@@准备进行信号机-[{}]，子路口-[{}]的阶段参数编号编码", signalId, subJuncNo);
        String transKey = genStageTransKey(signalId, subJuncNo);
        ConcurrentHashMap<String, String> stageMap = p1049Stage2CentralStageMap.get(transKey);
        if (stageMap == null) {
            stageMap = new ConcurrentHashMap<>();
            p1049Stage2CentralStageMap.put(transKey, stageMap);
        } else {
            //清除原先的配置项数据
            stageMap.clear();
        }

        //获取阶段数据项
        Optional<Map<String, StageParam>> stageParamMap = getData(crossingBaseInfo.getCrossingId(), subJuncNo, StageParam.class);
        if (stageParamMap.isPresent()) {
            ArrayList<StageParam> stageParams = Lists.newArrayList(stageParamMap.get().values().iterator());
            if (stageParams != null) {
                stageParams.sort(
                        Comparator.comparingInt(stageParam -> Integer.parseInt(stageParam.getStageNo()))
                );

                //根据排序的数据项生成中心机的阶段编号
                for (int i = 0; i < stageParams.size(); i++) {
                    stageMap.put(stageParams.get(i).getStageNo(), String.valueOf(i + 1));
                }
            }
        }
        log.error("@@@@@信号机-[{}]，子路口-[{}]的阶段参数编号编码为-{}", signalId, subJuncNo, stageMap);
    }

    /**
     * 根据信号机编号以及子路口号生成阶段映射关系关键字key
     *
     * @param signalId
     * @param subJuncNo
     * @return
     */
    public String genStageTransKey(String signalId, int subJuncNo) {
        return signalId + "###" + subJuncNo;
    }

    /**
     * 根据信号机编号以及中心机阶段编号获取1049阶段号
     *
     * @param signalId
     * @param centralStageNo
     * @return
     */
    public Optional<String> getP1049StageNo(String signalId, int subJuncNo, String centralStageNo) {
        ConcurrentHashMap<String, String> stageMap = p1049Stage2CentralStageMap.get(genStageTransKey(signalId, subJuncNo));
        if (stageMap != null) {
            Iterator<Map.Entry<String, String>> entries = stageMap.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<String, String> entry = entries.next();
                String key = entry.getKey();
                String value = entry.getValue();
                if (value.compareToIgnoreCase(centralStageNo) == 0) {
                    return Optional.of(key);
                }
            }

        }
        return Optional.empty();
    }

    /**
     * 获取当前已经编码的所有的中心机阶段编号
     *
     * @param signalId
     * @param subJuncNo
     * @return
     */
    public Optional<List<Integer>> getCentralStageNos(String signalId, int subJuncNo) {
        ConcurrentHashMap<String, String> stageMap = p1049Stage2CentralStageMap.get(genStageTransKey(signalId, subJuncNo));
        if (stageMap != null) {
            List<Integer> stageNos = new ArrayList<>();
            Iterator<Map.Entry<String, String>> entries = stageMap.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<String, String> entry = entries.next();
                String key = entry.getKey();
                String value = entry.getValue();
                stageNos.add(Integer.parseInt(value));
            }
            stageNos.sort(Integer::compareTo);
            return Optional.of(stageNos);
        }
        return Optional.empty();
    }


    /**
     * 根据信号机编号以及中心机阶段编号获取1049阶段号
     *
     * @param signalId
     * @param p1049StageNo
     * @return
     */
    public Optional<String> getCentralStageNo(String signalId, int subJuncNo, String p1049StageNo) {
        ConcurrentHashMap<String, String> stageMap = p1049Stage2CentralStageMap.get(genStageTransKey(signalId, subJuncNo));
        if (stageMap != null) {
            String value = stageMap.get(p1049StageNo);
            if (value != null) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    /**
     * 判定当前是否在系统指定阶段
     *
     * @param signalId
     * @return
     */
    public boolean isInLockStage(String signalId) {
        return p1049LockInfo.containsKey(signalId);
    }

    /**
     * 内存更新当前的锁定数据项
     *
     * @param crossID
     * @param stageNo
     */
    public void updateLockStage(String crossID, String stageNo) {
        LockSatgeInfo lockSatgeInfo = p1049LockInfo.get(crossID);
        if (lockSatgeInfo == null) {
            lockSatgeInfo = LockSatgeInfo.builder().stageNo(stageNo).lockTime(new Date()).build();
            log.error("路口-{}新锁定-{}-内存锁定数据项{}", crossID, stageNo, lockSatgeInfo);
            p1049LockInfo.put(crossID, lockSatgeInfo);
        } else {
            log.error("路口-{}重复锁定-{},原先锁定-{}", crossID, stageNo, lockSatgeInfo);
            lockSatgeInfo.setStageNo(stageNo);
            lockSatgeInfo.setLockTime(new Date());

            //上次取消设置为取消
            if (lockSatgeInfo.unLockFlowEvent != null
                    && lockSatgeInfo.unLockFlowEvent.getEnable() != null) {
                lockSatgeInfo.unLockFlowEvent.getEnable().set(false);
                log.error("路口-{}取消解锁事件,锁定数据项-{}-", crossID, lockSatgeInfo);
            }
        }

    }

    /**
     * 移除锁定的阶段
     *
     * @param crossID
     */
    public void rmLockStage(String crossID) {
        log.error("路口-{}移除锁定阶段事件", crossID);
        LockSatgeInfo lockSatgeInfo = p1049LockInfo.get(crossID);
        if (lockSatgeInfo != null) {
            log.error("路口-{}移除锁定阶段事件-数据项-{}", crossID, lockSatgeInfo);

            //上次取消设置为取消
            if (lockSatgeInfo.unLockFlowEvent != null
                    && lockSatgeInfo.unLockFlowEvent.getEnable() != null) {
                lockSatgeInfo.unLockFlowEvent.getEnable().set(false);
                log.error("路口-{}取消解锁事件,锁定数据项-{}-", crossID, lockSatgeInfo);
            }

        }

        p1049LockInfo.remove(crossID);
    }

    /**
     * 设置当前的取消事件
     *
     * @param crossId
     * @param unLockFlowEvent
     */
    public void setUnLockEvent(String crossId, UnLockFlowDirectionService.UnLockFlowEvent unLockFlowEvent) {
        LockSatgeInfo lockSatgeInfo = p1049LockInfo.get(crossId);
        if (lockSatgeInfo != null) {
            lockSatgeInfo.setUnLockFlowEvent(unLockFlowEvent);
            log.error("路口-{}设置取消解锁事件,锁定数据项-{}-", crossId, lockSatgeInfo);
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LockSatgeInfo {
        private String stageNo;
        private Date lockTime;
        private UnLockFlowDirectionService.UnLockFlowEvent unLockFlowEvent;
    }

    /**
     * 获取当前锁定的阶段
     *
     * @param crossId
     */
    public String getLockStageNo(String crossId) {
        LockSatgeInfo lockSatgeInfo = p1049LockInfo.get(crossId);
        if (lockSatgeInfo == null) {
            return ConstValue.ErrorStageNo;
        } else {
            return lockSatgeInfo.getStageNo();
        }
    }

    /**
     * 转换类型
     * 比如直左，需要转换成 直行 + 左转
     *
     * @param laneMovementType
     * @return
     */
    public List<LaneMovementType> getLaneMovementTypes(LaneMovementType laneMovementType) {
        List<LaneMovementType> laneMovementTypes = new ArrayList<>();
        laneMovementTypes.add(laneMovementType);

        if (laneMovementType.getCode() == LaneMovementType.RF_STRAIGHT_LEFT.getCode()) {
            laneMovementTypes.add(LaneMovementType.RF_STRAIGHT);
            laneMovementTypes.add(LaneMovementType.RF_LEFT);
        } else if (laneMovementType.getCode() == LaneMovementType.RF_STRAIGHT_RIGHT.getCode()) {
            laneMovementTypes.add(LaneMovementType.RF_STRAIGHT);
            laneMovementTypes.add(LaneMovementType.RF_RIGHT);
        } else if (laneMovementType.getCode() == LaneMovementType.RF_LEFT_RIGHT.getCode()) {
            laneMovementTypes.add(LaneMovementType.RF_RIGHT);
            laneMovementTypes.add(LaneMovementType.RF_LEFT);
        } else if (laneMovementType.getCode() == LaneMovementType.RF_STRAIGHT_LEFT_RIGHT.getCode()) {
            laneMovementTypes.add(LaneMovementType.RF_STRAIGHT);
            laneMovementTypes.add(LaneMovementType.RF_LEFT);
            laneMovementTypes.add(LaneMovementType.RF_RIGHT);
        }
        return laneMovementTypes;
    }



    /**
     * 根据1049相位列表，查找对应的车道，转换成莱斯标准的相位数据
     *
     * @param signalGroupNos
     * @param crossingBaseInfo
     * @return
     */
    public List<Integer> exchange2CentralPhase(List<Integer> signalGroupNos,
                                               CrossingService.CrossingBaseInfo crossingBaseInfo) {
        List<Integer> phaseNosInt = new ArrayList<>();

        Optional<Map<String, LampGroupParam>> lampGroupParamMapOp = getData(crossingBaseInfo.getCrossingId(), crossingBaseInfo.getSubJuncNo(),
                LampGroupParam.class);
        if(!lampGroupParamMapOp.isPresent()){
            return phaseNosInt;
        }

        Map<String, LampGroupParam> lampGroupParamMap = lampGroupParamMapOp.get();

        signalGroupNos.stream().forEach(
                signalGroupNo -> {

                    //查找内存相位数据项
                    Optional<SignalGroupParam> signalGroupParamOp = getData(crossingBaseInfo.getCrossingId(), crossingBaseInfo.getSubJuncNo(),
                            String.valueOf(signalGroupNo), SignalGroupParam.class);
                    if (signalGroupParamOp.isPresent()) {
                        SignalGroupParam signalGroupParam = signalGroupParamOp.get();

                        LampGroupNoList lampGroupNoList = signalGroupParam.getLampGroupNoList();
                        if(lampGroupNoList == null || lampGroupNoList.getLampGroupNo() == null
                        || lampGroupNoList.getLampGroupNo().isEmpty()){
                            return;
                        }

                        lampGroupNoList.getLampGroupNo().stream().forEach(
                                lampGroupNo -> {
                                    LampGroupParam lampGroupParam = lampGroupParamMap.get(lampGroupNo);
                                    if (lampGroupParam == null) {
                                        return;
                                    }

                                    //获取方向
                                    DirectionType directionType = DirectionType39900.trans2Les(lampGroupParam.getDirection());
                                    //灯组类型
                                    LampGroupParamType lampGroupParamType = LampGroupParamType.parseCode(lampGroupParam.getType());

                                    if(lampGroupParamType.isPed()){
                                        //转换人行横道数据项
                                        DefaultMovementType defaultMovementType = DefaultMovementType.parsePedestrianMovement(
                                                directionType);
                                        if(defaultMovementType != null){
                                            DefaultPhase defaultPhase = DefaultPhase.parse(defaultMovementType);
                                            if(defaultPhase != null){
                                                if (!phaseNosInt.contains(defaultPhase.getCode())) {
                                                    phaseNosInt.add(defaultPhase.getCode());
                                                }
                                            }
                                        }

                                    }else {
                                        //获取流向
                                        LaneMovementType laneMovementTypeOp = lampGroupParamType.getLaneMovementType(lampGroupParamMapOp.get(), lampGroupParam.getDirection());
                                        if (laneMovementTypeOp.getCode() == LaneMovementType.RF_OTHER.getCode()) {
                                            return;
                                        }

                                        //将混合车道流向转换成多个流向，以便进行相位转换
                                        List<LaneMovementType> laneMovementTypes = getLaneMovementTypes(laneMovementTypeOp);
                                        for (LaneMovementType laneMovementType : laneMovementTypes) {

                                            DefaultMovementType movementType = DefaultMovementType.parseMotorwayMovement(directionType,
                                                    laneMovementType);
                                            if (movementType == DefaultMovementType.UNKNOWN) {
                                                continue;
                                            }
                                            //将流向转换成莱斯相位
                                            for (DefaultPhase defaultPhase : DefaultPhase.values()) {
                                                if (defaultPhase.getMovement() == movementType) {
                                                    if (!phaseNosInt.contains(defaultPhase.getCode())) {
                                                        phaseNosInt.add(defaultPhase.getCode());
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                    }

                                });
                    }
                }
        );

        return phaseNosInt;
    }

}
