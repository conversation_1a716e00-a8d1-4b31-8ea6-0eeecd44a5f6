package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.LesControlMode;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.ManualControlService;
import com.myweb.daa.areasignal.centralsystem.service.TmpPlanDataService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.controller.HtSignalController;
import com.myweb.daa.areasignal.htbussiness.utils.Utils;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.CrossCtrlInfo;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class CrossControlModePublish {
    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private HtSignalController htSignalController;

    @Autowired
    private ManualControlService manualControlService;

    @Autowired
    private TmpPlanDataService tmpPlanDataService;

    /**
     * 存储路口最新的控制状态数据项
     */
    private Map<String, CrossCtrlInfo> crossControlModeMap = new ConcurrentHashMap<>();


    /**
     * 内存中记录所有当前莱斯控制方式，不使用上面的控制方式
     */
    private Map<String, Integer> curCrossModeMap = new ConcurrentHashMap<>();

    /**
     * 获取当前控制方式
     *
     * @param crossID
     * @return
     */
    public Optional<Integer> getCurrentLesControlMode(String crossID) {
        return Optional.ofNullable(curCrossModeMap.get(crossID));
    }

    /**
     * 判定是否控制方式存在
     * @param crossID
     * @return
     */
    public boolean isCrossControlModeExist(String crossID) {
        return crossControlModeMap.containsKey(crossID);
    }


    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processCrossControlMode(CrossCtrlInfo crossCtrlInfo) {
        String crossID = crossCtrlInfo.getCrossID();

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossID);
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        String lesMode = Utils.change2LesMode(crossingInfoOp.get(), crossCtrlInfo.getControlMode());

        //检查路口状态是否发生变化
        boolean needNotify = false;
        CrossCtrlInfo crossCtrlInfoOld = crossControlModeMap.get(crossingInfoOp.get().getCrossingId());
        //第一次获取路口状态
        if (crossCtrlInfoOld == null) {
            needNotify = true;
        } else {
            String valueNew = crossCtrlInfo.getControlMode();
            String valueOld = crossCtrlInfoOld.getControlMode();
            //如果数据均不为null
            if (valueNew != null && valueOld != null) {
                needNotify = !(valueNew.equalsIgnoreCase(valueOld));
            } else if (valueNew != null) {
                needNotify = true;
            }
        }

        if (needNotify) {
            //系统手动特殊转换控制
            if (lesMode.equalsIgnoreCase(String.valueOf(LesControlMode.LOCK_FLOW.value()))) {
                if (manualControlService.isManualControl(crossID)) {
                    lesMode = String.valueOf(LesControlMode.SYSTEM_MANUAL.value());
                }
            }
            //临时方案控制页数转换
            else if (lesMode.equalsIgnoreCase(String.valueOf(LesControlMode.TEMPORARY_PATTERN.value()))) {
                lesMode = tmpPlanDataService.modeChange(crossingInfoOp.get().getControllerId(), crossID, lesMode);
            }

            notifyCrossMode(crossingInfoOp.get(), lesMode, true);
        }

        crossControlModeMap.put(crossID, crossCtrlInfo);
    }

    /**
     * 通知当前控制方式
     *
     * @param crossingInfo
     * @param lesMode
     */
    public void notifyCrossMode(CrossingService.CrossingBaseInfo crossingInfo, String lesMode, boolean saveMode) {
        com.myweb.daa.areasignal.centralsystem.param.CrossControlMode controlMode =
                com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.builder()
                        .signalControllerID(crossingInfo.getControllerId())
                        .noArea(crossingInfo.getNoArea())
                        .noJunc(crossingInfo.getNoJunc())
                        .mode(Integer.valueOf(lesMode))
                        .crossingSeqNo(crossingInfo.getSubJuncNo()).build();

        if (saveMode) {
            //莱斯内部控制方式记录
            curCrossModeMap.put(crossingInfo.getCrossingId(), controlMode.getMode());
        }

        List<Object> objectList = new ArrayList<>();
        objectList.add(controlMode);
        MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingInfo.getControllerId(),
                com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.MqObjectId, objectList);
        messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossControlMode.class.getSimpleName().toLowerCase(), mqMessage);
        messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(controlMode)
                .build());
    }

    /**
     * 解决的问题是：
     * 厂家在信号机脱机又上线之后，控制方式不发生变化导致信号机控制方式异常；
     * 协议适配器控制方式只上传了异常脱机控制方式
     *
     * @param crossControlModeCheck
     */
    @EventListener
    @Async(GlobalConfigure.CROSS_STATE_PROCESS_EXECUTOR)
    public void processCrossStateCheck(CrossControlModeCheck crossControlModeCheck) {

        if (crossControlModeCheck == null || crossControlModeCheck.getCrossId() == null) {
            return;
        }

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp =
                crossingService.getCrossingInfo(crossControlModeCheck.getCrossId());
        if (!crossingInfoOp.isPresent()) {
            return;
        }

        JsonResult crossControlModeJr = htSignalController.loadCrossData(crossControlModeCheck.getCrossId(), "CrossCtrlInfo", "0");
        if (crossControlModeJr.isSuccess()) {
            Object data = crossControlModeJr.getData();
            if (data != null && (data instanceof List)) {
                List<Object> datas = (List<Object>) data;
                if (datas != null && !datas.isEmpty()
                        && datas.get(0) instanceof CrossCtrlInfo) {
                    CrossCtrlInfo crossCtrlInfo = (CrossCtrlInfo) (datas.get(0));
                    //增加保护
                    if ((crossCtrlInfo == null)
                            || (crossCtrlInfo.getControlMode().isEmpty())) {
                        return;
                    }
                    //设置内部设备ID
                    crossCtrlInfo.setCrossID(crossingInfoOp.get().getCrossingId());

                    processCrossControlMode(crossCtrlInfo);
                }
            }
        }
    }

    @Data
    public static class CrossControlModeCheck {
        private String crossId;
    }


    @EventListener
    @Async(GlobalConfigure.CROSS_STATE_PROCESS_EXECUTOR)
    public void processCrossOffline(CrossOffline crossOffline) {
        log.debug("信号机脱机,移除旧控制方式-{}", crossOffline.getCrossId());
        crossControlModeMap.remove(crossOffline.getCrossId());

        //莱斯控制方式记录
        curCrossModeMap.put(crossOffline.crossId, LesControlMode.OFFLINE.value());

    }

    @Data
    public static class CrossOffline {
        private String crossId;
    }


    /**
     * 发布所有的控制方式
     */
    public void notifyAllMode() {
        curCrossModeMap.keySet().forEach(
                crossId -> {
                    Optional<CrossingService.CrossingBaseInfo> crossingInfo = crossingService.getCrossingInfo(crossId);
                    if (!crossingInfo.isPresent()) {
                        return;
                    }
                    notifyCrossMode(crossingInfo.get(), String.valueOf(curCrossModeMap.get(crossId)), false);
                }
        );
    }

}
