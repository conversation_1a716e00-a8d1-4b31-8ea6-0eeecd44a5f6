package com.myweb.daa.areasignal.htbussiness.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class P1049Infos {
    private String signalId; //可能是信号机ID，也可能是路口id
    private List<P1049Entity> controlerInfos = new ArrayList<>();
}
