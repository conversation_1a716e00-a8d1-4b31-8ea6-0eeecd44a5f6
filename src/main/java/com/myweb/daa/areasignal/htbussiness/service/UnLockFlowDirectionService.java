package com.myweb.daa.areasignal.htbussiness.service;

import com.les.ads.ds.gis.Movement;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.param.UnLockFlowDirection;
import com.myweb.daa.areasignal.centralsystem.param.v2.DirectionType39900;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.service.SignalCacheService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;


/**
 * 接收延迟的取消相位锁定的原先锁定的接口进行相位的解锁定
 */
@Slf4j
@Data
@Service
public class UnLockFlowDirectionService {

    /**
     * 解锁相位事件
     */
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class UnLockFlowEvent {
        private String signalId;
        private int subJuncNo;
        private String stageNo;
        private AtomicBoolean enable;
    }

    @Autowired
    private HtSignalService htSignalService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private LockFlowDirectionService lockFlowDirectionService;


    /**
     * 生成锁定的数据
     * @param crossingBaseInfo
     * @param stageNoLes
     * @return
     */
    public List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection>
    genUnLockFlowDirectionMsg(CrossingService.CrossingBaseInfo crossingBaseInfo, int stageNoLes) {

        List<com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection> unLockFlowDirections = new ArrayList<>();
        if( crossingBaseInfo == null){
            return unLockFlowDirections;
        }

        List<Movement> toUseMovements = lockFlowDirectionService.getMovementsByStage(crossingBaseInfo, stageNoLes);

        if(toUseMovements.isEmpty()){
            log.error("路口{}获取阶段{}流向为空", crossingBaseInfo.getCrossingId(), stageNoLes);
            return unLockFlowDirections;
        }

        toUseMovements.forEach(
                movement -> {
                    DirectionType39900 inDir = DirectionType39900.trans(movement.getDirection());
                    DirectionType39900 outDir = DirectionType39900.trans(movement.getOutDir());
                    if(inDir == DirectionType39900.UNKNOWN || outDir == DirectionType39900.UNKNOWN){
                        log.error("路口{}阶段{}流向{}-{}转换成1049方向异常", crossingBaseInfo.getCrossingId(), stageNoLes,
                                movement.getDirection(), movement.getOutDir());
                        return;
                    }

                    com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection lockFlowDirectionMsg
                            = com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.UnLockFlowDirection.builder()
                            .CrossID(crossingBaseInfo.getCrossingId1049())
                            .Type("1")
                            .Entrance(String.valueOf(inDir.getCode()))
                            .Exit(String.valueOf(outDir.getCode())).build();
                    unLockFlowDirections.add(lockFlowDirectionMsg);
                }
        );

        log.error("路口{}解除锁定阶段{}生成解除锁定流向数据项-{}", crossingBaseInfo.getCrossingId(), stageNoLes, unLockFlowDirections);

        return unLockFlowDirections;
    }

    @EventListener
    @Async(GlobalConfigure.MESSAGE_PROCESS_EXECUTOR)
    public void processCrossPlan(UnLockFlowEvent unLockFlowEvent) {
        if (unLockFlowEvent.getEnable() != null && unLockFlowEvent.getEnable().get()) {
            Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(unLockFlowEvent.getSignalId());
            if (!crossingInfoOp.isPresent()) {
                log.error("{}", "没有找到路口对应的信号机" + unLockFlowEvent.getSignalId() + ",子路口" + unLockFlowEvent.getSubJuncNo());
                return;
            }

            List<Object> objectList = new ArrayList<>();
            UnLockFlowDirection lockFlowDirection = UnLockFlowDirection.builder()
                    .signalControllerID(crossingInfoOp.get().getControllerId())
                    .crossingSeqNo(crossingInfoOp.get().getSubJuncNo())
                    .noStage(Integer.parseInt(unLockFlowEvent.getStageNo()))
                    .build();
            objectList.add(lockFlowDirection);

            MqMessage mqMessage = P1049HelpUtils.buildSimuSetMqMsg(crossingInfoOp.get().getControllerId(),
                    UnLockFlowDirection.MqObjectId, objectList);
            messagePublisher.publishMessage(mqMessage);
        } else {
            log.error("锁定解除取消事件-{}", unLockFlowEvent);
        }
    }
}
