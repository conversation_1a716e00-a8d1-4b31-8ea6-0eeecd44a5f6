package com.myweb.daa.areasignal.htbussiness.service.publish;

import com.myweb.daa.areasignal.centralsystem.dbsave.DataSavePushBean;
import com.myweb.daa.areasignal.centralsystem.mq.MqMessage;
import com.myweb.daa.areasignal.centralsystem.service.CrossingService;
import com.myweb.daa.areasignal.centralsystem.utils.P1049HelpUtils;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.htbussiness.service.Ht1049SignalCacheService;
import com.myweb.daa.areasignal.transport.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/4/20 16:00
 */
@Slf4j
@Service
public class CrossPhaseLampStatusPublish {
    @Autowired
    private Ht1049SignalCacheService ht1049SignalCacheService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private MessageSender messageSender;


    @Data
    public static class StageInfo {
        private com.myweb.daa.areasignal.centralsystem.param.CrossStage crossStage;
        private LocalDateTime localDateTime;

        private long localTime;
    }

    private Map<String, StageInfo> stageInfoMap = new ConcurrentHashMap<>();

    /**
     * 相位切换数据推送
     *
     * @param newStageNo
     * @param crossingBaseInfo
     */
    private void notifyCrossStage(int newStageNo,
                                  CrossingService.CrossingBaseInfo crossingBaseInfo) {
        com.myweb.daa.areasignal.centralsystem.param.CrossStage crossStage =
                com.myweb.daa.areasignal.centralsystem.param.CrossStage.builder()
                        .signalControllerID(crossingBaseInfo.getControllerId())
                        .noArea(crossingBaseInfo.getNoArea())
                        .noJunc(crossingBaseInfo.getNoJunc())
                        .noOldStage(0)
                        .noNewStage(newStageNo)
                        .cityCode(GlobalConfigure.cityCode)
                        .crossingSeqNo(crossingBaseInfo.getSubJuncNo())
                        .build();

        //查看是否需要修改为真实的相位阶段运行时间
        StageInfo stageInfo = stageInfoMap.get(crossingBaseInfo.getCrossingId());
        if (stageInfo != null) {
            //判定新旧阶段是否相同，相同时不进行数据传输;比如是由于非当前阶段绿灯相位的灯态发生变化
            if (stageInfo.getCrossStage().getNoNewStage() == newStageNo) {
                log.warn("信号机-{}新旧阶段相同不进行数据广播-{}", crossingBaseInfo.getControllerId(), newStageNo);
                return;
            }

            double time = ((int) (System.currentTimeMillis() - stageInfo.getLocalTime())) / 1000.0;
            BigDecimal d = new BigDecimal(time);
            BigDecimal bigDecimal = d.setScale(0, RoundingMode.HALF_UP);
            crossStage.setLenOldStage(bigDecimal.intValue());
            log.debug("&&&&&阶段运行时间-{},转化为-{}", time, crossStage.getLenOldStage());
            crossStage.setNoOldStage(stageInfo.getCrossStage().getNoNewStage());
            stageInfo.setLocalDateTime(LocalDateTime.now());
            stageInfo.setLocalTime(System.currentTimeMillis());
            stageInfo.setCrossStage(crossStage);
        } else {
            StageInfo stageInfo1 = new StageInfo();
            stageInfo1.setLocalDateTime(LocalDateTime.now());
            stageInfo1.setCrossStage(crossStage);
            stageInfo1.setLocalTime(System.currentTimeMillis());
            stageInfoMap.put(crossingBaseInfo.getCrossingId(), stageInfo1);
        }

        List<Object> objectList = new ArrayList<>();
        objectList.add(crossStage);
        MqMessage mqMessage = P1049HelpUtils.buildPushMqMsg(crossingBaseInfo.getControllerId(),
                com.myweb.daa.areasignal.centralsystem.param.CrossStage.MqObjectId, objectList);
        messageSender.send(com.myweb.daa.areasignal.centralsystem.param.CrossStage.class.getSimpleName().toLowerCase(), mqMessage);
        messagePublisher.publishMessage(DataSavePushBean.builder().realTimeMsgInterface(crossStage)
                .build());

    }


}
