package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 15:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Data")
public class StageTrafficDataLane {

    private String LaneNo;

    /**
     * 过车车辆数
     */
    private int VehicleNum;

    /**
     * 当量小汽车
     */
    private String Pcu;

    /**
     * 平均车头时距
     */
    private String HeadTime;

    /**
     * 饱和度
     */
    private String Saturation;

    /**
     * 阶段结束时排队长度
     */
    private String QueueLength;

    /**
     * 占有率
     */
    private String Occupancy;
}
