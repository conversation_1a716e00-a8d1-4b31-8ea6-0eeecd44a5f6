package com.myweb.daa.areasignal.protocol.p1049.process.message.object;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SDO_Error1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 13:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SDO_Error")
public class SDO_Error1049 {
    @XStreamAlias("ErrObj")
    private String errObj;
    @XStreamAlias("ErrType")
    private String errType;
    @XStreamAlias("ErrDesc")
    private String errDesc;
    @XStreamAlias("SDO_TimeOut")
    private String SDO_TimeOut;
}
