package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: PlanParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("PlanParam")
public class PlanParam implements Serializable, DataIndexAble {
    private String CrossID;
    private String PlanNo;
    private String PlanName;
    private String CycleLen;
    private String CoordStageNo;
    private String Offset;

    private StageTimingList StageTimingList;


    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return PlanNo;
    }
}
