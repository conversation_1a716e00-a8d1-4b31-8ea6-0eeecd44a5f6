package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: RegionParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("RegionParam")
public class RegionParam  implements DataIndexAble{
    private String RegionID;
    private String RegionName;
    private SubRegionIDList SubRegionIDList;
    private CrossIDList CrossIDList;

    @Override
    public String dataID() {
        return RegionID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
