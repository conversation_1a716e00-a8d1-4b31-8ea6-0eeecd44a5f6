package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: CommOperation1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 13:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Body")
public class Body1049 {

    @XStreamAlias("Operation")
    @XStreamImplicit
    private List<BaseOperation1049> operation;

}
