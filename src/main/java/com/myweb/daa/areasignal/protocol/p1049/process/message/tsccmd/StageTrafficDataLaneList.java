package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 15:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("DataList")
public class StageTrafficDataLaneList {

    @XStreamImplicit(itemFieldName = "Data")
    private List<StageTrafficDataLane> StageTrafficDataLane;
}
