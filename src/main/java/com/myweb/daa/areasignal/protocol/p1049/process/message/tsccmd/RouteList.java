package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: SysInfo
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 14:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("RouteList")
public class RouteList {
    @XStreamImplicit(itemFieldName = "Route")
    List<Route> Route;
}
