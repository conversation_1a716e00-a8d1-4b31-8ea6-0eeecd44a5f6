package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: LampGroupNoList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("LampGroupNoList")
public class LampGroupNoList {
    @XStreamImplicit(itemFieldName = "LampGroupNo")
    private List<String> LampGroupNo;
}
