package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SysState
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/15 14:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SysState")
public class SysState implements DataIndexAble{
    private String Value;
    private String Time;

    @Override
    public String dataID() {
        return "0";
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
