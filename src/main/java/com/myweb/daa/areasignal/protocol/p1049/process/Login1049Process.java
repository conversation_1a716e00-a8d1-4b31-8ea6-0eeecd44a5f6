package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.centralsystem.service.SystemIpService;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.HtSignalService;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_Error1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_User1049;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: Login1049Process
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:37
 */
@Component
@Slf4j
public class Login1049Process implements CmdProcessAble {

    @Autowired
    private MessageOuterPublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private P1049Manager p1049Manager;

    @Autowired
    private HtSignalService htSignalService;

    @Autowired
    private SystemIpService systemIpService;

    /**
     * 品牌+ip地址
     */
    private Map<String, Boolean> isFirstRequestMap = new ConcurrentHashMap<>();

    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildLoginSuccess(SDO_User1049 sdo_user1049,
                                             BaseMessage1049 baseMessage1049,
                                             P1049Configure p1049Configure) {
        //构造内部数据项
        SDO_User1049 su = SDO_User1049.builder()
                .userName(sdo_user1049.getUserName())
                .pwd(sdo_user1049.getPwd()).build();
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_User1049(su)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 login1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return login1049;
    }


    /**
     * 构造登录出错数据项
     *
     * @param p1049Configure
     * @param errObj
     * @param errType
     * @param errDesc
     * @return
     */
    public BaseMessage1049 buildLoginError1049(P1049Configure p1049Configure,
                                               String errObj, String errType,
                                               String errDesc) {
        //构造内部数据项
        SDO_Error1049 su = SDO_Error1049.builder()
                .errObj(errObj)
                .errType(errType)
                .errDesc(errDesc)
                .SDO_TimeOut("10").build();

        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_Error1049(su)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);

        //构造数据项
        BaseMessage1049 loginError1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return loginError1049;
    }


    @Override
    public void processRequest(Object object, BaseMessage1049 baseMessage1049) {
        if (!(object instanceof SDO_User1049)) {
            log.error("使用Login1049Process进行数据处理，但是不是SDO_User1049类型");
            return;
        }

        SDO_User1049 sdo_user1049 = (SDO_User1049) object;
        log.debug("收到数据报文项-{}", sdo_user1049);
        //检查用户登录信息
        Optional<SignalBrandPort> signalBrandPortOptional = baseMessage1049.getSignalBrandPortOptional();
        if (!signalBrandPortOptional.isPresent()) {
            log.error("没有找到源系统类型,登录数据无法处理");
            return;
        }
        //获取信号系统配置参数
        P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(baseMessage1049.getSignalBrandPortOptional());
        if (sysConfig.getUserName().equals(sdo_user1049.getUserName())
                && sysConfig.getPassword().equals(sdo_user1049.getPwd())) {
            //添加新的token
            String token = p1049Manager.addToken(baseMessage1049.getAddress(), signalBrandPortOptional.get());
            //登录成功
            BaseMessage1049 loginSuccess = buildLoginSuccess(sdo_user1049, baseMessage1049, p1049Configure);
            //设置系统源
            loginSuccess.setSignalBrandPortOptional(baseMessage1049.getSignalBrandPortOptional());
            //设置头数据项
            P1049Utils.setBaseHeader1049(loginSuccess, p1049Configure, token, MessageType.RESPONSE, baseMessage1049.getSeq());
            //设置发送目的
            loginSuccess.setAddress(baseMessage1049.getAddress());
            //数据发送
            InterProtocol interProtocol = P1049Utils.buildMessage(loginSuccess.getAddress(), InterProtocolType.P1049_MSG
                    , loginSuccess, false, false);
            messagePublisher.sendMessageOuter(interProtocol);

            //判定是否已经请求过数据项
            String loginKey = signalBrandPortOptional.get().name() + "-" + baseMessage1049.getAddress();
            Boolean signalRequested = isFirstRequestMap.get(loginKey);
            if (p1049Configure.isLoginGetData() && signalRequested == null) {
                isFirstRequestMap.put(loginKey, true);
                JsonResult jsonResult = htSignalService.loadDataWhenLoginSuccess(signalBrandPortOptional.get(),
                        baseMessage1049.getAddress());
                log.info("获取数据项-{}", jsonResult);

            } else if (signalRequested != null) {
                htSignalService.reSubscribe(signalBrandPortOptional.get(), baseMessage1049.getAddress());
                log.info("2222重新订阅数据项结束");
            }
        } else {
            //登录失败
            String errObj = "";
            String errType = "";
            String errDesc = "";
            if (!sysConfig.getUserName().equals(sdo_user1049.getUserName())) {
                errObj = "UserName";
                errType = "SDE_UserName";
                errDesc = "用户名错误";
            } else {
                errObj = ("Pwd");
                errType = ("SDE_Pwd");
                errType = ("用户密码错误");
            }
            BaseMessage1049 loginError1049 = buildLoginError1049(p1049Configure, errObj, errType, errDesc);
            loginError1049.setSignalBrandPortOptional(signalBrandPortOptional);
            //设置头数据项
            P1049Utils.setBaseHeader1049(loginError1049, p1049Configure, "", MessageType.ERROR, baseMessage1049.getSeq());
            //设置发送目的
            loginError1049.setAddress(baseMessage1049.getAddress());
            //数据发送
            InterProtocol interProtocol = P1049Utils.buildMessage(loginError1049.getAddress(), InterProtocolType.P1049_MSG
                    , loginError1049, false, false);
            messagePublisher.sendMessageOuter(interProtocol);
        }
    }

    @Override
    public void processResponse(Object object, BaseMessage1049 baseMessage1049) {
        if (!(object instanceof SDO_User1049)) {
            log.error("使用Login1049Process进行数据处理，但是不是SDO_User1049类型");
            return;
        }

        SDO_User1049 sdo_user1049 = (SDO_User1049) object;
        log.debug("收到数据报文项-{}", sdo_user1049);
        //检查用户登录信息
        Optional<SignalBrandPort> signalBrandPortOptional = baseMessage1049.getSignalBrandPortOptional();
        if (!signalBrandPortOptional.isPresent()) {
            log.error("没有找到源系统类型,登录数据无法处理");
            return;
        }

        //添加新的token
        String token = p1049Manager.addToken(baseMessage1049.getAddress(), signalBrandPortOptional.get(),
                baseMessage1049.getToken());

        //判定是否已经请求过数据项
        String loginKey = signalBrandPortOptional.get().name() + "-" + baseMessage1049.getAddress();
        Boolean signalRequested = isFirstRequestMap.get(loginKey);
        if (p1049Configure.isLoginGetData() && signalRequested == null) {
            isFirstRequestMap.put(loginKey, true);
            JsonResult jsonResult = htSignalService.loadDataWhenLoginSuccess(signalBrandPortOptional.get(),
                    baseMessage1049.getAddress());
            log.info("获取数据项-{}", jsonResult);

        } else if (signalRequested != null) {
            htSignalService.reSubscribe(signalBrandPortOptional.get(), baseMessage1049.getAddress());
            log.info("11111重新订阅数据项结束");
        }
    }

    @Override
    public void processError(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 error");
    }

    @Override
    public void processPush(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 push");
    }

    @Override
    public OperationName getOperationName() {
        return OperationName.Login;
    }

    @Override
    public String getCmdObjectName() {
        return SDO_User1049.class.getName();
    }


    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildLoginMsg(P1049Configure.SysConfig sysConfig) {
        //构造内部数据项
        SDO_User1049 su = SDO_User1049.builder()
                .userName(sysConfig.getUserName())
                .pwd(sysConfig.getPassword()).build();
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_User1049(su)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 login1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return login1049;
    }

}
