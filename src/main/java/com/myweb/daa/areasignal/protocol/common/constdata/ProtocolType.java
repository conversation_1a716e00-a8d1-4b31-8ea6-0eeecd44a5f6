package com.myweb.daa.areasignal.protocol.common.constdata;

import com.myweb.daa.areasignal.protocol.common.OuterProtocolType;
import com.myweb.daa.areasignal.protocol.p1049.constdata.P1049GetMessageType;


/**
 * @ClassName: ProtocolType
 * @Description: 外部协议枚举类型
 * @Author: king
 * @CreateDate: 2018/12/3 15:12
 */
public enum ProtocolType {

    /**
     * 1049协议
     */
    P1049_SIGNAL(OuterProtocolType.P1049_SIGNAL.value(), OuterProtocolType.P1049_SIGNAL.description(), P1049GetMessageType.class);


    private int value;
    private String description;
    private Class getMessageTypeable;

    ProtocolType(int value, String description, Class getMessageTypeable) {
        this.value = value;
        this.description = description;
        this.getMessageTypeable = getMessageTypeable;
    }

    public int value() {
        return this.value;
    }

    public String description() {
        return this.description;
    }

    public Class getMessageTypeable() {
        return getMessageTypeable;
    }
}
