package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/26
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SignalControllerError")
public class SignalControllerError implements DataIndexAble{

    @XStreamAlias("SignalControllerID")
    private String signalControllerId;

    @XStreamAlias("ErrorType")
    private Integer errorType;

    @XStreamAlias("ErrorDesc")
    private String errorDesc;

    @XStreamAlias("OccerTime")
    private String occerTime;

    @Override
    public String dataID() {
        return signalControllerId;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
