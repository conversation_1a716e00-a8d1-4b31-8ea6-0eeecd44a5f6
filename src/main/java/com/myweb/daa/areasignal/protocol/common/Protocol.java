package com.myweb.daa.areasignal.protocol.common;

import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.protocol.common.channelhandler.ProtocolChannelInitializer;
import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.p1049.channelhandler.P1049SignalChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.util.Optional;

/**
 * @ClassName: Protocol
 * @Description: 协议管理类
 * @Author: king
 * @CreateDate: 2018/12/3 15:04
 */
@Data
@Slf4j
public class Protocol {
    private static final String DEFAULT_PROTOCL_STRING = ProtocolType.P1049_SIGNAL.name();
    private ProtocolType protocolType;
    private ProtocolChannelInitializer protocolChannelInitializer;
    private Environment environment;


    private Protocol(ProtocolType protocolType,
                     ProtocolChannelInitializer protocolChannelInitializer, Environment environment) {
        this.protocolType = protocolType;
        this.protocolChannelInitializer = protocolChannelInitializer;
        this.environment = environment;
    }


    public static Optional<Protocol> buildProtocol(String protocol,
                                                   MessagePublisher messagePublisher, boolean isComm, Environment environment) {
        if (protocol.trim().isEmpty()) {
            protocol = DEFAULT_PROTOCL_STRING;
        }
        ProtocolType protocolType = ProtocolType.valueOf(protocol.trim());
        return buildProtocol(protocolType, messagePublisher, isComm, environment);
    }

    public static Optional<Protocol> buildProtocol(ProtocolType protocolType, MessagePublisher messagePublisher, boolean isComm, Environment environment) {
        if (ProtocolType.P1049_SIGNAL == protocolType) {
            P1049SignalChannelInitializer p1049SignalChannelInitializer = new P1049SignalChannelInitializer<SocketChannel>(messagePublisher, environment);
            return Optional.of(new Protocol(protocolType, p1049SignalChannelInitializer, environment));
        } else {
            log.error("not support protocol " + protocolType.value() + ", " + protocolType.description() + " yet");
            return Optional.empty();
        }
    }


}
