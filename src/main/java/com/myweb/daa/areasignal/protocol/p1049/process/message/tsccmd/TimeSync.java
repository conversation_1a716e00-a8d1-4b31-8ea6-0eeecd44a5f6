package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/2/12 14:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("TimeSync")
public class TimeSync {

    private int Year;

    private int Month;

    private int Day;

    private int Hour;

    private int Minute;

    private int Second;

}
