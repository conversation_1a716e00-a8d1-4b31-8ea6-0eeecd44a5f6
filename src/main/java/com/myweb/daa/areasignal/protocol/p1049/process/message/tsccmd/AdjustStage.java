package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/25
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("AdjustStage")
public class AdjustStage {

    @XStreamAlias("CrossID")
    private String crossId;

    @XStreamAlias("StageNo")
    private Integer stageNo;

    @XStreamAlias("Type")
    private Integer type;

    @XStreamAlias("Len")
    private Integer len;
}
