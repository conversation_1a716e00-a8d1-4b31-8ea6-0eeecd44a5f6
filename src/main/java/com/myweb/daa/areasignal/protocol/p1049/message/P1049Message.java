package com.myweb.daa.areasignal.protocol.p1049.message;

import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.common.message.AbstractProtocolMessage;
import com.myweb.daa.areasignal.protocol.p1049.constdata.P1049Type;
import lombok.Data;

/**
 * @ClassName: P1049Message
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:14
 */
@Data
public final class P1049Message extends AbstractProtocolMessage {

    public P1049Message() {
        super(ProtocolType.P1049_SIGNAL);
    }

    /**
     * 完整的xml数据项
     */
    private byte[] body;

    @Override
    public byte[] getBody() {
        return body;
    }

    @Override
    public short getMessageID() {
        return P1049Type.P1049_MSG.value();
    }
}
