/*
 * Copyright 2013-2018 Lil<PERSON><PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.myweb.daa.areasignal.protocol.common.codec;


import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.protocol.common.message.AbstractProtocolMessage;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 报文处理末端，当前为发送数据到总线
 * @date 2014年3月15日
 */
@Slf4j
@Data
public class CommonMessageHandler extends ChannelInboundHandlerAdapter {

    private MessagePublisher messagePublisher;

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        AbstractProtocolMessage message = (AbstractProtocolMessage) msg;
        //设置channel上下文对象
        message.setCtx(ctx);
        messagePublisher.publishMessage(message);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
        log.error("close the connection for exception exist", cause);
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent e = (IdleStateEvent) evt;
            if (e.state() == IdleState.READER_IDLE) {
                ctx.close();
            } else if (e.state() == IdleState.WRITER_IDLE) {
                log.error("超时未写数据,准备断开tcp连接-{}", ctx);
                ctx.close();
            }
        }
    }

}
