package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: StageNoList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 16:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageNoList")
public class StageNoList implements Serializable {
    @XStreamImplicit(itemFieldName = "StageNo")
    private List<String> StageNo;

}
