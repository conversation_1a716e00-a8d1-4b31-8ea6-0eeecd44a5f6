package com.myweb.daa.areasignal.protocol.p1049.channelhandler;

import com.myweb.daa.areasignal.netty.ChannelHolder;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
public class CheckMultiLinkHandler extends ChannelInboundHandlerAdapter {

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {

        /**记录当前activeSocket*/
        int localPort = ChannelHolder.getLocalPort(ctx.channel());

        Optional<ChannelHolder.ChannelInfo> loggedChannel = ChannelHolder.getMAP().values().stream().filter(
                channelInfo -> ChannelHolder.getLocalPort(channelInfo.getChannel()) == localPort
        ).findAny();

        //关闭之前的链路数据
        if (loggedChannel.isPresent()) {
            ctx.close().addListener(ChannelFutureListener.CLOSE);
            String info = "信号系统本地" + localPort + "存在两个链路，拒绝新连接";
            log.error("链路异常-{}", info);
            log.error("old-{}, current-{}", loggedChannel, ctx);
        } else {
            ctx.fireChannelActive();
        }
    }

}
