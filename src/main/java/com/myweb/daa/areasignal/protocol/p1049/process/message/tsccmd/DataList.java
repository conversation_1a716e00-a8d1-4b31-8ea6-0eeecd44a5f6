package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: DataList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:25
 */
@lombok.Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("DataList")
public class DataList {
    @XStreamImplicit(itemFieldName = "Data")
    private List<Data> Data;
}
