package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CrossCycle
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 14:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossCycle")
public class CrossCycle implements DataIndexAble{

    private String CrossID;
    private String StartTime;
    private String LastCycleLen;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
