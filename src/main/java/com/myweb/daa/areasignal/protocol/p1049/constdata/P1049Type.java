/*
 * Copyright 2013-2018 Lil<PERSON><PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.myweb.daa.areasignal.protocol.p1049.constdata;

import com.myweb.daa.areasignal.protocol.p1049.constdata.inner.P1049_Msg;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 协议内部类型定义
 * @date 2014年3月15日
 */
@Slf4j
public enum P1049Type {
    /****************************P1049协议****************************************************/
    /**
     * 数据报文处理
     */
    P1049_MSG((short) 1, P1049_Msg.class),
    ;


    /**
     * 类型值
     */
    private short msgId;
    /**
     * 报文类型
     */
    private Class classType;


    P1049Type(short msgId, Class classType) {
        this.msgId = msgId;
        this.classType = classType;
    }

    public short value() {
        return this.msgId;
    }

    public Class classType() {
        return classType;
    }

}
