package com.myweb.daa.areasignal.protocol.http.dto;

import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossParamAll {

    private String crossID;

    private String crossName;

    private int feature;

    private String grade;

    private String longitude;

    private String latitude;

    private String Altitude;

    private List<DetectorParam> detectorParamList;

    private List<LaneParam> laneParamList;

    private List<PedestrianParam> pedestrianParamList;

    private List<LampGroupParam> lampGroupParamList;

    private List<SignalGroupParam> signalGroupParamList;

    private String greenConflictMatrix;

    private List<StageParam> stageParamList;

    private List<PlanParam> planParamList;

    private List<DayPlanParam> dayPlanParamList;

    private List<ScheduleParam> scheduleParamList;

}
