package com.myweb.daa.areasignal.protocol.common;


import com.myweb.daa.areasignal.protocol.InterProtocolType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: interprotocol
 * @Description: 内部模块定义的协议接口
 * @Author: king
 * @CreateDate: 2018/12/4 9:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InterProtocol {
    /**
     * 内部定义的数据项
     */
    private InterProtocolType interProtocolType;

    /**
     * 外部的协议类别，用于内部向外部发送报文的时候指定协议
     */
    private String outerProtocolType;

    /**
     * 向外部发送数据的时候，指定IP,也可以是串口标记
     */
    private String outerIp;

    /**
     * 向外部发送数据的时候，可以指定端口，默认为0表示不指定端口【只有IP的时候有效】
     */
    private int outerPort;

    /**
     * 用于标记发送的时候报文数据项，统一处理
     */
    private String outerUuid;

    /**
     * 用于标记发送报文时候的时间戳，统一处理
     */
    private long outerTimeStamp;

    /**
     * 应答的关系字，在需要应答的时候必须填写
     */
    private String ackKey;

    /**
     * 需要应答
     */
    private boolean needAck;

    /**
     * 等待应答的时间，默认是3s
     */
    private int timeOutSeconds;

    /**
     * 数据项json格式
     */
    private String jsonString;

    /**
     * 判定是否是模拟的数据报文
     */
    private boolean simuSend;
}
