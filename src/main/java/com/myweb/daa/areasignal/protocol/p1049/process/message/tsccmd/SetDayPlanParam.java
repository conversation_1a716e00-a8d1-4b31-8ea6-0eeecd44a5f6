package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 17:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SetDayPlanParam")
public class SetDayPlanParam {
    /**
     * 操作类型：1新增、2修改、3删除
     */
    private String Oper;
    /**
     * 操作类型：0新增、1删除  慈溪海康
     */
    private DayPlanParam DayPlanParam;
}
