package com.myweb.daa.areasignal.protocol.p1049.process;

import com.alibaba.fastjson.JSONObject;
import com.myweb.commons.persistence.JsonResult;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.AckManager.InvokeFuture;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseMessage;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.TSCCmd;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName: MessageSendProcess
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 13:15
 */
@Component
@Slf4j
public class MessageSendProcess {

    @Autowired
    private MessageOuterPublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private P1049Manager p1049Manager;

    /**
     * 报文序列
     */
    @Getter
    private AtomicLong seqAtomicLong1 = new AtomicLong(0);


    public String getSeq() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String nowStr = now.format(format);
        return nowStr + String.format("%03d", seqAtomicLong1.getAndIncrement() % 100);
    }


    public static String toLowerCaseFirstOne(String s) {
        if (Character.isLowerCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    /**
     * 向外部發送數據
     *
     * @param messageType
     * @param operationName
     * @param object
     */
    public JsonResult<?> processSend(MessageType messageType, OperationName operationName, Object object, boolean waitAck,
                                     SignalBrandPort signalBrandPort, Class clazz, String address) {
        int timeOutSecond = GlobalConfigure.timeOutSecond;
        return processSend(messageType, operationName, object, waitAck, signalBrandPort, clazz, timeOutSecond, address);  //todo change 25 to 5s

    }

    /**
     * 向外部發送數據
     *
     * @param messageType
     * @param operationName
     * @param object
     */
    public JsonResult<?> processSend(MessageType messageType, OperationName operationName, Object object, boolean waitAck,
                                     SignalBrandPort signalBrandPort, Class clazz, int timeOutSecond, String address) {
        //获取token
        Optional<String> tokenOptional = p1049Manager.getLoginToken(signalBrandPort, address);
        if (!tokenOptional.isPresent()) {
            return JsonResult.error("客户端尚未登录，无法发送的数据-" + object + "$$$" + signalBrandPort + "$$$" + address);
        }

        LocalDateTime startTime = LocalDateTime.now();
        log.debug("开始等待-{}应答-{}", JSONObject.toJSONString(clazz), startTime);
        LocalDateTime endTime = LocalDateTime.now();
        long millis = Duration.between(endTime, startTime).toMillis();
        log.debug("请求数据-{} \n 收到了应答-{}, \n 花费时间-{}, \n 报文中时间-{} \n" +
                        "数据应答项-{}", JSONObject.toJSONString(baseMessage1049)
                , endTime, millis,
                System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);



        {
            return new JsonResult<>(true, baseMessage1049 + "应答花费时间" + millis + "ms",
                    response.getResponseObject());
        }

    }


    /**
     * 向外部發送數據
     *
     * @param messageType
     * @param operationName
     */
    public JsonResult<?> processSendSimu(MessageType messageType, OperationName operationName,
                                         SignalBrandPort signalBrandPort, String address, String content) {
        List<TSCCmd> tSCCmd = new ArrayList<>();
        Object object = tSCCmd;
        Class clazz = TSCCmd.class;
        boolean waitAck = false;

        int timeOutSecond = GlobalConfigure.timeOutSecond;
        //获取token
        Optional<String> tokenOptional = p1049Manager.getLoginToken(signalBrandPort, address);
        if (!tokenOptional.isPresent()) {
            return JsonResult.error("客户端尚未登录，无法发送的数据-" + object);
        }

        //获取地址,20220926 必须传入数据IP地址，而非根据品牌获取
        /*
        Optional<String> ipOptional = p1049Manager.getLoginAddress(signalBrandPort);
        if (!ipOptional.isPresent()) {
            return JsonResult.error("客户端获取地址失败，无法发送的数据-" + object);
        }
        */
        Optional<String> ipOptional = Optional.of(address);

        //获取对象中名称，首字母小写
        String simpleName = (clazz.getSimpleName()); //toLowerCaseFirstOne

        Optional<BaseOperation1049> baseOperation1049Optional = BaseOperation1049.buildBaseOperation1049(simpleName, object);
        if (!baseOperation1049Optional.isPresent()) {
            return JsonResult.error("尚不支持发送的数据-" + object);
        }

        BaseOperation1049 operation1049 = baseOperation1049Optional.get();
        operation1049.setOrder("1");
        operation1049.setName(operationName.name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 baseMessage1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        baseMessage1049.setSignalBrandPortOptional(Optional.of(signalBrandPort));

        String token = tokenOptional.get();
        //设置头数据项
        P1049Utils.setBaseHeader1049(baseMessage1049, p1049Configure, token, messageType
                , getSeq());
        //设置发送目的
        baseMessage1049.setAddress(baseMessage1049.getAddress());
        //数据发送
        InterProtocol interProtocol = P1049Utils.buildMessageSimu(ipOptional.get(), 0, InterProtocolType.P1049_MSG
                , baseMessage1049, waitAck, "", timeOutSecond, content);

        if (waitAck) {
            /**发送报文*/
            Optional<InvokeFuture> invokeFuture = messagePublisher.sendMessageOuter(interProtocol);
            if (invokeFuture.isPresent()) {
                InvokeFuture future = invokeFuture.get();
                try {
                    LocalDateTime startTime = LocalDateTime.now();
                    log.debug("开始等待-{}应答-{}", JSONObject.toJSONString(baseMessage1049), startTime);
                    ResponseMessage response = future.waitResponse();
                    LocalDateTime endTime = LocalDateTime.now();
                    long millis = Duration.between(endTime, startTime).toMillis();
                    log.debug("请求数据-{} \n 收到了应答-{}, \n 花费时间-{}, \n 报文中时间-{} \n" +
                                    "数据应答项-{}", JSONObject.toJSONString(baseMessage1049)
                            , endTime, millis,
                            System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
                    /**解析应答数据项*/
                    if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                        return JsonResult.error(response.getResponseStatus().des());
                    } else {
                        return new JsonResult<>(true, baseMessage1049 + "应答花费时间" + millis + "ms",
                                response.getResponseObject());
                    }
                } catch (InterruptedException e) {
                    log.error("等待异常", e);
                    return JsonResult.error("等待应答出现异常");
                }
            } else {
                return JsonResult.error("数据总线发送出现异常");
            }
        } else {
            messagePublisher.sendMessageOuter(interProtocol);
            return new JsonResult(true, " 发送成功", baseMessage1049);
        }
    }
}
