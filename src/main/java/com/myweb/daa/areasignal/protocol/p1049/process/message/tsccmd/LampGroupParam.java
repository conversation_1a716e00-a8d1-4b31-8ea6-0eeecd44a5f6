package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/15
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("LampGroupParam")
public class LampGroupParam implements DataIndexAble{

    @XStreamAlias("CrossID")
    private String crossId;

    @XStreamAlias("LampGroupNo")
    private Integer lampGroupNo;

    @XStreamAlias("Direction")
    private Integer direction;

    @XStreamAlias("Type")
    private String type;

    @Override
    public String dataID() {
        return crossId;
    }

    @Override
    public String dataNo() {
        return String.valueOf(lampGroupNo);
    }
}
