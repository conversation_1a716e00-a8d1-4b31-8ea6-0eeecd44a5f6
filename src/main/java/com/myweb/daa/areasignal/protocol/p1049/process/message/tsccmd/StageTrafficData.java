package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 15:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageTrafficData")
public class StageTrafficData implements DataIndexAble{
    private String CrossID;

    private String StartTime;

    private String EndTime;

    private String StageNo;


    @XStreamAlias("DataList")
    private StageTrafficDataLaneList StageTrafficDataLaneList;

    @Override
    public String dataID() {
        return "CrossID";
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
