package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SysInfo
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 14:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SysInfo")
public class SysInfo implements DataIndexAble{
    private String SysName;
    private String SysVersion;
    private String Supplier;

    private RegionIDList RegionIDList;

    @XStreamAlias("RouteIDList")
    private RouteIDList routeIDList;

    @XStreamAlias("SubRegionIDList")
    private SubRegionIDList subRegionIDList;

    private SignalControllerIDList SignalControllerIDList;

    @Override
    public String dataID() {
        return "0";
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
