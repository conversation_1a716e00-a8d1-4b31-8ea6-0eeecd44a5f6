package com.myweb.daa.areasignal.protocol.p1049.process.message;

/**
 * @ClassName: CmdProcessAble
 * @Description: 接收处理支持
 * @Author: king
 * @CreateDate: 2019/11/13 9:59
 */
public interface CmdProcessAble {
    /**
     * 获取处理关键字
     * 操作类型
     *
     * @return
     */
    OperationName getOperationName();

    /**
     * 获取处理关键字
     * 内部数据类型
     *
     * @return
     */
    String getCmdObjectName();

    /**
     * 命令处理函数
     *
     * @param object
     */
    void processRequest(Object object, BaseMessage1049 baseMessage1049);

    /**
     * 命令处理函数
     *
     * @param object
     */
    void processResponse(Object object, BaseMessage1049 baseMessage1049);

    /**
     * 命令处理函数
     *
     * @param object
     */
    void processError(Object object, BaseMessage1049 baseMessage1049);

    /**
     * 命令处理函数
     *
     * @param object
     */
    void processPush(Object object, BaseMessage1049 baseMessage1049);


}
