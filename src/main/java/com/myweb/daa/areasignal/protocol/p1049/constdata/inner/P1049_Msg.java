package com.myweb.daa.areasignal.protocol.p1049.constdata.inner;

import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.netty.ChannelHolder;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.constdata.CommonConst;
import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.common.message.AbstractMessageType;
import com.myweb.daa.areasignal.protocol.common.message.AbstractProtocolMessage;
import com.myweb.daa.areasignal.protocol.p1049.constdata.P1049Type;
import com.myweb.daa.areasignal.protocol.p1049.message.P1049Message;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.util.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Optional;

/**
 * @ClassName: P1049_Login
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:04
 */
@Component
@Slf4j
public class P1049_Msg extends AbstractMessageType {

    @Autowired
    private P1049Configure p1049Configure;

    public P1049_Msg() {
        super(P1049Type.P1049_MSG.name(), P1049Type.P1049_MSG.value(), CommonConst.CHANGE_ABLE_LENGTH,
                Optional.of(InterProtocolType.P1049_MSG),
                ProtocolType.P1049_SIGNAL);
    }

    @Override
    public Optional<Object> toInner(AbstractProtocolMessage abstractProtocolMessage) {
        if (abstractProtocolMessage instanceof P1049Message) {
            P1049Message p1049Msg = (P1049Message) abstractProtocolMessage;
            try {
                String str = new String(p1049Msg.getBody(), p1049Configure.getCodec());
                log.debug("******收到数据 \n {}", str);

                Optional<BaseMessage1049> baseMessage1049 = XmlUtils.xml2Object(str, BaseMessage1049.class);
                if (baseMessage1049.isPresent()) {
                    if (baseMessage1049.get().getBody() == null
                            || baseMessage1049.get().getBody().getOperation().isEmpty()) {
                        log.error("命令没有具体参数-{}", baseMessage1049.get());
                    } else {
                        //设置业务关联属性
                        String address = ChannelHolder.getIp(ChannelHolder.getRemoteAddress(abstractProtocolMessage.getCtx())).get();
                        baseMessage1049.get().setAddress(address);
                        baseMessage1049.get().setOrignal(str);
                        int lcoalPort = ChannelHolder.getLocalPort(abstractProtocolMessage.getCtx().channel());
                        int remotePort = ChannelHolder.getRemotePort(abstractProtocolMessage.getCtx().channel());
                        baseMessage1049.get().setSignalBrandPortOptional(SignalBrandPort.getType(lcoalPort, remotePort));
                        return Optional.of(baseMessage1049.get());
                    }
                } else {
                    log.warn("{}-命令转换失败了", abstractProtocolMessage);
                }
            } catch (UnsupportedEncodingException e) {
                log.error("数据解码异常-", e);
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<AbstractProtocolMessage> toOutter(Object object) {
        if (object instanceof String) {
            //转换成功
            P1049Message p1049Message = new P1049Message();
            try {
                p1049Message.setBody(((String) object).getBytes(p1049Configure.getCodec()));
            } catch (UnsupportedEncodingException e) {
                log.error("生成报文数据异常-{}", object, e);
            }
            log.debug("******准备发送数据-\n{}", object);
            return Optional.of(p1049Message);
        } else {
            //转换失败
            return Optional.empty();
        }
    }


}
