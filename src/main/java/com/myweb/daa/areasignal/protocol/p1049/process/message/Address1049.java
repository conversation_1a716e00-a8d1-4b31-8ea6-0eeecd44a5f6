package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: Address1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Address")
public class Address1049 {
    @XStreamAlias("Sys")
    private String Sys;
    @XStreamAlias("SubSys")
    private SubSys1049 SubSys;
    @XStreamAlias("Instance")
    private Instance1049 Instance;
}