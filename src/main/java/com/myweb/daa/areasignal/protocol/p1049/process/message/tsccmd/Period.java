package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 16:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Period")
@Slf4j
public class Period implements Serializable {
    private String StartTime;
    private String PlanNo;
    private String CtrlMode;
}
