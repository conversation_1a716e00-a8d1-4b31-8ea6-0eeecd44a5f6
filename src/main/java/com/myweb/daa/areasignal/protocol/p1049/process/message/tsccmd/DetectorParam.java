package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message
 * @author: whr
 * @description:
 * @date: 2024/7/20
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("DetectorParam")
public class DetectorParam implements DataIndexAble{

    @XStreamAlias("CrossID")
    private String crossId;

    @XStreamAlias("DetectorNo")
    private Integer detectorNo;

    @XStreamAlias("Type")
    private Integer type;

    @XStreamAlias("Position")
    private Integer position;

    @XStreamAlias("Target")
    private String target;

    @XStreamAlias("Distance")
    private Integer distance;

    @XStreamAlias("LaneNoList")
    private LaneNoList laneNoList;

    @XStreamAlias("PedestrianNoList")
    private PedestrianNoList pedestrianNoList;

    @Override
    public String dataID() {
        return crossId;
    }

    @Override
    public String dataNo() {
        return String.valueOf(detectorNo);
    }
}
