package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: CrossIDList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossIDList")
public class CrossIDList {
    @XStreamImplicit(itemFieldName = "CrossID")
    private List<String> CrossID;
}
