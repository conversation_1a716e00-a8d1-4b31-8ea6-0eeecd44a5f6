package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/27
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("RoadSectionSpeedList")
public class RoadSectionSpeedList {

    @XStreamImplicit(itemFieldName = "RoadSectionSpeed")
    private List<RoadSectionSpeed> roadSectionSpeed;
}
