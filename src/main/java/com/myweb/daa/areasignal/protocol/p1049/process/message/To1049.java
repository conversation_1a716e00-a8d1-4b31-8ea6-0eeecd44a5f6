package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: To1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("To")
public class To1049 {
    @XStreamAlias("Address")
    private Address1049 address;
}
