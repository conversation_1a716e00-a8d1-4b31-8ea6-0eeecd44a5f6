package com.myweb.daa.areasignal.protocol.p1049.util;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.Dom4JDriver;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.security.AnyTypePermission;
import lombok.extern.slf4j.Slf4j;

import java.io.Writer;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: XmlUtils
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 14:09
 */
@Slf4j
public class XmlUtils {
    protected static String PREFIX_CDATA = "<![CDATA[";
    protected static String SUFFIX_CDATA = "]]>";

    protected static Map<String, XStream> xml2Object_XStream_map = new ConcurrentHashMap<>();
    protected static Map<String, XStream> object2Xml_XStream_map = new ConcurrentHashMap<>();

    /**
     * xml字符数据项转化到object对象
     *
     * @param xml
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> Optional<T> xml2Object(String xml, Class<?> clazz) {
        try {
            XStream xstream = null;
            if (!xml2Object_XStream_map.containsKey(clazz.getName())) {
                xstream = new XStream();
                XStream.setupDefaultSecurity(xstream);
                //当未指定classloader的时候，spring boot未正确使用classloader
                xstream.setClassLoader(clazz.getClassLoader());
                //应用clazz类的注解
                xstream.processAnnotations(clazz);
                //自动检测注解
                xstream.autodetectAnnotations(true);
                //添加参数
                xstream.addPermission(AnyTypePermission.ANY);
                xstream.ignoreUnknownElements();
                xml2Object_XStream_map.put(clazz.getName(), xstream);
            } else {
                xstream = xml2Object_XStream_map.get(clazz.getName());
            }
            return Optional.of((T) xstream.fromXML(xml));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("xml2Object error-{}-{}", xml, e);
        }
        return Optional.empty();
    }

    /**
     * object转化为xml字符数据项
     *
     * @param object
     * @return
     */
    public static Optional<String> object2Xml(Object object) {
        try {
            XStream xstream = null;
            if (!object2Xml_XStream_map.containsKey(object.getClass().getName())) {
                xstream = new XStream(new Dom4JDriver() {
                    @Override
                    public HierarchicalStreamWriter createWriter(Writer out) {
                        return new PrettyPrintWriter(out) {
                            @Override
                            protected void writeText(QuickWriter writer, String text) {
                                if (text.startsWith(PREFIX_CDATA) && text.endsWith(SUFFIX_CDATA)) {
                                    writer.write(text);
                                } else {
                                    super.writeText(writer, text);
                                }
                            }
                        };
                    }
                });
                xstream.processAnnotations(object.getClass());
                object2Xml_XStream_map.put(object.getClass().getName(), xstream);
            } else {
                xstream = object2Xml_XStream_map.get(object.getClass().getName());
            }
            return Optional.of(getFullMsg(xstream.toXML(object)).replaceAll("  ", " ").replace("\n", ""));
        } catch (Exception e) {
            log.error("object2Xml error", e);
        }
        return Optional.empty();
    }

    /**
     * 添加报文数据头，字符转义
     *
     * @param body
     * @return
     */
    public static String getFullMsg(String body) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + body.replace("__", "_");
    }
}
