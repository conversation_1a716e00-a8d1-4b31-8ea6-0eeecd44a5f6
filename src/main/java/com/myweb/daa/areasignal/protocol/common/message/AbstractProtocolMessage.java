package com.myweb.daa.areasignal.protocol.common.message;

import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import io.netty.channel.ChannelHandlerContext;
import lombok.Data;

/**
 * @ClassName: AbstractProtocolMessage
 * @Description: 抽象类，外部协议报文需要实现此接口
 * @Author: king
 * @CreateDate: 2018/12/3 19:00
 */
@Data
public abstract class AbstractProtocolMessage {
    /**
     * 报文类别
     */
    private ProtocolType protocolType;

    /**
     * 报文处理
     */
    private ChannelHandlerContext ctx;

    public AbstractProtocolMessage(ProtocolType protocolType) {
        this.protocolType = protocolType;
    }

    /**
     * 获取正文数据
     *
     * @return
     */
    public abstract byte[] getBody();

    /**
     * 获取报文编号
     *
     * @return
     */
    public abstract short getMessageID();

}
