package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/4/26 14:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageCtrl")
public class StageCtrl implements Serializable {
    private String CrossID;
    private String StageNo;
    private Integer Green;

    /**
     * 华通数据项 设置类型 1:延长,2：缩短,0：失败
     */
    private String Oper;
    /**
     * 当前阶段延长缩短时长
     */
    private Integer TimeLen;
}
