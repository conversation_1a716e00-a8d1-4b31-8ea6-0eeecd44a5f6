package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/21
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SignalGroupParam")
public class SignalGroupParam implements DataIndexAble{

    @XStreamAlias("CrossID")
    private String crossId;

    @XStreamAlias("SignalGroupNo")
    private Integer signalGroupNo;

    @XStreamAlias("Name")
    private String name;

    @XStreamAlias("GreenFlashLen")
    private Integer greenFlashLen;

    @XStreamAlias("MaxGreen")
    private Integer maxGreen;

    @XStreamAlias("MinGreen")
    private Integer minGreen;

    @XStreamAlias("LampGroupNoList")
    private LampGroupNoList lampGroupNoList;

    @Override
    public String dataID() {
        return crossId;
    }

    @Override
    public String dataNo() {
        return String.valueOf(signalGroupNo);
    }
}
