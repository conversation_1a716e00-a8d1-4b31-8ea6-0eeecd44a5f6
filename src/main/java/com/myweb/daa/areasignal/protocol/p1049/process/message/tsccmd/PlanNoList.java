package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: PlanNoList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 16:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("PlanNoList")
public class PlanNoList {
    @XStreamImplicit(itemFieldName = "PlanNo")
    private List<String> PlanNo;
}
