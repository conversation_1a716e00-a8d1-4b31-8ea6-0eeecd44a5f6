package com.myweb.daa.areasignal.protocol.p1049.process.message.object;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SDO_User1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SDO_Success")
public class SDO_Success {
    @XStreamAlias("SuccessObj")
    private String SuccessObj;
    @XStreamAlias("SuccessDesc")
    private String SuccessDesc;
}

