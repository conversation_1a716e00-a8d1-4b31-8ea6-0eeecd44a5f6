package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: RegionIDList
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 14:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("RegionIDList")
public class RegionIDList {
    @XStreamImplicit(itemFieldName = "RegionID")
    private List<String> RegionID;
    @XStreamImplicit(itemFieldName = "QYBH")
    private List<String> QYBH;
}
