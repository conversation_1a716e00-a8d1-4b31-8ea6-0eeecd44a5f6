package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_HeartBeat;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class HeartBeat1049Process implements CmdProcessAble {
    @Autowired
    private P1049Manager p1049Manager;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private MessageOuterPublisher messagePublisher;

    @Autowired
    private MessageSendProcess messageSendProcess;

    @Override
    public OperationName getOperationName() {
        return OperationName.Notify;
    }

    @Override
    public String getCmdObjectName() {
        return SDO_HeartBeat.class.getName();
    }

    @Override
    public void processRequest(Object object, BaseMessage1049 baseMessage1049) {
        processPush(object, baseMessage1049);
    }

    @Override
    public void processResponse(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 response");
    }

    @Override
    public void processError(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 error");
    }

    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildHeartBeat() {
        //构造内部数据项
        SDO_HeartBeat hb = new SDO_HeartBeat();
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_HeartBeat(hb)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 login1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return login1049;
    }


    @Override
    public void processPush(Object object, BaseMessage1049 baseMessage1049) {
        if (!(object instanceof SDO_HeartBeat)) {
            log.error("HeartBeat1049Process，不是SDO_HeartBeat类型");
            return;
        }
        SDO_HeartBeat sdo_heartBeat = (SDO_HeartBeat) object;
        log.error("收到{}心跳[PUSH]数据报文项-Seq-{}", baseMessage1049.getSignalBrandPortOptional().get(),
                baseMessage1049.getSeq());

        /**
         * 攸亮不能再发送，否则会导致循环应答发送
         */
        Optional<SignalBrandPort> signalBrandPortOptional = baseMessage1049.getSignalBrandPortOptional();
        if (signalBrandPortOptional.isPresent() && signalBrandPortOptional.get().brandCode() ==
                SignalBrandPort.YL.brandCode()) {
            return;
        }

        //添加新的token
        Optional<String> tokenData = p1049Manager.getToken(signalBrandPortOptional.get(), baseMessage1049.getAddress());
        if (!tokenData.isPresent()) {
            log.error("无法应答心跳，对端尚未登录-{}", p1049Manager.getTokenMap());
            return;
        }

        //设置token
        String token = baseMessage1049.getToken();
        //登录成功
        BaseMessage1049 loginSuccess = buildHeartBeat();
        //设置系统源
        loginSuccess.setSignalBrandPortOptional(baseMessage1049.getSignalBrandPortOptional());
        //设置头数据项
        P1049Utils.setBaseHeader1049(loginSuccess, p1049Configure, token, MessageType.PUSH, baseMessage1049.getSeq());
        //设置发送目的
        loginSuccess.setAddress(baseMessage1049.getAddress());
        //数据发送
        InterProtocol interProtocol = P1049Utils.buildMessage(loginSuccess.getAddress(), InterProtocolType.P1049_MSG
                , loginSuccess, false, false);
        messagePublisher.sendMessageOuter(interProtocol);

        //更新心跳时间
        p1049Manager.updateHeartBeatTime(baseMessage1049.getSignalBrandPortOptional().get(), baseMessage1049.getAddress());
    }


    /**
     * 同步发送心跳数据项
     */
    @Scheduled(initialDelay = 60000, fixedRate = 60000)
    public void syncHeartBeat() {
        log.error("当前token数据项-{}", p1049Manager.getTokenMap());
        p1049Manager.getTokenMap().keySet().forEach(
                signalBrandPort -> {
                    Map<String, P1049Manager.TokenData> stringTokenDataMap =
                            p1049Manager.getTokenMap().get(signalBrandPort);

                    stringTokenDataMap.values().forEach(
                            tokenData -> {
                                //设置token
                                String token = tokenData.getToken();
                                //登录成功
                                BaseMessage1049 loginSuccess = buildHeartBeat();
                                //设置系统源
                                loginSuccess.setSignalBrandPortOptional(Optional.of(signalBrandPort));
                                //设置头数据项
                                P1049Utils.setBaseHeader1049(loginSuccess, p1049Configure, token, MessageType.PUSH,
                                        messageSendProcess.getSeq());
                                //设置发送目的
                                loginSuccess.setAddress(tokenData.getAddress());
                                //数据发送
                                InterProtocol interProtocol = P1049Utils.buildMessage(loginSuccess.getAddress(), InterProtocolType.P1049_MSG
                                        , loginSuccess, false, false);
                                messagePublisher.sendMessageOuter(interProtocol);
                                log.error("发送心跳{}-{}数据项-Seq-{}", signalBrandPort.name(), tokenData.getAddress(),
                                        loginSuccess.getSeq());
                            }
                    );

                }
        );
    }


}
