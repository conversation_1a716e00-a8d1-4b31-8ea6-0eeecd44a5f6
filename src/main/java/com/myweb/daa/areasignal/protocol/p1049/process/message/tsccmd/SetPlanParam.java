package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SetPlanParam")
public class SetPlanParam {

    /**
     * 设置类型（1：新增，2：修改，3：删除）
     */
    private String Oper;

    @XStreamAlias("StageParamList")
    private StageParamList stageParamList;

    private PlanParam PlanParam;
}
