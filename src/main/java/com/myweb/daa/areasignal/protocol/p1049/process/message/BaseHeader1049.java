package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import lombok.Data;

import java.util.Optional;

/**
 * @ClassName: BaseHeader1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 13:13
 */
@Data
public abstract class BaseHeader1049 {
    @XStreamAlias("Version")
    protected String version;
    @XStreamAlias("Token")
    protected String token;
    @XStreamAlias("From")
    protected From1049 from;
    @XStreamAlias("To")
    protected To1049 to;
    @XStreamAlias("Type")
    protected String type;
    @XStreamAlias("Seq")
    protected String seq;
    /**
     * address
     */
    @XStreamOmitField
    protected String address;

    /**
     * 用来区分1049数据源，信号机类型
     */
    @XStreamOmitField
    protected Optional<SignalBrandPort> signalBrandPortOptional;

    @XStreamOmitField
    protected String orignal;

}
