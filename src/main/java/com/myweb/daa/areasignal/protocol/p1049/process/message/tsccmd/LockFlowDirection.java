package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: LockFlowDirection
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("LockFlowDirection")
public class LockFlowDirection {
    private String CrossID;
    private String Type;
    private String Entrance;
    private String Exit;
    private String LockType;
    private String Duration;
}
