package com.myweb.daa.areasignal.protocol.p1049.process.message;


import com.myweb.daa.areasignal.protocol.p1049.process.message.object.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.*;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;

import java.beans.Introspector;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName: BaseOperation1049
 * @Description: !!!!!!!!!!!!!!!!!!!!!!
 * !!!!!!!!!!!!!!!!!!!!!!
 * 不要在此类中随便添加函数，
 * 不要随便添加无用的参数，
 * 否则会导致数据报文解析的异常
 *
 * list数据不要增加s
 *
 * !!!!!!!!!!!!!!!!!!!!!!
 * !!!!!!!!!!!!!!!!!!!!!!
 *
 * 2025.4.3 所有参数均 设置为list类型，方便统一处理
 * @Author: king
 * @CreateDate: 2019/11/5 13:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Operation")
@Slf4j
public class BaseOperation1049 {
    @XStreamAsAttribute()
    protected String order;
    @XStreamAsAttribute()
    protected String name;
    @XStreamAlias("SDO_User")
    private SDO_User1049 sDO_User1049;
    @XStreamAlias("SDO_Error")
    private SDO_Error1049 sDO_Error1049;
    @XStreamAlias("SDO_MsgEntity")
    private SDO_MsgEntity sDO_MsgEntity;
    @XStreamAlias("SDO_HeartBeat")
    private SDO_HeartBeat sDO_HeartBeat;
    @XStreamAlias("SDO_TimeOut")
    private SDO_TimeOut sDO_TimeOut;
    @XStreamImplicit(itemFieldName = "SDO_Success")
    private List<SDO_Success> sDO_Success;
    @XStreamAlias("SDO_TimeServer")
    private SDO_TimeServer sDO_TimeServer;
    @XStreamImplicit(itemFieldName = "TSCCmd")
    private List<TSCCmd> tSCCmd;

    /**
     * 系统参数
     */
    @XStreamImplicit(itemFieldName = "SysInfo")
    private List<SysInfo> sysInfo;

    /**
     * 区域参数
     */
    @XStreamImplicit(itemFieldName = "RegionParam")
    private List<RegionParam> regionParam;

    /**
     * 线路参数
     */
    @XStreamImplicit(itemFieldName = "RouteParam")
    private List<RouteParam> routeParam;

    /**
     * 子区参数
     */
    @XStreamImplicit(itemFieldName = "SubRegionParam")
    private List<SubRegionParam> subRegionParam;

    @XStreamImplicit(itemFieldName = "CrossParam")
    private List<CrossParam> crossParam;

    @XStreamImplicit(itemFieldName = "SignalController")
    private List<SignalController> signalController;

    @XStreamImplicit(itemFieldName = "LampGroupParam")
    private List<LampGroupParam> lampGroupParam;

    @XStreamImplicit(itemFieldName = "DetectorParam")
    private List<DetectorParam> detectorParam;

    @XStreamImplicit(itemFieldName = "LaneParam")
    private List<LaneParam> laneParam;

    @XStreamImplicit(itemFieldName = "PedestrianParam")
    private List<PedestrianParam> pedestrianParam;

    @XStreamImplicit(itemFieldName = "SignalGroupParam")
    private List<SignalGroupParam> signalGroupParam;

    @XStreamImplicit(itemFieldName = "StageParam")
    private List<StageParam> stageParam;

    @XStreamImplicit(itemFieldName = "PlanParam")
    private List<PlanParam> planParam;

    @XStreamImplicit(itemFieldName = "DayPlanParam")
    private List<DayPlanParam> dayPlanParam;

    @XStreamImplicit(itemFieldName = "ScheduleParam")
    private List<ScheduleParam> scheduleParam;

    @XStreamImplicit(itemFieldName = "SysState")
    private List<SysState> sysState;

    @XStreamImplicit(itemFieldName = "CrossState")
    private List<CrossState> crossState;

    @XStreamImplicit(itemFieldName = "SignalControllerError")
    private List<SignalControllerError> signalControllerError;

    @XStreamImplicit(itemFieldName = "CrossCtrlInfo")
    private List<CrossCtrlInfo> crossCtrlInfo;

    @XStreamImplicit(itemFieldName = "CrossCycle")
    private List<CrossCycle> crossCycle;

    @XStreamImplicit(itemFieldName = "CrossStage")
    private List<CrossStage> crossStage;

    @XStreamImplicit(itemFieldName = "CrossSignalGroupStatus")
    private List<CrossSignalGroupStatus> crossSignalGroupStatus;

    @XStreamImplicit(itemFieldName = "CrossTrafficData")
    private List<CrossTrafficData> crossTrafficData;

    @XStreamImplicit(itemFieldName = "StageTrafficData")
    private List<StageTrafficData> stageTrafficData;

    @XStreamImplicit(itemFieldName = "VarLaneStatus")
    private List<VarLaneStatus> varLaneStatus;

    @XStreamImplicit(itemFieldName = "RouteCtrlInfo")
    private List<RouteCtrlInfo> routeCtrlInfo;

    @XStreamImplicit(itemFieldName = "RouteSpeed")
    private List<RouteSpeed> routeSpeed;

    @XStreamImplicit(itemFieldName = "SCDoorStatus")
    private List<SCDoorStatus> sCDoorStatus;

    @XStreamImplicit(itemFieldName = "LockFlowDirection")
    private List<LockFlowDirection> lockFlowDirection;

    @XStreamImplicit(itemFieldName = "UnLockFlowDirection")
    private List<UnLockFlowDirection> unLockFlowDirection;

    @XStreamImplicit(itemFieldName = "CrossReportCtrl")
    private List<CrossReportCtrl> crossReportCtrl;

    @XStreamImplicit(itemFieldName = "CenterPlan")
    private List<CenterPlan> centerPlan;

    @XStreamImplicit(itemFieldName = "SetPlanParam")
    private List<SetPlanParam> setPlanParam;

    @XStreamImplicit(itemFieldName = "SetDayPlanParam")
    private List<SetDayPlanParam> setDayPlanParam;

    @XStreamImplicit(itemFieldName = "SetScheduleParam")
    private List<SetScheduleParam> setScheduleParam;

    @XStreamImplicit(itemFieldName = "AdjustStage")
    private List<AdjustStage> adjustStage;

    @XStreamImplicit(itemFieldName = "CtrlVarLane")
    private List<CtrlVarLane> ctrlVarLane;

    @XStreamImplicit(itemFieldName = "CrossRunInfoRetrans")
    private List<CrossRunInfoRetrans> crossRunInfoRetrans;


    /**
     * 调用所有对象的get函数，找到不是null的数据项
     *
     * @return
     */
    public Optional<Object> getNonNullFiled() {
        final ArrayList<Object> objects = new ArrayList<>();
        Arrays.asList(this.getClass().getDeclaredMethods()).stream().forEach(
                method -> {
                    try {
                        /**不是无参的函数不调用*/
                        if (method.getParameterCount() != 0) {
                            return;
                        }
                        /**此函数本身不调用*/
                        String methodName = method.getName();
                        if (methodName.endsWith("getNonNullFiled")
                                || methodName.endsWith("getBaseOperationData")
                                || methodName.endsWith("buildBaseOperation1049")
                                || methodName.endsWith("getOrder")
                                || methodName.endsWith("getName")) {
                            return;
                        }
                        if (!methodName.startsWith("get")) {
                            return;
                        }
                        Object object = method.invoke(this);
                        if (object != null) {
                            objects.add(object);
                        }
                    } catch (IllegalAccessException e) {
                        log.error("get函数异常", e);
                    } catch (InvocationTargetException e) {
                        log.error("get函数异常", e);
                    }
                }
        );
        if (objects.size() > 1 || objects.size() == 0) {
            //log.error("异常的报文数据项-{}", this);
        }
        return objects.stream().findAny();
    }


    /**
     * 获取不为null的实际数据项
     */
    public Optional<Object> getBaseOperationData() {
        return getNonNullFiled();
    }


    /**
     * 設置数据项
     *
     * @param object
     * @return
     */
    public static Optional<BaseOperation1049> buildBaseOperation1049(String name, Object object) {
        BaseOperation1049 baseOperation1049 = BaseOperation1049.builder().build();
        try {
            //注意此处Introspector.decapitalize(name)，查询beansUtils中,在首字母、后续字母均为大写的情况下，他的name应该保持不变
            //否则均为首字母小写，这样可以正确处理设置数据项
            //归根结底还是因为1049中名称定义与java名称定义的不一致导致的
            BeanUtils.setProperty(baseOperation1049, Introspector.decapitalize(name), object);
            return Optional.of(baseOperation1049);
        } catch (Exception e) {
            log.error("buildBaseOperation1049出现异常-{}-{}-{}", name, object, e);
        }
        return Optional.empty();
    }

    /**
     * 設置数据项
     *
     * @param object
     * @return
     */
    public static Optional<BaseOperation1049> buildBaseOperation1049_old(Object object) {
        BaseOperation1049 baseOperation1049 = BaseOperation1049.builder().build();
        Method[] declaredMethods = baseOperation1049.getClass().getDeclaredMethods();
        for (int i = 0; i < declaredMethods.length; i++) {
            Method method = declaredMethods[i];
            try {
                /**不是无参的函数不调用*/
                if (method.getParameterCount() != 1) {
                    continue;
                }
                /**此函数本身不调用*/
                String methodName = method.getName();
                if (methodName.endsWith("buildBaseOperation1049")) {
                    continue;
                }
                if (!methodName.startsWith("set")) {
                    continue;
                }

                Class<?>[] parameterTypes = method.getParameterTypes();
                if (parameterTypes.length != 1) {
                    continue;
                }

                if (parameterTypes[0].equals(List.class)) {
                    //针对List数据项,获取泛型数据项
                    Type[] types = method.getGenericParameterTypes();
                    //Now assuming that the first parameter to the method is of type List<Integer>
                    ParameterizedType pType = (ParameterizedType) types[0];
                    Class<?> clazz = (Class<?>) pType.getActualTypeArguments()[0];
                    if (!clazz.getName().equals(object.getClass().getName())) {
                        continue;
                    }
                } else {
                    if (!parameterTypes[0].getName().equals(object.getClass().getName())) {
                        continue;
                    }
                }
                method.invoke(baseOperation1049, object);
                return Optional.of(baseOperation1049);
            } catch (Exception e) {
                log.error("set函数异常-{}-{}", e, object);
            }
        }

        return Optional.empty();
    }
}
