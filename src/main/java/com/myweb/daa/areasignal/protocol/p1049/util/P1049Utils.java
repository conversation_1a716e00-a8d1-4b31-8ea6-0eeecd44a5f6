package com.myweb.daa.areasignal.protocol.p1049.util;


import com.myweb.daa.areasignal.business.bean.LinkStatus;
import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.AckManager.NeedAck;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.common.OuterProtocolType;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;

import java.util.Optional;
import java.util.Random;

/**
 * @ClassName: P1049Utils
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/6 13:06
 */
public class P1049Utils {
    private P1049Utils() {

    }

    /**
     * 设置1049头数据项
     *
     * @param baseHeader1049
     * @param p1049Configure
     * @param token
     * @param type
     * @param seq
     */
    public static void setBaseHeader1049(BaseHeader1049 baseHeader1049,
                                         P1049Configure p1049Configure,
                                         String token,
                                         MessageType type,
                                         String seq) {
        //获取系统配置项
        P1049Configure.SysConfig sysConfig = p1049Configure.getSysConfig(baseHeader1049.getSignalBrandPortOptional());

        //版本
        baseHeader1049.setVersion(sysConfig.getVersion());
        //token
        baseHeader1049.setToken(token);
        //源端数据项
        Address1049 from = new Address1049();
        from.setInstance(new Instance1049());
        from.setSubSys(new SubSys1049());
        from.setSys(sysConfig.getFromSys());
        From1049 from1049 = new From1049();
        from1049.setAddress(from);
        baseHeader1049.setFrom(from1049);
        //目的端数据项
        Address1049 to = new Address1049();
        to.setInstance(new Instance1049());
        to.setSubSys(new SubSys1049());
        to.setSys(sysConfig.getToSys());
        To1049 to1049 = new To1049();
        to1049.setAddress(to);
        baseHeader1049.setTo(to1049);
        //类型
        baseHeader1049.setType(type.name());
        //序列号
        baseHeader1049.setSeq(seq);
    }

    public static Random r = new Random(System.currentTimeMillis());

    /**
     * 生成随机的10位Token字符串
     *
     * @return
     */
    public static String getRandom() {
        long num = Math.abs(r.nextLong() % 10000000000L);
        String s = String.valueOf(num);
        for (int i = 0; i < 10 - s.length(); i++) {
            s = "0" + s;
        }
        return s;
    }

    /**
     * 构造数据报文
     *
     * @param address
     * @param interProtocolType
     * @param data
     * @param waitAck
     * @param timeOutSecond
     * @return
     */
    public static InterProtocol buildMessage(String address, InterProtocolType interProtocolType,
                                             Object data, boolean waitAck, int timeOutSecond, boolean isSimuSend ) {
        if (data instanceof NeedAck && waitAck) {
            NeedAck needAck = (NeedAck) data;
            return buildMessage(address, 0,
                    interProtocolType, data, true, needAck.getAckKey(), timeOutSecond, isSimuSend);
        } else {
            return buildMessage(address, 0,
                    interProtocolType, data, false, "", timeOutSecond, isSimuSend);
        }
    }

    /**
     * 构造数据项报文
     *
     * @param address
     * @param interProtocolType
     * @param data
     * @return
     */
    public static InterProtocol buildMessage(String address, InterProtocolType interProtocolType,
                                             Object data, boolean waitAck, boolean isSimuSend) {
        //默认应答5秒
        return buildMessage(address, interProtocolType, data, waitAck, 5, isSimuSend);
    }

    /**
     * @param address
     * @param interProtocolType
     * @param data
     * @param needAck
     * @return
     */
    public static InterProtocol buildMessage(String address, int port, InterProtocolType interProtocolType,
                                             Object data, boolean needAck, String ackKey, int timeOutSecond, boolean isSimuSend) {
        InterProtocol interProtocol = new InterProtocol();
        interProtocol.setOuterIp(address);
        interProtocol.setOuterPort(port);
        interProtocol.setInterProtocolType(interProtocolType);
        interProtocol.setOuterProtocolType(String.valueOf(OuterProtocolType.P1049_SIGNAL.value()));
        interProtocol.setNeedAck(needAck);
        interProtocol.setAckKey(ackKey);
        interProtocol.setTimeOutSeconds(timeOutSecond);
        interProtocol.setSimuSend(isSimuSend);
        Optional<String> object2Xml = XmlUtils.object2Xml(data);
        if (object2Xml.isPresent()) {
            interProtocol.setJsonString(object2Xml.get());
        }
        return interProtocol;
    }

    /**
     * 只用于测试发送body数据项
     *
     * @param address
     * @param interProtocolType
     * @param data
     * @param needAck
     * @return
     */
    public static InterProtocol buildMessageSimu(String address, int port, InterProtocolType interProtocolType,
                                                 Object data, boolean needAck, String ackKey, int timeOutSecond, String content) {
        InterProtocol interProtocol = new InterProtocol();
        interProtocol.setOuterIp(address);
        interProtocol.setOuterPort(port);
        interProtocol.setInterProtocolType(interProtocolType);
        interProtocol.setOuterProtocolType(String.valueOf(OuterProtocolType.P1049_SIGNAL.value()));
        interProtocol.setNeedAck(needAck);
        interProtocol.setAckKey(ackKey);
        interProtocol.setTimeOutSeconds(timeOutSecond);
        Optional<String> object2Xml = XmlUtils.object2Xml(data);
        if (object2Xml.isPresent()) {
            interProtocol.setJsonString(object2Xml.get().replace("<Operation order=\"1\" name=\"Get\"/>", content));
        }
        return interProtocol;
    }


    /**
     * 从1049中状态获取变为本地状态数据
     *
     * @param value
     * @return
     */
    public static LinkStatus getLinkStatus(String value) {
        LinkStatus linkStatus = LinkStatus.HANDSHAKE;
        if (value.compareToIgnoreCase("Offline") == 0) {
            linkStatus = LinkStatus.UNLINK;
        }
        return linkStatus;
    }

}
