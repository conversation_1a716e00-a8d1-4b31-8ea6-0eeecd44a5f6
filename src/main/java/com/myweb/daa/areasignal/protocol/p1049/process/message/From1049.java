package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: From1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 11:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("From")
public class From1049 {
    @XStreamAlias("Address")
    private Address1049 address;
}