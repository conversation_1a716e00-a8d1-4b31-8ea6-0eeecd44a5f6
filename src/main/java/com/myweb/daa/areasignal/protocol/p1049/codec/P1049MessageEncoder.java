/*
 * Copyright 2013-2018 Lilinfeng.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.myweb.daa.areasignal.protocol.p1049.codec;


import com.myweb.daa.areasignal.protocol.p1049.message.P1049Message;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 数据发送处理
 * @date 2014年3月15日
 */
@Slf4j
public final class P1049MessageEncoder extends
        MessageToByteEncoder<P1049Message> {

    @Override
    protected void encode(ChannelHandlerContext ctx, P1049Message msg,
                          ByteBuf sendBuf) throws Exception {
        if (msg == null || msg.getBody() == null) {
            throw new Exception("The encode message is null");
        }

        //发送二进制数据项
        sendBuf.writeBytes(msg.getBody());
    }


}
