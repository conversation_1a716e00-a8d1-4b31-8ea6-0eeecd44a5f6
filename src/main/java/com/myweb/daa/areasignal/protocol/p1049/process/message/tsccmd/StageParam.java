package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: StageParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageParam")
public class StageParam implements Serializable, DataIndexAble {
    private String CrossID;
    private String StageNo;
    private String StageName;
    private String Attribute;
    private SignalGroupStatusList SignalGroupStatusList;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return StageNo;
    }
}
