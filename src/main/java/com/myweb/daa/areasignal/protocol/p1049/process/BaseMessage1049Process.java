package com.myweb.daa.areasignal.protocol.p1049.process;

import com.alibaba.fastjson.JSONObject;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.htbussiness.service.NotifyMessageService;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.CmdProcessAble;
import com.myweb.daa.areasignal.protocol.p1049.process.message.MessageType;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Const;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: BaseMessage1049Process
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 9:40
 */
@Component
@Slf4j
public class BaseMessage1049Process implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Autowired
    private NotifyMessageService notifyMessageService;

    /**
     * 报文类型以及转换类型之间的映射关系
     */
    private ConcurrentHashMap<String, CmdProcessAble> cmds = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void setMessagesMap() {
        //从容器中获取数据项添加到处理对象中
        Map<String, CmdProcessAble> beansOfType = applicationContext.getBeansOfType(CmdProcessAble.class);
        for (Map.Entry<String, CmdProcessAble> entry : beansOfType.entrySet()) {
            String key = entry.getKey();
            CmdProcessAble value = entry.getValue();
            cmds.put(getCmdKey(value), value);
        }
    }

    /**
     * 获取处理关键字
     *
     * @param cmdProcessAble
     * @return
     */
    public String getCmdKey(CmdProcessAble cmdProcessAble) {
        return cmdProcessAble.getOperationName().name().toLowerCase() + P1049Const.CMD_KEY_SPILLER + cmdProcessAble.getCmdObjectName();
    }

    @EventListener
    @Async(GlobalConfigure.MESSAGE_1049_PROCESS_EXECUTOR)
    public void tabRequestProcess(BaseMessage1049 baseMessage1049) {
        log.trace("get message - {}", JSONObject.toJSONString(baseMessage1049));
        Optional<SignalBrandPort> signalBrandPortOptional = baseMessage1049.getSignalBrandPortOptional();
        if (!signalBrandPortOptional.isPresent()) {
            log.error("没有找到源系统类型,登录数据无法处理");
            return;
        }
        baseMessage1049.getBody().getOperation().forEach(
                baseOperation1049 -> {
                    String optype = baseOperation1049.getName();
                    Optional<Object> optional = baseOperation1049.getBaseOperationData();
                    if (!optional.isPresent()) {
                        if (!baseMessage1049.getOrignal().contains("StageTrafficData")) {
                            log.error("基础命令项数据异常-{}", baseMessage1049.getOrignal());
                        }
                        return;
                    }
                    String cmdKey = optype.toLowerCase() + P1049Const.CMD_KEY_SPILLER + optional.get().getClass().getName();
                    CmdProcessAble cmdProcessAble = cmds.get(cmdKey);
                    if (cmdProcessAble == null) {
                        if (baseMessage1049.getType().equals(MessageType.PUSH.name())) {
                            //尝试主动推送数据处理
                            notifyMessageService.processPush(optional.get(), baseMessage1049);
                        } else {
                            log.warn("没有命令处理的函数-{}", baseOperation1049);
                        }
                    } else {
                        //检查命令类型进行数据处理
                        if (baseMessage1049.getType().equals(MessageType.REQUEST.name())) {
                            cmdProcessAble.processRequest(optional.get(), baseMessage1049);
                        } else if (baseMessage1049.getType().equals(MessageType.RESPONSE.name())) {
                            cmdProcessAble.processResponse(optional.get(), baseMessage1049);
                        } else if (baseMessage1049.getType().equals(MessageType.ERROR.name())) {
                            cmdProcessAble.processError(optional.get(), baseMessage1049);
                        } else if (baseMessage1049.getType().equals(MessageType.PUSH.name())) {
                            cmdProcessAble.processPush(optional.get(), baseMessage1049);
                        }
                    }
                }
        );
    }
}
