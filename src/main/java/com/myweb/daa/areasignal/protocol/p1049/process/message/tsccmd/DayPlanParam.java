package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 16:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("DayPlanParam")
public class DayPlanParam implements Serializable, DataIndexAble {
    private String CrossID;
    private String DayPlanNo;
    private String DayPlanName;
    private PeriodList PeriodList;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return DayPlanNo;
    }
}
