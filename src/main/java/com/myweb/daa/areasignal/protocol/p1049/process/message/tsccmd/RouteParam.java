package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/12
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("RouteParam")
public class RouteParam implements DataIndexAble{

    @XStreamAlias("RouteID")
    private String routeId;

    @XStreamAlias("RouteName")
    private String routeName;

    @XStreamAlias("Type")
    private Integer type;

    @XStreamAlias("RouteCrossList")
    private RouteCrossList routeCrossList;

    @XStreamAlias("SubRegionIDList")
    private SubRegionIDList subRegionIDList;

    @Override
    public String dataID() {
        return routeId;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
