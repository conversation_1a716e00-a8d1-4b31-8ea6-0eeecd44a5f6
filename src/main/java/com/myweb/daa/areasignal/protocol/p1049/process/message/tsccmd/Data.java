package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @ClassName: Data
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:26
 */
@lombok.Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Data")
public class Data {
    private String LaneNo;
    private String Volume;
    private String AvgVehLen;
    private String Pcu;
    private String HeadDistance;
    private String HeadTime;
    private String Speed;
    private String Saturation;
    private String Density;
    private String QueueLength;
    private String MaxQueueLength;
    private String Occupancy;
}
