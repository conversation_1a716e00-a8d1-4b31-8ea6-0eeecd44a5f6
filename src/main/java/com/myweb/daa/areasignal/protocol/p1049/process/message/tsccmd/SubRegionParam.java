package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SubRegionParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SubRegionParam")
public class SubRegionParam implements DataIndexAble{

    @XStreamAlias("SubRegionID")
    private String subRegionID;

    @XStreamAlias("SubRegionName")
    private String subRegionName;

    @XStreamAlias("CrossIDList")
    private CrossIDList crossIdList;

    @XStreamAlias("KeyCrossIDList")
    private CrossIDList keyCrossIDList;

    @Override
    public String dataID() {
        return subRegionID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
