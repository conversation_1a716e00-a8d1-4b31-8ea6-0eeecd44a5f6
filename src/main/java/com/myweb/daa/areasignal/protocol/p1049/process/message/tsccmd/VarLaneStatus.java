package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/26 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("VarLaneStatus")
public class VarLaneStatus implements DataIndexAble{

    private String CrossID;

    private String LaneNo;

    private String CurMovement;

    private String CurMode;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return LaneNo;
    }
}
