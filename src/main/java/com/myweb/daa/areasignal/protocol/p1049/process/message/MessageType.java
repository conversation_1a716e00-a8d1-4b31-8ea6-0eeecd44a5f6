package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.myweb.daa.areasignal.centralsystem.param.DirectionType1049;

/**
 * @ClassName: MessageType
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/6 13:17
 */
public enum MessageType {
    REQUEST,
    RESPONSE,
    PUSH,
    ERROR;

    public static MessageType parse(String type) {
        if (type != null) {
            for (MessageType messageType : MessageType.values()) {
                if (messageType.name().equalsIgnoreCase(type)) {
                    return messageType;
                }
            }
        }
        return MessageType.REQUEST;
    }
}
