package com.myweb.daa.areasignal.protocol.common;

/**
 * @ClassName: OuterProtocolType
 * @Description: 对应于 com.myweb.dataacquisition.protocol.common.constdata.ProtocolType 中的协议类别，只保留了id以及名称
 * @Author: king
 * @CreateDate: 2019/3/25 13:51
 */
public enum OuterProtocolType {
    /**
     * 莱斯信号机协议
     */
    LES_SIGNAL(200, "莱斯信号机协议"),

    /**
     * 1049协议
     */
    P1049_SIGNAL(201, "1049协议");


    private int value;
    private String description;


    OuterProtocolType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int value() {
        return this.value;
    }

    public String description() {
        return this.description;
    }

}