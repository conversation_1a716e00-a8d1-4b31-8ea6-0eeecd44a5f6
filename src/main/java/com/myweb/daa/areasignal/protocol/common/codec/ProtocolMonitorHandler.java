package com.myweb.daa.areasignal.protocol.common.codec;

import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.netty.ChannelHolder;
import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.common.utils.EquipmentStatus;
import com.myweb.daa.areasignal.protocol.common.utils.MessageProcessUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.concurrent.SingleThreadEventExecutor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName: ProtocolMonitorHandler
 * @Description: 用于监控的基本处理handler，记录当前连接的socket
 * 2019-01-30 增加socket定义的协议类型
 * 2019-01-30 增加接收/发送总字节数，周期接收/发送字节数，吞吐量
 * @Author: king
 * @CreateDate: 2018/12/4 8:30
 */
@Slf4j
public class ProtocolMonitorHandler extends ChannelInboundHandlerAdapter {

    private MessagePublisher messagePublisher;
    private volatile ScheduledFuture<?> messageCountSchedule = null;
    private volatile long pendingTasks;
    private volatile long lastRecieveTime;
    private volatile AtomicLong totalReadBytes = new AtomicLong(0);

    @Getter
    private ProtocolType protocolType;


    public ProtocolMonitorHandler(ProtocolType protocolType, MessagePublisher messagePublisher) {
        this.protocolType = protocolType;
        this.messagePublisher = messagePublisher;
    }


    public void statistic(ChannelHandlerContext ctx) {
        /**
         * 下面是统计当前eventloop中所有线程的状况
         Iterator<EventExecutor> eventExecutorIterator = ctx.executor().parent().iterator();
         while(eventExecutorIterator.hasNext())
         {
         SingleThreadEventExecutor executor = (SingleThreadEventExecutor)eventExecutorIterator.next();
         int size = executor.pendingTasks();
         if(ctx.executor() == executor) {
         log.debug(ctx.channel() + "-->" + executor + "pending size in queue is -->" + size);
         }else {
         log.debug(executor + "pending size in queue is -->" + size);
         }
         }

         /** 线程池运行状态*/
        SingleThreadEventExecutor executor = (SingleThreadEventExecutor) ctx.executor();
        pendingTasks = executor.pendingTasks();

        /** 发送队列积压消息数*/
        long pendingSize = ctx.channel().unsafe().outboundBuffer().totalPendingWriteBytes();

        /*
        log.debug(ctx.channel() + " --> " + executor + "\t\t pending size in queue is --> " + pendingTasks
                + "\t\t --> channel total pending write bytes is " + pendingSize + " bytes"
                +  "\t\t --> channel total read bytes is " + totalReadBytes.get() + " bytes");
                */

        ChannelHolder.setExtraInfo(ctx, pendingTasks, totalReadBytes.get(), 0, lastRecieveTime);
    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) {

        /**记录当前activeSocket*/
        ChannelHolder.put(ChannelHolder.ChannelInfo.builder()
                .channel(ctx.channel())
                .channelHandlerContext(ctx)
                .protocolType(protocolType)
                .activeDateTime(System.currentTimeMillis())
                .build());

        /**添加激活日志*/
        ChannelHolder.addLog(ctx.channel(), true, protocolType);

        /**定期统计channel状态
         * 查询代码可知，当为comm口的时候，线程对象为ThreadPerChannelEventLoop，在Run方法中不会对scheduleTask进行调度
         *         messageCountSchedule = ctx.executor().scheduleAtFixedRate(
         *                 new TaskTimer(ctx), 1000, 1000,
         *                 TimeUnit.MILLISECONDS);
         *取消定时调度数据项，改为接收到数据的时候进行数据的统计
         */
        /**发送设备active*/
        EquipmentStatus equipmentStatus = MessageProcessUtils.buildEquipmentStatus(ctx, protocolType, true, true);
        messagePublisher.publishMessage(equipmentStatus);

        ctx.fireChannelActive();
    }


    @Override
    public void channelInactive(ChannelHandlerContext ctx) {

        /**移除Socket*/
        ChannelHolder.remove(ctx);
        /**添加激活日志*/
        ChannelHolder.addLog(ctx.channel(), false, protocolType);

        if (messageCountSchedule != null) {
            messageCountSchedule.cancel(true);
            messageCountSchedule = null;
        }

        /**发送设备inactive*/
        EquipmentStatus equipmentStatus = MessageProcessUtils.buildEquipmentStatus(ctx, protocolType, false, true);
        messagePublisher.publishMessage(equipmentStatus);

        ctx.fireChannelInactive();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        int readableBytes = ((ByteBuf) msg).readableBytes();
        totalReadBytes.getAndAdd(readableBytes);
        lastRecieveTime = System.currentTimeMillis();

        statistic(ctx);

        ctx.fireChannelRead(msg);
    }

}
