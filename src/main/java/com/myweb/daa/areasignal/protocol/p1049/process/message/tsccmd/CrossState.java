package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CrossState
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/15 14:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossState")
public class CrossState implements DataIndexAble {
    private String CrossID;
    private String Value;

    /**
     * 用于标记是否是手动调看，用于主动刷新信号机状态
     */
    private boolean manual;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
