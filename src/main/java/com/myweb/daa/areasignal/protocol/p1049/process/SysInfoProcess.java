package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseMessage1049;
import com.myweb.daa.areasignal.protocol.p1049.process.message.CmdProcessAble;
import com.myweb.daa.areasignal.protocol.p1049.process.message.OperationName;
import com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd.SysInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class SysInfoProcess implements CmdProcessAble {
    @Override
    public OperationName getOperationName() {
        return OperationName.Get;
    }

    @Override
    public String getCmdObjectName() {
        return SysInfo.class.getName();
    }

    @Override
    public void processRequest(Object object, BaseMessage1049 baseMessage1049) {

    }

    @Override
    public void processResponse(Object object, BaseMessage1049 baseMessage1049) {
        log.error("get message at {} ---{}", LocalDateTime.now(), object);
    }

    @Override
    public void processError(Object object, BaseMessage1049 baseMessage1049) {

    }

    @Override
    public void processPush(Object object, BaseMessage1049 baseMessage1049) {

    }

}
