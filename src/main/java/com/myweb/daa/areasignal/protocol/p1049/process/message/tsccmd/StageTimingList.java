package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/16 14:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageTimingList")
public class StageTimingList {
    //1049扩展协议
    @XStreamImplicit(itemFieldName = "StageTiming")
    private List<StageTiming> StageTiming;
}
