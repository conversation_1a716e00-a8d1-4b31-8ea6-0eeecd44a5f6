package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: LaneParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("LaneParam")
public class LaneParam implements DataIndexAble{
    private String CrossID;
    private String LaneNo;
    private String Direction;
    private String Attribute;
    private String Movement;
    private String Feature;
    @XStreamAlias("Azimuth")
    private Integer azimuth;

    @XStreamAlias("WaitingArea")
    private Integer waitingArea;

    @XStreamAlias("VarMovementList")
    private VarMovementList varMovementList;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return LaneNo;
    }
}
