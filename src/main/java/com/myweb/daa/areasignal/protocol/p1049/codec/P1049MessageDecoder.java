/*
 * Copyright 2013-2018 Lil<PERSON>feng.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.myweb.daa.areasignal.protocol.p1049.codec;


import com.myweb.daa.areasignal.protocol.p1049.message.P1049Message;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 分包处理
 * @date 2014年3月15日
 */
@Slf4j
public class P1049MessageDecoder extends ChannelInboundHandlerAdapter {

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        final ByteBuf buffer = (ByteBuf) msg;
        byte[] body = new byte[buffer.readableBytes()];
        buffer.readBytes(body);
        P1049Message pl049Message = new P1049Message();
        pl049Message.setBody(body);
        pl049Message.setCtx(ctx);
        ReferenceCountUtil.release(msg);
        ctx.fireChannelRead(pl049Message);
    }

}
