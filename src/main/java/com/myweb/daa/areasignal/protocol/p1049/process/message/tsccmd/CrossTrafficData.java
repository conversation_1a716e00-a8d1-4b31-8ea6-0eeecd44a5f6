package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CrossTrafficData
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossTrafficData")
public class CrossTrafficData implements DataIndexAble{
    private String CrossID;
    private String EndTime;
    private String Interval;
    private DataList DataList;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
