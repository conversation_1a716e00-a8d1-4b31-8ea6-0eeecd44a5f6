package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_TimeServer;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class Timeserver1049Process implements CmdProcessAble {


    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private MessageOuterPublisher messagePublisher;

    @Override
    public OperationName getOperationName() {
        return OperationName.Get;
    }

    @Override
    public String getCmdObjectName() {
        return SDO_TimeServer.class.getName();
    }

    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildResponse(SDO_TimeServer sdo_timeServer) {

        sdo_timeServer.setHost("1192.168.11.114");
        sdo_timeServer.setPort("123");
        sdo_timeServer.setProtocol("NTP");

        //构造内部数据项
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_TimeServer(sdo_timeServer)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 login1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return login1049;
    }

    @Override
    public void processRequest(Object object, BaseMessage1049 baseMessage1049) {
        if (!(object instanceof SDO_TimeServer)) {
            log.error("Sub1049Process，但是不是SDO_MsgEntity类型");
            return;
        }
        SDO_TimeServer sdo_timeServer = (SDO_TimeServer) object;
        log.debug("收到数据报文项-{}", sdo_timeServer);


        //设置token
        String token = baseMessage1049.getToken();
        //登录成功
        BaseMessage1049 loginSuccess = buildResponse(sdo_timeServer);
        //设置系统源
        loginSuccess.setSignalBrandPortOptional(baseMessage1049.getSignalBrandPortOptional());
        //设置头数据项
        P1049Utils.setBaseHeader1049(loginSuccess, p1049Configure, token, MessageType.RESPONSE, baseMessage1049.getSeq());
        //设置发送目的
        loginSuccess.setAddress(baseMessage1049.getAddress());
        //数据发送
        InterProtocol interProtocol = P1049Utils.buildMessage(loginSuccess.getAddress(), InterProtocolType.P1049_MSG
                , loginSuccess, false, false);
        messagePublisher.sendMessageOuter(interProtocol);

    }


    @Override
    public void processResponse(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 response");
    }

    @Override
    public void processError(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 error");
    }

    @Override
    public void processPush(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 push");
    }
}
