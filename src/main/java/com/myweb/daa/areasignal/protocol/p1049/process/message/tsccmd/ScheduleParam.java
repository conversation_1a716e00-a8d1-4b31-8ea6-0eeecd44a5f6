package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 16:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("ScheduleParam")
@Slf4j
public class ScheduleParam implements Serializable, DataIndexAble {
    private String CrossID;
    private String ScheduleNo;
    private String ScheduleName;

    /**
     * 调度类型。调度类型的优先级由高到低，分别取值
     * 1：特殊日调度（由  StartDay 到  EndDay 标识的 1 天或多天）
     * 2：时间段周调度（StartDay 到  EndDay 中的周几）
     * 3：周调度
     */
    private String Type;
    private String StartDay;
    private String EndDay;
    private String WeekDay;
    private String DayPlanNo;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return ScheduleNo;
    }

}
