package com.myweb.daa.areasignal.protocol.p1049.process.message;

import com.myweb.daa.areasignal.event.AckManager.AckAble;
import com.myweb.daa.areasignal.event.AckManager.NeedAck;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * @ClassName: BaseMessage1049
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/5 13:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("Message")
public class BaseMessage1049 extends BaseHeader1049 implements AckAble, NeedAck {
    @XStreamAlias("Body")
    private Body1049 body;

    @Override
    public String toString() {
        return "BaseMessage1049{" +
                "body=" + body +
                ", version='" + version + '\'' +
                ", token='" + token + '\'' +
                ", from=" + from +
                ", to=" + to +
                ", type='" + type + '\'' +
                ", seq='" + seq + '\'' +
                ", address='" + address + '\'' +
                '}';
    }

    @Override
    public String getAckBackKey() {
        return super.getSeq();
    }

    @Override
    public String getAckKey() {
        return super.getSeq();
    }

    /**
     * 获取数据项中的第一个数据
     *
     * @return
     */
    public Optional<Object> getFirstMsgData() {
        if (getBody() == null) {
            return Optional.empty();
        } else if (getBody().getOperation() == null) {
            return Optional.empty();
        } else if (getBody().getOperation().size() < 1) {
            return Optional.empty();
        } else {
            return getBody().getOperation().get(0).getBaseOperationData();
        }
    }
}
