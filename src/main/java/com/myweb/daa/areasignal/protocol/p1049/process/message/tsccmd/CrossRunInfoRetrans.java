package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossRunInfoRetrans")
public class CrossRunInfoRetrans {

    @XStreamAlias("StartTime")
    private String startTime;

    @XStreamAlias("EndTime")
    private String endTime;

    @XStreamAlias("ObjName")
    private String objName;

    @XStreamAlias("CrossIDList")
    private CrossIDList crossIDList;

}
