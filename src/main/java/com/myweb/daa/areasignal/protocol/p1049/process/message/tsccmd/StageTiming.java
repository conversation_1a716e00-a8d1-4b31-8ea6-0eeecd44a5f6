package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 17:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageTiming")
public class StageTiming implements Serializable {
    private String StageNo;
    private String Green;
    private String Yellow;
    private String AllRed;

    /**
     * 感应/自适应控制最大绿灯时间
     */
    private String MaxGreen;

    /**
     * 感应/自适应控制最小绿灯时间
     */
    private String MinGreen;

    @XStreamAlias("AdjustList")
    private AdjustList adjustList;
}
