package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CrossStage
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/22 15:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossStage")
public class CrossStage implements DataIndexAble {
    private String CrossID;
    private String LastStageNo;
    private String LastStageLen;
    private String CurStageNo;
    private String CurStageStartTime;
    private String CurStageLen;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
