/*
 * Copyright 2013-2018 Lilinfeng.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.myweb.daa.areasignal.protocol.p1049.codec;


import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 分包处理
 * @date 2014年3月15日
 */
@Slf4j
public class P1049MessageYLDecoder extends ChannelInboundHandlerAdapter {

    private static ByteBuf SPILTER_DATA = Unpooled.buffer(1);

    //     * YL信号系统每次连接都会发送  0x 55 54 43 53 0d 0a
    static {
        /** 数据分割符 */
        SPILTER_DATA.writeByte(0x55);
        SPILTER_DATA.writeByte(0x54);
        SPILTER_DATA.writeByte(0x43);
        SPILTER_DATA.writeByte(0x53);
        SPILTER_DATA.writeByte(0x0d);
        SPILTER_DATA.writeByte(0x0a);
    }

    private static int indexOf(ByteBuf haystack, ByteBuf needle) {
        for (int i = haystack.readerIndex(); i < haystack.readableBytes(); i ++) {
            int haystackIndex = i;
            int needleIndex;
            for (needleIndex = 0; needleIndex < needle.readableBytes() && haystackIndex < haystack.readableBytes(); needleIndex++) {
                if (haystack.getByte(haystackIndex) != needle.getByte(needleIndex)) {
                    break;
                } else {
                    haystackIndex++;
                }
            }

            if (needleIndex == needle.readableBytes()) {
                // Found the needle from the haystack!
                return i - haystack.readerIndex();
            }
        }
        return -1;
    }

    /**
     * @param ctx
     * @param msg
     * @throws Exception
     */

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        final ByteBuf buffer = (ByteBuf) msg;

        //过滤初始化的数据项
        if(buffer.readableBytes() >= 6){
            int i = indexOf(buffer, SPILTER_DATA);
            if(i != - 1){
                buffer.skipBytes(i + 6);
            }
        }

        if(buffer.readableBytes() > 0) {
            ctx.fireChannelRead(msg);
        }
    }

}
