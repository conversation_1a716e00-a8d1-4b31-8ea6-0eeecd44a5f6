package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CrossParam
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/13 16:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("CrossParam")
public class CrossParam implements DataIndexAble{
    private String CrossID;
    private String CrossName;
    private String Feature;
    private String Grade;

    @XStreamAlias("DetNoList")
    private DetNoList detNoList;

    private LaneNoList LaneNoList;

    @XStreamAlias("PedestrianNoList")
    private PedestrianNoList pedestrianNoList;

    @XStreamAlias("LampGroupNoList")
    private LampGroupNoList lampGroupNoList;

    @XStreamAlias("SignalGroupNoList")
    private SignalGroupNoList signalGroupNoList;

    @XStreamAlias("GreenConflictMatrix")
    private String greenConflictMatrix;

    private StageNoList StageNoList;

    private PlanNoList PlanNoList;

    //1049协议扩展
    private DayPlanNoList DayPlanNoList;

    private ScheduleNoList ScheduleNoList;

    //慈溪海康 经纬度 以及 信号机IP
    private String Longitude;
    private String Latitude;
    private String IP;

    private String Altitude;

    /**
     * 用于查看当前路口是否已经进行映射配置，用于后续新增路口时进行路口添加
     */
    private boolean mapped;

    @Override
    public String dataID() {
        return CrossID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
