package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/21
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("PedestrianParam")
public class PedestrianParam implements DataIndexAble{

    @XStreamAlias("CrossID")
    private String crossId;

    @XStreamAlias("PedestrianNo")
    private Integer pedestrianNo;

    @XStreamAlias("Direction")
    private Integer direction;

    @XStreamAlias("Attribute")
    private Integer attribute;

    @Override
    public String dataID() {
        return crossId;
    }

    @Override
    public String dataNo() {
        return String.valueOf(pedestrianNo);
    }
}
