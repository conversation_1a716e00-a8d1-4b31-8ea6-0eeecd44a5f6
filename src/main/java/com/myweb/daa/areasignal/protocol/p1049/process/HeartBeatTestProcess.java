package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_HeartBeat;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@Profile("test")
public class HeartBeatTestProcess {


    @Autowired
    private P1049Manager p1049Manager;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private MessageSendProcess messageSendProcess;

    @Autowired
    private MessageOuterPublisher messagePublisher;

    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildHeartBeat() {
        //构造内部数据项
        SDO_HeartBeat hb = new SDO_HeartBeat();
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_HeartBeat(hb)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(OperationName.Notify.name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 login1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return login1049;
    }

    /**
     * 同步发送心跳数据项
     */
    @Scheduled(initialDelay = 60000, fixedRate = 1000)
    public void syncHeartBeat() {
        log.error("当前token数据项-{}", p1049Manager.getTokenMap());
         {
                {
                        //设置token
                        String token = "test-token";
                        //登录成功
                        BaseMessage1049 loginSuccess = buildHeartBeat();
                        //设置系统源
                        loginSuccess.setSignalBrandPortOptional(Optional.of(SignalBrandPort.DH));
                        //设置头数据项
                        P1049Utils.setBaseHeader1049(loginSuccess, p1049Configure, token, MessageType.PUSH,
                                messageSendProcess.getSeq());
                        //设置发送目的
                        loginSuccess.setAddress("127.0.0.1");
                        //数据发送
                        InterProtocol interProtocol = P1049Utils.buildMessage(loginSuccess.getAddress(), InterProtocolType.P1049_MSG
                                , loginSuccess, false, false);
                        messagePublisher.sendMessageOuter(interProtocol);
                        log.error("发送心跳{}-{}数据项-Seq-{}", SignalBrandPort.DH.name(), "127.0.0.1",
                                loginSuccess.getSeq());
                }
         }

    }

}
