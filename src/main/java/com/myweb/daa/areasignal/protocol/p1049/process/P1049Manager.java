package com.myweb.daa.areasignal.protocol.p1049.process;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.myweb.daa.areasignal.business.bean.LinkStatus;
import com.myweb.daa.areasignal.business.bean.Ops1049SystemStatus;
import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import com.myweb.daa.areasignal.protocol.p1049.process.message.BaseHeader1049;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName: P1049Manager
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/6 10:28
 */
@Data
@Slf4j
@Component
public class P1049Manager {

    @Getter
    private Map<SignalBrandPort, Map<String, TokenData>> tokenMap = new ConcurrentHashMap<>();

    /**
     * 历史数据项
     */
    private Queue<TokenData> tokenMapLog = new ConcurrentLinkedQueue();

    /**
     * 查看数据token是否正确
     *
     * @param baseHeader1049
     * @return
     */
    public boolean isTokenOk(BaseHeader1049 baseHeader1049) {
        if (!baseHeader1049.getSignalBrandPortOptional().isPresent()
                || baseHeader1049.getAddress() == null
                || baseHeader1049.getAddress().isEmpty()) {
            return false;
        }

        Optional<String> tokenOptional = getToken(baseHeader1049.getSignalBrandPortOptional().get(), baseHeader1049.getAddress());
        if (tokenOptional.isPresent()) {
            if (tokenOptional.get().equals(baseHeader1049.getToken())) {
                return true;
            } else {
                log.info("地址{} 异常的token {},应该是{}", baseHeader1049.getAddress(), baseHeader1049.getToken(), tokenOptional.get());
            }
        } else {
            log.info("地址{} 异常的token {}, 尚未登录，当前内存项{}", baseHeader1049.getAddress(), baseHeader1049.getToken(), getTokenMap());
        }
        return false;

    }

    /**
     * 返回用户的token，没有登录则返回空
     *
     * @param signalBrandPort
     * @return
     */
    public Optional<String> getToken(SignalBrandPort signalBrandPort, String address) {
        //已经登录了,默认登录
         return Optional.of("88888");

    }

    /**
     * 新用户登录，生成token
     *
     * @param address
     * @return
     */
    public String addToken(String address, SignalBrandPort signalBrandPort) {

        Map<String, TokenData> stringTokenDataMap = tokenMap.get(signalBrandPort);
        if (stringTokenDataMap == null) {
            stringTokenDataMap = new ConcurrentHashMap<>();
            tokenMap.put(signalBrandPort, stringTokenDataMap);
        }

        //新用户登录
        String token = P1049Utils.getRandom();
        stringTokenDataMap.put(address,
                TokenData.builder()
                        .address(address)
                        .signalBrandPort(signalBrandPort)
                        .token(token)
                        .loginTime(LocalDateTime.now())
                        .heartBeat(new AtomicLong(0)).build());

        log.error("@@@@@当前系统登录信息-{}", JSONObject.toJSONString(tokenMap));

        return (token);
    }

    /**
     * 新用户登录，生成token
     *
     * @param address
     * @return
     */
    public String addToken(String address, SignalBrandPort signalBrandPort, String token) {
        Map<String, TokenData> stringTokenDataMap = tokenMap.get(signalBrandPort);
        if (stringTokenDataMap == null) {
            stringTokenDataMap = new ConcurrentHashMap<>();
            tokenMap.put(signalBrandPort, stringTokenDataMap);
        }

        //新用户登录
        stringTokenDataMap.put(address,
                TokenData.builder()
                        .address(address)
                        .signalBrandPort(signalBrandPort)
                        .token(token)
                        .loginTime(LocalDateTime.now())
                        .heartBeat(new AtomicLong(0)).build());

        log.error("@@@@@当前系统登录信息-{}", JSONObject.toJSONString(tokenMap));

        return (token);
    }

    /**
     * 用户退出，删除token
     *
     * @param address
     * @return
     */
    public boolean rmToken(String address, SignalBrandPort signalBrandPort) {
        if (!tokenMap.containsKey(signalBrandPort)) {
            return true;
        }
        //address = ChannelHolder.getIp(address).get();
        Map<String, TokenData> stringTokenDataMap = tokenMap.get(signalBrandPort);

        //已经存在数据项，放入历史记录
        if (stringTokenDataMap.containsKey(address)) {
            if (tokenMapLog.size() > 1000) {
                tokenMapLog.poll();
            }
            tokenMapLog.add(stringTokenDataMap.get(address));
        }
        stringTokenDataMap.remove(address);

        log.error("@@@@@当前系统登录信息-{}", JSONObject.toJSONString(tokenMap));
        return true;
    }

    /**
     * 获取当前登录的token
     *
     * @return
     */
    public Optional<String> getLoginToken(SignalBrandPort signalBrandPort, String address) {
        return getToken(signalBrandPort, address);
    }


    /**
     * 更新心跳数据时间
     *
     * @param signalBrandPort
     * @param address
     */
    public void updateHeartBeatTime(SignalBrandPort signalBrandPort, String address) {
        if (tokenMap.containsKey(signalBrandPort)) {
            Map<String, TokenData> stringTokenDataMap = tokenMap.get(signalBrandPort);
            TokenData tokenData = stringTokenDataMap.get(address);
            if (tokenData != null) {
                tokenData.setLstHeartBeatTime(LocalDateTime.now());
                tokenData.getHeartBeat().incrementAndGet();
            }
        }
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenData {
        private String token;
        private String address;
        private SignalBrandPort signalBrandPort;

        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime loginTime;

        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lstHeartBeatTime;

        private AtomicLong heartBeat;
    }


    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void equipmentStatus(Ops1049SystemStatus ops1049SystemStatus) {
        log.error("$$$$$$系统链路状态变更-{}", ops1049SystemStatus);
        //系统端脱机
        if (ops1049SystemStatus.getLinkStatus() == LinkStatus.UNLINK) {

            if (ops1049SystemStatus.getAddress() == null || ops1049SystemStatus.getSignalBrandPort() == null) {
                return;
            }

            rmToken(ops1049SystemStatus.getAddress(), ops1049SystemStatus.getSignalBrandPort());
        }
    }
}
