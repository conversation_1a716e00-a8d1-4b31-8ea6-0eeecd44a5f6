package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/8/12 15:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("ScheduleNoList")
public class ScheduleNoList implements Serializable {
    @XStreamImplicit(itemFieldName = "ScheduleNo")
    private List<String> ScheduleNo;
}