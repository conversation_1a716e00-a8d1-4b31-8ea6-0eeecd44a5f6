package com.myweb.daa.areasignal.protocol.p1049.process;

import com.myweb.daa.areasignal.config.P1049Configure;
import com.myweb.daa.areasignal.event.MessageOuterPublisher;
import com.myweb.daa.areasignal.protocol.InterProtocolType;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.p1049.process.message.*;
import com.myweb.daa.areasignal.protocol.p1049.process.message.object.SDO_User1049;
import com.myweb.daa.areasignal.protocol.p1049.util.P1049Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: Logout1049Process
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/9 10:54
 */
@Component
@Slf4j
public class Logout1049Process implements CmdProcessAble {

    @Autowired
    private MessageOuterPublisher messagePublisher;

    @Autowired
    private P1049Configure p1049Configure;

    @Autowired
    private P1049Manager p1049Manager;

    /**
     * 构造登录成功
     *
     * @return
     */
    public BaseMessage1049 buildLogout(SDO_User1049 sdo_user1049,
                                       BaseMessage1049 baseMessage1049,
                                       P1049Configure p1049Configure) {
        //构造内部数据项
        SDO_User1049 su = SDO_User1049.builder()
                .userName(sdo_user1049.getUserName())
                .pwd(sdo_user1049.getPwd()).build();
        //opration数据项
        //opration数据项
        BaseOperation1049 operation1049 = BaseOperation1049.builder()
                .sDO_User1049(su)
                .build();
        operation1049.setOrder("1");
        operation1049.setName(getOperationName().name());
        List<BaseOperation1049> operations = new ArrayList<>();
        operations.add(operation1049);
        //构造数据项
        BaseMessage1049 logout1049 = BaseMessage1049.builder()
                .body(Body1049.builder()
                        .operation(operations).build())
                .build();
        return logout1049;
    }

    @Override
    public OperationName getOperationName() {
        return OperationName.Logout;
    }

    @Override
    public String getCmdObjectName() {
        return SDO_User1049.class.getName();
    }

    @Override
    public void processRequest(Object object, BaseMessage1049 baseMessage1049) {
        if (!(object instanceof SDO_User1049)) {
            log.error("使用Login1049Process进行数据处理，但是不是SDO_User1049类型");
            return;
        }
        SDO_User1049 sdo_user1049 = (SDO_User1049) object;
        log.debug("收到数据报文项-{}", sdo_user1049);

        if (!baseMessage1049.getSignalBrandPortOptional().isPresent()) {
            log.error("没有找到系统源数据logout");
            return;
        }

        if (p1049Manager.isTokenOk(baseMessage1049)) {
            //移除token，发送登出成功数据项
            p1049Manager.rmToken(baseMessage1049.getAddress(), baseMessage1049.getSignalBrandPortOptional().get());
            //登录成功
            BaseMessage1049 logout1049 = buildLogout(sdo_user1049, baseMessage1049, p1049Configure);
            //设置系统源
            logout1049.setSignalBrandPortOptional(baseMessage1049.getSignalBrandPortOptional());
            //设置头数据项
            P1049Utils.setBaseHeader1049(logout1049, p1049Configure, baseMessage1049.getToken(), MessageType.RESPONSE, baseMessage1049.getSeq());
            //设置发送目的
            logout1049.setAddress(baseMessage1049.getAddress());
            //数据发送
            InterProtocol interProtocol = P1049Utils.buildMessage(baseMessage1049.getAddress(), InterProtocolType.P1049_MSG
                    , logout1049, false, false);
            messagePublisher.sendMessageOuter(interProtocol);
        }

    }


    @Override
    public void processResponse(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 response");
    }

    @Override
    public void processError(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 error");
    }

    @Override
    public void processPush(Object object, BaseMessage1049 baseMessage1049) {
        log.error("不支持 push");
    }

}
