package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: utcs1049
 * @packageName: com.les.its.scs.core.gb1049.message.tsccmd
 * @author: whr
 * @description:
 * @date: 2024/7/15
 * @version: 1.0
 * @modifyHistory:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("SignalController")
public class SignalController implements DataIndexAble {

    //取值12位交通管理部门机构代码（按GA 380—2012）+ 99 + 4位数字
    @XStreamAlias("SignalControllerID")
    private String signalControllerID;

    @XStreamAlias("Supplier")
    private String supplier;

    @XStreamAlias("Type")
    private String type;

    @XStreamAlias("ID")
    private String id;

    @XStreamAlias("CommMode")
    private String commMode;

    @XStreamAlias("IP")
    private String ip;

    @XStreamAlias("SubMask")
    private String subMask;

    @XStreamAlias("Gateway")
    private String gateway;

    @XStreamAlias("Port")
    private Integer port;

    @XStreamAlias("HasDoorStatus")
    private Integer hasDoorStatus;

    @XStreamAlias("Longitude")
    private Double longitude;

    @XStreamAlias("Latitude")
    private Double latitude;

    @XStreamAlias("CrossIDList")
    private CrossIDList crossIDList;

    @Override
    public String dataID() {
        return signalControllerID;
    }

    @Override
    public String dataNo() {
        return "0";
    }
}
