package com.myweb.daa.areasignal.protocol.http.service;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 向外部发送数据请求的服务
 */
@Service
@Slf4j
public class DataQueryService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${global.dataUrl}")
    private  String dataUrl;

    public String postData(Class<?> clazz, Map<> ){
        String queryUrl = dataUrl + url;
        log.info("向{}请求数据", queryUrl);
        try {
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("返回数据-{}", str);
            return str;
        } catch (Exception e) {
            log.error("请求数据的时候出现异常.", e);
        }

        return null;
    }


}
