package com.myweb.daa.areasignal.protocol.p1049.process.message.tsccmd;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/4/26 14:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XStreamAlias("StageCtrlList")
public class StageCtrlList implements Serializable {

    private String CrossID;

    @XStreamImplicit(itemFieldName = "StageOptList")
    private List<StageOptList> StageOptList;
}
