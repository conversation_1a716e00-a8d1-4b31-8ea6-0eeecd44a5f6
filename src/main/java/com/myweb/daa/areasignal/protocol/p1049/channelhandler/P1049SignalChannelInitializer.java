package com.myweb.daa.areasignal.protocol.p1049.channelhandler;


import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.protocol.common.channelhandler.ProtocolChannelInitializer;
import com.myweb.daa.areasignal.protocol.common.codec.CommonMessageHandler;
import com.myweb.daa.areasignal.protocol.common.codec.ProtocolMonitorHandler;
import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.p1049.codec.P1049MessageDecoder;
import com.myweb.daa.areasignal.protocol.p1049.codec.P1049MessageEncoder;
import com.myweb.daa.areasignal.protocol.p1049.codec.P1049MessageYLDecoder;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.codec.xml.XmlFrameDecoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.Data;
import org.springframework.core.env.Environment;

/**
 * @ClassName: 1049协议处理链路
 * @Description: 继承ProtocolChannelInitializer返回处理链
 * @Author: king
 * @CreateDate: 2018/12/3 15:43
 */
@Data
public class P1049SignalChannelInitializer<B extends Channel> implements ProtocolChannelInitializer {

    private MessagePublisher messagePublisher;

    private Environment environment;

    public P1049SignalChannelInitializer(MessagePublisher messagePublisher, Environment environment) {
        this.messagePublisher = messagePublisher;
        this.environment = environment;
    }

    @Override
    public ChannelInitializer<B> getChannelInitializer() {
        return
                new ChannelInitializer<B>() {
                    @Override
                    public void initChannel(B ch) {
                        //ch.pipeline().addLast(new CheckMultiLinkHandler());
                        ch.pipeline().addLast(new ProtocolMonitorHandler(ProtocolType.P1049_SIGNAL, messagePublisher));
                        ch.pipeline().addLast(new LoggingHandler(LogLevel.DEBUG));

                        String[] activeProfiles = environment.getActiveProfiles();
                        for (String profile : activeProfiles) {
                            if(profile.equalsIgnoreCase("test")) {
                                ch.pipeline().addLast("p1049MessageYLDecoder", new P1049MessageYLDecoder());
                            }
                        }

                        ch.pipeline().addLast("XmlFrameDecoder", new XmlFrameDecoder(5000000));
                        ch.pipeline().addLast("p1049MessageDecoder", new P1049MessageDecoder());
                        ch.pipeline().addLast("p1049MessageEncoder", new P1049MessageEncoder());
                        ch.pipeline().addLast("readTimeoutHandler",
                                new ReadTimeoutHandler(60 * 5));
                        ch.pipeline().addLast("idleHandler",
                                new IdleStateHandler(60 * 10, 60 * 3, 60 * 10));

                        //报文处理类
                        CommonMessageHandler messageHandler = new CommonMessageHandler();
                        messageHandler.setMessagePublisher(messagePublisher);
                        ch.pipeline().addLast("P1049MessageHandler",
                                messageHandler);
                    }
                };
    }

}
