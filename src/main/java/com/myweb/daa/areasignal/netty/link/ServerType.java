package com.myweb.daa.areasignal.netty.link;


import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName: ServerType
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/16 13:24
 */
public interface ServerType {
    @Data
    @AllArgsConstructor
    class PortPotocol {
        private int port;
        private String protocol;

        public Optional<String> getKey() {
            try {
                ProtocolType protocolType = ProtocolType.valueOf(protocol);
                return Optional.of(protocolType.value() + "-" + port);
            } catch (IllegalArgumentException e) {
                return Optional.empty();
            }
        }
    }

    /**
     * 获取服务端类型的参数
     *
     * @return
     */
    List<PortPotocol> getServerList();
}
