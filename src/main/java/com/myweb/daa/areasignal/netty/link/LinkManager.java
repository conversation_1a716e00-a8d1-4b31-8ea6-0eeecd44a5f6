package com.myweb.daa.areasignal.netty.link;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: LinkManager
 * @Description: 管理所有设备
 * @Author: king
 * @CreateDate: 2019/5/16 11:30
 */
@Data
@Component
@Slf4j
public class LinkManager implements ApplicationContextAware {

    private Map<String, AbstractLink> linkMap = new ConcurrentHashMap<>();

    /**
     * spring上下文
     */
    private ApplicationContext applicationContext;

    /**
     * 添加连接项
     *
     * @param abstractLink
     */
    public void addLink(AbstractLink abstractLink) {
        log.info("添加链路-{}", abstractLink.getKey());
        linkMap.putIfAbsent(abstractLink.getKey(), abstractLink);
    }


    /**
     * 移除连接项
     *
     * @param abstractLink
     */
    public void rmLink(AbstractLink abstractLink) {
        log.info("删除链路-{}", abstractLink.getKey());
        linkMap.remove(abstractLink.getKey());
    }

    /**
     * 根据客户端key获取netty实例
     *
     * @param ipPortProtocol
     * @return
     */
    public Optional<AbstractLink> getClient(ClientType.IpPortProtocol ipPortProtocol) {
        if (!ipPortProtocol.getKey().isPresent()) {
            log.error("客户端 {} 获取 netty 关键字异常", ipPortProtocol);
            return Optional.empty();
        }

        AbstractLink abstractLink = linkMap.get(ipPortProtocol.getKey().get());
        if (abstractLink != null) {
            return Optional.of(abstractLink);
        } else {
            return Optional.empty();
        }
    }

    /**
     * 根据服务端key获取netty实例
     *
     * @param portPotocol
     * @return
     */
    public Optional<AbstractLink> getServer(ServerType.PortPotocol portPotocol) {
        if (!portPotocol.getKey().isPresent()) {
            log.error("服务器 {} 获取 netty 关键字异常", portPotocol);
            return Optional.empty();
        }

        AbstractLink abstractLink = linkMap.get(portPotocol.getKey().get());
        if (abstractLink != null) {
            return Optional.of(abstractLink);
        } else {
            return Optional.empty();
        }
    }


    /**
     * 获取客户端类型的所有需要linkManager启动管理的对象
     */
    public List<ClientType.IpPortProtocol> getClientList() {
        Map<String, ClientType> beansOfType = applicationContext.getBeansOfType(ClientType.class);
        List<ClientType.IpPortProtocol> ipPortProtocolList = new ArrayList<>();
        beansOfType.keySet().forEach(
                key ->
                {
                    beansOfType.get(key).getClientList().forEach(
                            ipPortProtocol -> {
                                ipPortProtocolList.add(ipPortProtocol);
                            }
                    );
                }
        );
        return ipPortProtocolList;
    }

    /**
     * 获取服务端类型的所有需要linkManager启动管理的对象
     */
    public List<ServerType.PortPotocol> getServerList() {
        Map<String, ServerType> beansOfType = applicationContext.getBeansOfType(ServerType.class);
        List<ServerType.PortPotocol> portPotocols = new ArrayList<>();
        beansOfType.keySet().forEach(
                key ->
                {
                    beansOfType.get(key).getServerList().forEach(
                            portPotocol -> {
                                portPotocols.add(portPotocol);
                            }
                    );
                }
        );
        return portPotocols;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
