package com.myweb.daa.areasignal.netty.reconnect;

import com.myweb.daa.areasignal.config.GlobalConfigure;
import com.myweb.daa.areasignal.task.AsyncTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: ReconnectEvent
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/7 15:13
 */
@Slf4j
@Component
public class ReconnectEventProcess {

    @Autowired
    private AsyncTaskService asyncTaskService;

    /**
     * 接收重连数据事件
     *
     * @param reconnectEvent
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void reconnectEventProcess(ReconnectEvent reconnectEvent) {
        asyncTaskService.executeReconnectEvent(reconnectEvent.getReconnectAble());
    }
}
