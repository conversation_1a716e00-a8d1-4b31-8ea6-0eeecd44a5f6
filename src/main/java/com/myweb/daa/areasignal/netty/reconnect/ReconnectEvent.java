package com.myweb.daa.areasignal.netty.reconnect;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: ReconnectEvent
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/7 15:13
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReconnectEvent {
    private ReconnectAble reconnectAble;
}
