package com.myweb.daa.areasignal.netty;


import com.github.jkschneider.netty.jssc.JsscChannel;
import com.myweb.daa.areasignal.event.AckManager.response.CommandFactory;
import com.myweb.daa.areasignal.event.AckManager.response.ResponseStatus;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.protocol.common.InterProtocol;
import com.myweb.daa.areasignal.protocol.common.OuterProtocolType;
import com.myweb.daa.areasignal.protocol.common.constdata.ProtocolType;
import com.myweb.daa.areasignal.protocol.common.message.AbstractProtocolMessage;
import com.myweb.daa.areasignal.utils.ConstValue;
import com.myweb.daa.areasignal.utils.ResultCode;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.DatagramChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.NetUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * @ClassName: ChannelHolder
 * @Description: 存储当前连接的socket信息
 * @Author: king
 * @CreateDate: 2018/12/4 8:21
 */
@Slf4j
public class ChannelHolder {
    private static final Map<String, ChannelInfo> MAP = new ConcurrentHashMap<>(16);

    /**
     * 只保留最近的1000条数据项
     */
    private static int MAX_LOG_SIZE = 1000;

    @Getter
    private static final Queue<LogInfo> linkLogQueue = new ConcurrentLinkedQueue();

    private static final String IP_PORT_TAG = ":";

    private static final String REMOTE_LOCAL_CONNECT = "->";

    private ChannelHolder() {
    }

    /**
     * socket标记关键字
     *
     * @param channel
     * @return
     */
    public static String getKey(Channel channel) {
        if (channel instanceof NioSocketChannel) {
            NioSocketChannel nioSocketChannel = (NioSocketChannel) channel;
            return NetUtil.toSocketAddressString(nioSocketChannel.remoteAddress())
                    + REMOTE_LOCAL_CONNECT + NetUtil.toSocketAddressString(nioSocketChannel.localAddress());
        } else if (channel instanceof JsscChannel) {
            JsscChannel rxtxChannel = (JsscChannel) channel;
            return rxtxChannel.remoteAddress().value()
                    + REMOTE_LOCAL_CONNECT + rxtxChannel.localAddress().value();
        } else {
            log.error("无法获取channel关键字,尚未支持的channel类型{}", channel);
            return channel.toString();
        }
    }

    /**
     * socket标记关键字
     *
     * @param channel
     * @return
     */
    public static int getLocalPort(Channel channel) {
        if (channel instanceof NioSocketChannel) {
            NioSocketChannel nioSocketChannel = (NioSocketChannel) channel;
            return nioSocketChannel.localAddress().getPort();
        } else {
            return 0;
        }
    }

    /**
     * socket标记关键字
     *
     * @param channel
     * @return
     */
    public static int getRemotePort(Channel channel) {
        if (channel instanceof NioSocketChannel) {
            NioSocketChannel nioSocketChannel = (NioSocketChannel) channel;
            return nioSocketChannel.remoteAddress().getPort();
        } else {
            return 0;
        }
    }

    /**
     * 获取远端地址，用于标记数据来源
     *
     * @param channel
     * @return
     */
    public static String getRemoteAddress(Channel channel) {
        if (channel instanceof NioSocketChannel) {
            NioSocketChannel nioSocketChannel = (NioSocketChannel) channel;
            return NetUtil.toSocketAddressString(nioSocketChannel.remoteAddress());
        } else if (channel instanceof JsscChannel) {
            JsscChannel rxtxChannel = (JsscChannel) channel;
            return rxtxChannel.remoteAddress().value();
        } else {
            log.error("无法获取远端地址,尚未支持的channel类型{}", channel);
            return channel.remoteAddress().toString();
        }
    }

    /**
     * @param ctx
     * @return
     */
    public static String getRemoteAddress(ChannelHandlerContext ctx) {
        return getRemoteAddress(ctx.channel());
    }

    /**
     * 获取IP
     *
     * @param ipAndPortStr
     * @return
     */
    public static Optional<String> getIp(String ipAndPortStr) {
        String[] ipAndPort = ipAndPortStr.split(":");
        if (ipAndPort.length == 2) {
            return Optional.of(ipAndPort[0]);
        } else {
            log.error("错误的IP端口格式-{}", ipAndPort);
            return Optional.empty();
        }
    }

    /**
     * 获取端口
     *
     * @param ipAndPortStr
     * @return
     */
    public static Optional<Integer> getPort(String ipAndPortStr) {
        String[] ipAndPort = ipAndPortStr.split(":");
        if (ipAndPort.length == 2) {
            return Optional.of(Integer.parseInt(ipAndPort[1]));
        } else {
            log.error("错误的IP端口格式-{}", ipAndPort);
            return Optional.empty();
        }
    }

    /**
     * 获取远端的地址
     *
     * @return
     */
    public static Optional<String> getAddressIp(String key, boolean isLocal) {
        String[] strings = key.split(REMOTE_LOCAL_CONNECT);
        if (strings.length != 2) {
            log.error("错误的key格式-{}", key);
            return Optional.empty();
        }

        String[] ipAndPort = strings[isLocal ? 1 : 0].split(":");
        if (ipAndPort.length == 2 || ipAndPort.length == 1) {
            return Optional.of(ipAndPort[0]);
        } else {
            log.error("错误的IP端口格式-{}", strings[0]);
            return Optional.empty();
        }

    }

    /**
     * 获取远端的端口
     *
     * @return
     */
    public static Optional<Integer> getAddressPort(String key, boolean isLocal) {
        String[] strings = key.split(REMOTE_LOCAL_CONNECT);
        if (strings.length != 2) {
            log.error("错误的key格式-{}", key);
            return Optional.empty();
        }

        String[] ipAndPort = strings[isLocal ? 1 : 0].split(":");
        if (ipAndPort.length == 2) {
            return Optional.of(Integer.valueOf(ipAndPort[1]));
        } else if (ipAndPort.length == 1) {
            return Optional.of(Integer.valueOf(0));
        } else {
            log.error("错误的IP端口格式-{}", strings[0]);
            return Optional.empty();
        }
    }


    /**
     * @param ctx
     * @return
     */
    public static String getKey(ChannelHandlerContext ctx) {
        return getKey(ctx.channel());
    }

    /**
     * 添加链路激活或者inactive的日志
     *
     * @param channel
     * @param active
     */
    public static void addLog(Channel channel, boolean active, ProtocolType protocolType) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        String nowStr = now.format(format);

        if (linkLogQueue.size() > MAX_LOG_SIZE) {
            linkLogQueue.poll();
        }

        String key;
        if (channel instanceof DatagramChannel) {
            key = getKey((DatagramChannel) channel);
        } else {
            key = getKey((NioSocketChannel) channel);
        }
        String info = "链路:(远端)" + key + "(本地)-" + (active ? "连接" : "断开连接");

        String title = protocolType.description() + "-" + key;
        linkLogQueue.add(LogInfo.builder().timestamp(nowStr).content(info).title(title).build());
    }


    /**
     * 根据channelinfo发送数据项
     *
     * @param channelInfo
     * @param abstractProtocolMessage
     * @param interProtocol
     * @param messagePublisher
     * @return
     */
    public static ResultCode send(ChannelInfo channelInfo, AbstractProtocolMessage abstractProtocolMessage,
                                  InterProtocol interProtocol, MessagePublisher messagePublisher) {
        ChannelHandlerContext channelHandlerContext = channelInfo.getChannelHandlerContext();
        writeAndFlush(channelHandlerContext, abstractProtocolMessage, interProtocol, messagePublisher);
        return ResultCode.SUCCESS;
    }

    /**
     * 向ip设备发送数据，ip也可能是串口设备的名称例如-COM1
     *
     * @param ip
     * @param port                    为0时表示不指定具体port
     * @param abstractProtocolMessage
     * @return
     */
    public static ResultCode send(String ip, int port, AbstractProtocolMessage abstractProtocolMessage,
                                  InterProtocol interProtocol, MessagePublisher messagePublisher) {
        //根据ip、port获取可用的设备
        List<String> keyList = MAP.keySet().stream().filter(key ->
        {

            String[] remoteLocal = key.split(REMOTE_LOCAL_CONNECT);
            if (remoteLocal.length != 2) {
                log.error("key={} format is not right 1", key);
                return false;
            }

            /**IP类型的地址的时候*/
            if (ip.matches(ConstValue.IP_PATTERN)) {
                String[] dataVector = remoteLocal[0].replace("/", "").split(IP_PORT_TAG);
                if (dataVector.length != 2) {
                    log.error("key={} format is not right 2", key);
                    return false;
                }

                if (port == 0) {
                    return dataVector[0].trim().equals(ip);
                } else {
                    return dataVector[0].trim().equals(ip) && (port == Integer.valueOf(dataVector[1]));
                }
            } else {
                /**COM类型地址的时候*/
                return remoteLocal[0].trim().equals(ip);
            }
        }).collect(Collectors.toList());

        //没有可用的设备
        if (keyList.isEmpty()) {
            log.warn("can not find ip={} to send , avaliable keys {}", ip, MAP.keySet());
            return ResultCode.CHANNELHOLDER_NO_EQUIPMENT_CONNECTTED;
        }


        //向可用的设备发送数据
        for (String s : keyList) {
            ChannelHandlerContext channelHandlerContext = MAP.get(s).getChannelHandlerContext();
            writeAndFlush(channelHandlerContext, abstractProtocolMessage, interProtocol, messagePublisher);
        }
        return ResultCode.SUCCESS;
    }

    /**
     * 根据配置的协议类型向设备发送数据
     *
     * @param protocolType
     * @param abstractProtocolMessage
     * @return
     */
    public static ResultCode send(ProtocolType protocolType, AbstractProtocolMessage abstractProtocolMessage,
                                  InterProtocol interProtocol, MessagePublisher messagePublisher) {
        //根据ip、port获取可用的设备
        List<String> keyList = MAP.keySet().stream().filter(key ->
        {
            return MAP.get(key).getProtocolType() == protocolType;
        }).collect(Collectors.toList());

        //没有可用的设备
        if (keyList.isEmpty()) {
            log.warn("can not find protocolType={} to send , avaliable channels are {}", protocolType, MAP);
            return ResultCode.CHANNELHOLDER_NO_EQUIPMENT_CONNECTTED;
        }

        //向可用的设备发送数据
        for (String s : keyList) {
            ChannelHandlerContext channelHandlerContext = MAP.get(s).getChannelHandlerContext();
            writeAndFlush(channelHandlerContext, abstractProtocolMessage, interProtocol, messagePublisher);
        }
        return ResultCode.SUCCESS;
    }


    /**
     * 获取当前激活的链路的所有协议类型
     *
     * @return
     */
    public static List<ProtocolType> getActiveProtocolType(int protocolType) {
        return MAP.values().stream().filter(channelInfo -> channelInfo.getProtocolType().value() == protocolType).map(channelInfo ->
                channelInfo.getProtocolType()).collect(Collectors.toList());
    }

    /**
     * 获取指定IP的协议类型
     *
     * @return
     */
    public static Optional<ChannelInfo> getChannelInfo(String ip, int port, int protocolType) {
        return MAP.keySet().stream().filter(key ->
        {
            String[] remoteLocal = key.split(REMOTE_LOCAL_CONNECT);
            if (remoteLocal.length != 2) {
                log.error("key={} format is not right 3", key);
                return false;
            }
            /**IP类型的地址的时候*/
            if (ip.matches(ConstValue.IP_PATTERN)) {
                String[] dataVector = remoteLocal[0].replace("/", "").split(IP_PORT_TAG);
                if (dataVector.length != 2) {
                    //log.error("key={} format is not right 4 - ip-{}", key, ip);
                    return false;
                }

                /**判定是否指定了端口*/
                if (port == 0) {
                    return dataVector[0].trim().equals(ip);
                } else {
                    return dataVector[0].trim().equals(ip)
                            && (Integer.valueOf(dataVector[1].trim()) == port);
                }
            } else {
                /**COM类型地址的时候\带端口ip:port*/
                return remoteLocal[0].trim().equals(ip);
            }

        }).filter(key ->
                MAP.get(key).getProtocolType().value() == protocolType).map(key -> MAP.get(key)).findAny();
    }


    public static void writeAndFlush(ChannelHandlerContext channelHandlerContext, Object object,
                                     InterProtocol interProtocol, MessagePublisher messagePublisher) {
        if (null == channelHandlerContext) {
            log.error("can not write and flush for channelHandlerContext is null");
            return;
        }

        channelHandlerContext.channel().writeAndFlush(object).addListener((ChannelFutureListener) future -> {
            if (future.isSuccess()) {
                log.debug("success send to [{}] about [{}]", channelHandlerContext.channel().remoteAddress(), object);
            } else {
                log.error("failed send to [{}] reason [{}] about [{}]", channelHandlerContext.channel().remoteAddress(),
                        future.cause().getMessage(),
                        object);
            }

            /**增加应答到应用层*/
            if (messagePublisher != null && interProtocol != null && object instanceof AbstractProtocolMessage) {
                messagePublisher.publishMessage(CommandFactory.createMessageResponse(
                        future.isSuccess() ? ResponseStatus.SUCCESS_SEND : ResponseStatus.FAILED_SEND, interProtocol));
            }
        });
    }


    public static void put(ChannelInfo channelInfo) {
        MAP.put(getKey(channelInfo.getChannelHandlerContext()), channelInfo);
    }


    public static Map<String, ChannelInfo> getMAP() {
        return MAP;
    }

    public static void remove(ChannelHandlerContext ctx) {
        MAP.remove(getKey(ctx));
    }


    public static void setWriteBytes(ChannelHandlerContext ctx,
                                     long writeBytes) {
        if (!MAP.containsKey(getKey(ctx))) {
            return;
        }

        ChannelInfo channelInfo = MAP.get(getKey(ctx));
        channelInfo.setTotalWriteBytes(channelInfo.getTotalReadBytes() + writeBytes);
    }

    public static void setExtraInfo(ChannelHandlerContext ctx,
                                    long pendingTasks,
                                    long totalReadBytes,
                                    long readRates,
                                    long lastRecieveTime) {
        if (!MAP.containsKey(getKey(ctx))) {
            log.error("没有找到key为{}的channel链路", getKey(ctx));
            return;
        }

        ChannelInfo channelInfo = MAP.get(getKey(ctx));
        channelInfo.setPendingTasks(pendingTasks);
        channelInfo.setTotalReadBytes(totalReadBytes);
        channelInfo.setReadRates(readRates);
        channelInfo.setLastRecieveTime(lastRecieveTime);
    }


    @Builder
    @AllArgsConstructor
    @Data
    public static class ChannelInfo {
        private ChannelHandlerContext channelHandlerContext;
        private Channel channel;
        private ProtocolType protocolType;
        private long activeDateTime;

        private long pendingTasks;
        private long totalReadBytes;
        private long readRates;
        private long totalWriteBytes;
        private long writeRates;
        private long lastRecieveTime;

        public String getActiveDateTime() {
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            return timeFormat.format(new Date(activeDateTime));
        }

        public String getLastRecieveTime() {
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            return timeFormat.format(new Date(lastRecieveTime));
        }
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogInfo {
        private String timestamp;
        private String title;
        private String content;
    }


}
