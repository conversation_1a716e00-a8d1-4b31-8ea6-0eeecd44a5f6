package com.myweb.daa.areasignal.netty;


import com.github.jkschneider.netty.jssc.JsscChannel;
import com.github.jkschneider.netty.jssc.JsscChannelOption;
import com.github.jkschneider.netty.jssc.JsscDeviceAddress;
import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.netty.link.AbstractLink;
import com.myweb.daa.areasignal.netty.reconnect.ReconnectAble;
import com.myweb.daa.areasignal.netty.reconnect.ReconnectEvent;
import com.myweb.daa.areasignal.protocol.common.Protocol;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.oio.OioEventLoopGroup;
import jssc.SerialPort;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.util.*;


/**
 * The type Netty comm.
 *
 * @ClassName: NettyComm
 * @Description:
 * @Author: king
 * @CreateDate: 2019 /3/8 9:22
 */
@Slf4j
@Data
public class NettyComm3 extends AbstractLink implements ReconnectAble {

    /**
     * 连接的串口名称
     */
    private String commAddress;

    /**
     * 端口对应的解析协议
     */
    private Optional<Protocol> protocol;

    /**
     * 客户端所在的EventLoopGroup，设置默认线程个数为1
     */
    private EventLoopGroup group = new OioEventLoopGroup(1);

    /**
     * 发起异步连接操作
     */
    private ChannelFuture future;

    /**
     * 创建引导项
     */
    private Bootstrap b;

    private MessagePublisher messagePublisher;

    /**
     * 标记当前是否已经停止
     */
    private volatile boolean stopFlag = false;


    /**
     * 重连随机数
     */
    private static Random reconnectRandom = new Random(System.currentTimeMillis());

    private static List<String> avaliableComms = Collections.synchronizedList(new ArrayList<String>());

    static {
        /*
        log.info("begin init comm interface -{}", LocalDateTime.now());
        CommPortIdentifier portid = null;
        Enumeration e = CommPortIdentifier.getPortIdentifiers();
        while (e.hasMoreElements()) {
            portid = (CommPortIdentifier) e.nextElement();
            log.info("可用串口设备-" + portid.getName());
            avaliableComms.add(portid.getName());
        }
        log.info("end init comm interface -{}", LocalDateTime.now());
        log.info("所有设备信息-{}", avaliableComms);
        */
    }


    /**
     * Instantiates a new Netty comm.
     *
     * @param commAddress      the comm address
     * @param protocol         the protocol
     * @param messagePublisher the message publisher
     */
    public NettyComm3(String commAddress, String protocol, MessagePublisher messagePublisher, Environment env) {
        this.commAddress = commAddress;
        this.messagePublisher = messagePublisher;
        this.protocol = Protocol.buildProtocol(protocol, messagePublisher, true, env);
    }

    /**
     * 防止出现大规模重连
     * 重连间隔在3 - 20 随机
     *
     * @return
     */
    private int getRandomReconnectTime() {
        return (3 + reconnectRandom.nextInt(7)) * 1000;
    }


    /**
     * 绑定comm口
     */
    public void connect() {
        if (stopFlag) {
            log.warn("客户端 {} 已经停止连接", commAddress);
            return;
        }

        NettyComm3 nettyComm = this;

        try {
            /*
            if (!avaliableComms.contains(commAddress)) {
                log.error("绑定串口失败,未找到需要连接的串口设备 [{}]", commAddress);
                //关闭之后进行重新连接
                int delay = getRandomReconnectTime();
                log.warn("串口设备{} {}s后尝试重新连接", commAddress, delay);
                messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(nettyComm).build());
                return;
            }
            */

            b = new Bootstrap();
            b.group(group)
                    .channel(JsscChannel.class)
                    .option(JsscChannelOption.BAUD_RATE, 9600)
                    .option(JsscChannelOption.DATA_BITS, SerialPort.DATABITS_8)
                    .option(JsscChannelOption.PARITY_BIT, SerialPort.PARITY_ODD)
                    .option(JsscChannelOption.STOP_BITS, SerialPort.STOPBITS_1)
                    .option(JsscChannelOption.DTR, false)
                    .option(JsscChannelOption.RTS, false)
                    .option(JsscChannelOption.WAIT_TIME, 1000)
                    .handler(protocol.get().getProtocolChannelInitializer().getChannelInitializer());

            future = b.connect(new JsscDeviceAddress(commAddress)).sync();
            future.channel().closeFuture().addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture future) throws Exception {

                    if (stopFlag) {
                        log.warn("串口设备{} 已经停止连接", commAddress);
                        return;
                    }

                    /**关闭之后进行重新连接*/
                    int delay = getRandomReconnectTime();
                    log.warn("串口设备{} {}ms后尝试重新连接", commAddress, delay);
                    messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(nettyComm).build());
                }
            });
        } catch (Exception e) {
            // 所有资源释放完成之后，清空资源，再次发起重连操作
            int delay = getRandomReconnectTime();
            log.warn("串口设备{}连接出现异常 {}ms尝试重新连接-{}", commAddress, delay, e);
            messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(this).build());
        }
    }


    @Override
    public void reconnect() {
        connect();
    }

    @Override
    public String getKey() {
        return this.getProtocol().get().getProtocolType().value() + "-" + commAddress;
    }

    @Override
    public void stop() {
        stopFlag = true;
        group.shutdownGracefully();
    }
}
