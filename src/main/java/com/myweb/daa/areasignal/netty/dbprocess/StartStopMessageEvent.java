package com.myweb.daa.areasignal.netty.dbprocess;


import com.myweb.daa.areasignal.netty.link.ClientType;
import com.myweb.daa.areasignal.netty.link.ServerType;
import lombok.Getter;

import java.util.Optional;

/**
 * @ClassName: StartStopMessageEvent
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/16 14:26
 */
public class StartStopMessageEvent {
    /**
     * 是启动还是关闭
     */
    @Getter
    private boolean start;

    /**
     * 客户端数据项
     */
    @Getter
    private Optional<ClientType.IpPortProtocol> ipPortProtocolOptional;

    /**
     * 服务端数据项
     */
    @Getter
    private Optional<ServerType.PortPotocol> portPotocolOptional;

    public StartStopMessageEvent(boolean start, Optional<ClientType.IpPortProtocol> ipPortProtocolOptional, Optional<ServerType.PortPotocol> portPotocolOptional) {
        this.start = start;
        this.ipPortProtocolOptional = ipPortProtocolOptional;
        this.portPotocolOptional = portPotocolOptional;
    }
}
