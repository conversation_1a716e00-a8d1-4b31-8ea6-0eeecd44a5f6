package com.myweb.daa.areasignal.netty;


import com.myweb.daa.areasignal.event.MessagePublisher;
import com.myweb.daa.areasignal.netty.link.AbstractLink;
import com.myweb.daa.areasignal.protocol.common.Protocol;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.util.Optional;

/**
 * @ClassName: NettyServer
 * @Description: 监听式服务器实现
 * @Author: king
 * @CreateDate: 2018/12/3 15:01
 */
@Slf4j
@Data
public class NettyServer extends AbstractLink {
    /**
     * netty监听的端口
     */
    private int port;

    /**
     * 端口对应的解析协议
     */
    private Optional<Protocol> protocol;

    // 配置服务端的NIO线程组
    private EventLoopGroup bossGroup = new NioEventLoopGroup();
    private EventLoopGroup workerGroup = new NioEventLoopGroup();

    public NettyServer(int port, String protocol,
                       MessagePublisher messagePublisher, Environment env) {
        this.port = port;
        this.protocol = Protocol.buildProtocol(protocol, messagePublisher, false, env);
    }


    /**
     * 绑定端口开始数据的监听
     *
     * @throws Exception
     */
    public void bind() throws Exception {
        //检查协议是否正常
        if (!protocol.isPresent()) {
            log.error("not able to bind port" + port + ",because protocol is null");
            return;
        }

        ServerBootstrap b = new ServerBootstrap();
        b.group(bossGroup, workerGroup).channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 100)
                .handler(new LoggingHandler(LogLevel.INFO))
                .childHandler(protocol.get().getProtocolChannelInitializer().getChannelInitializer());
        // 绑定端口，同步等待成功
        ChannelFuture f = b.bind(port).sync();
        log.info("端口绑定成功: " + port + ",协议=" + protocol.get().getProtocolType());

        // 等待服务端监听端口关闭
        f.channel().closeFuture().addListener((ChannelFutureListener) future -> {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        });
    }

    @Override
    public void stop() {
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();
    }

    @Override
    public String getKey() {
        return this.getProtocol().get().getProtocolType().value() + "-" + port;
    }
}
