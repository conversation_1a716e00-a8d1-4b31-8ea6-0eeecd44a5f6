package com.myweb.daa.areasignal;

import com.keyholesoftware.publish.swagger.PublishSwagger;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.persistence.EntityManager;

/**
 * -Xms2048m -Xmx2048m
 * -XX:NewSize=1600m -XX:MaxNewSize=1600m
 * -XX:MaxDirectMemorySize=1024m
 * -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
 * -Xloggc:gc.log -XX:+UseGCLogFileRotation
 * -XX:NumberOfGCLogFiles=100
 * -XX:GCLogFileSize=10m
 * -XX:+PrintGCApplicationStoppedTime
 * -XX:+PrintGCApplicationConcurrentTime
 * -XX:+HeapDumpOnOutOfMemoryError
 * 一行数据项
 * -Xms2048m -Xmx2048m -XX:NewSize=1600m -XX:MaxNewSize=1600m -XX:MaxDirectMemorySize=1024m -XX:+PrintGCDetails -XX:+PrintGCTimeStamps  -Xloggc:gc.log -XX:+UseGCLogFileRotation  -XX:NumberOfGCLogFiles=100 -XX:GCLogFileSize=10m -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime  -XX:+HeapDumpOnOutOfMemoryError
 */
@SpringBootApplication
@PublishSwagger
@EnableConfigurationProperties
@ComponentScan("com.myweb")
@EnableScheduling
public class Adapter1049V2Application {

    public static void main(String[] args) {

        SpringApplication.run(Adapter1049V2Application.class, args);
    }

    @Bean
    public JPAQueryFactory jpaQueryFactory(EntityManager entityManager) {
        return new JPAQueryFactory(entityManager);
    }
}
