package com.myweb.daa.areasignal.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/15 13:51
 */
@Data
@Component
@ConfigurationProperties(prefix = "test.signal")
@Slf4j
public class TestSignalConfigure {
    private boolean useTest;
    private String signalId;
    private List<String> crossingIds;
    private int noArea;
    private int noJunc;
    private List<Integer> subJuncNos;
    private String ip;
    private int port;
    private String signalId_1049;
    private List<String> crossingIds_1049;
    private String systemIp;
}
