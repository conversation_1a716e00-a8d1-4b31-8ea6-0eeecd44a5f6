package com.myweb.daa.areasignal.config;

import cn.hutool.core.util.CharsetUtil;
import com.myweb.daa.areasignal.htbussiness.bean.SignalBrandPort;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * @ClassName: P1049Configure
 * @Description:
 * @Author: king
 * @CreateDate: 2019/11/6 11:01
 */
@Data
@Component
@ConfigurationProperties(prefix = "global.p1049")
@Slf4j
public class P1049Configure {

    private boolean savedb;
    private boolean loginGetData;
    private Map<String, SysConfig> sysConfigMap;

    /**
     * 数据编码格式，默认
     */
    private String dataCodec;


    public String getCodec() {
        if (dataCodec == null || dataCodec.isEmpty()) {
            return CharsetUtil.UTF_8;
        } else {
            return dataCodec;
        }
    }

    public SysConfig getSysConfig(Optional<SignalBrandPort> signalBrandPortOptional) {
        if (signalBrandPortOptional.isPresent()) {
            SysConfig sysConfig = sysConfigMap.get(signalBrandPortOptional.get().name());
            if (sysConfig == null) {
                log.error("没有找到信号系统配置对象-{},使用默认配置", signalBrandPortOptional.get());
                return sysConfigMap.get(SignalBrandPort.HT);
            } else {
                return sysConfig;
            }
        } else {
            log.error("信号系统源未知,使用默认配置");
            return sysConfigMap.get(SignalBrandPort.HT);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class SysConfig {

        /**
         * 版本
         */
        private String version;
        /**
         * 源系统名称
         */
        private String fromSys;

        /**
         * 目的系统
         */
        private String toSys;

        /**
         * dest sub system
         */
        private String toSubsys;
        /**
         * 用户名
         */
        private String userName;
        /**
         * 密码
         */
        private String password;

        /**
         * 远端系统ip地址
         */
        private String remoteIp;

        /**
         * 是否启用相位参数，用于支持相位关联车道数据项
         */
        private boolean enablePhaseParam;


    }
}
