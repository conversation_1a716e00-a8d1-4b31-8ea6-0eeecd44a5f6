package com.myweb.daa.areasignal.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Global configure.
 *
 * @ClassName: GlobalConfigure
 * @Description: 全局数据配置中心
 * @Author: king
 * @CreateDate: 2018 /12/6 9:59
 */
@Configuration
public class GlobalConfigure {
    /**
     * 用于分发接收报文的分发线程池
     */
    public final static String MESSAGE_ASYC_EXECUTOR = "MESSAGE";

    /**
     * 用于分发接收向外系统发送报文的分发线程池
     */
    public final static String OUTER_MESSAGE_ASYC_EXECUTOR = "OUTER_MESSAGE";

    /**
     * 用于处理报文数据的线程池
     */
    public final static String MESSAGE_PROCESS_EXECUTOR = "INNER_MESSAGE";

    /**
     * 用于处理报文数据的线程池
     */
    public final static String MESSAGE_1049_PROCESS_EXECUTOR = "MESSAGE_1049";

    /**
     * 用于处理数据存储的线程池
     */
    public final static String DB_MSG_PROCESS_EXECUTOR = "DB_MSG";

    /**
     * 用于处理系统mq消息的线程池
     */
    public final static String MQ_MSG_PROCESS_EXECUTOR = "MQ_MSG";

    /**
     * 用于处理系统mq优化消息的线程池
     */
    public final static String OPTIMIZE_PROCESS_EXECUTOR = "OPTIMIZE";

    /**
     * 用于处理系统查询状态的线程池
     */
    public final static String CROSS_STATE_PROCESS_EXECUTOR = "CROSS_STATE";

    /**
     * 用于sgp数据存储的线程池
     */
    public final static String SGP_PROCESS_EXECUTOR = "SGP_EXE";


    /**
     * 后台静默调看数据线程池
     */
    public final static String DATA_LOOK_PROCESS_EXECUTOR = "DATA_LOOK";

    /**
     * 本地端口监听配置
     */
    public static List<Integer> listenPortList = new ArrayList<>();
    /**
     * 本地端口监听协议类型
     */
    public static List<String> listenProtocolList = new ArrayList<>();
    /**
     * 远程监听IP
     */
    public static List<String> connectIPList = new ArrayList<>();
    /**
     * 远程端口监听配置
     */
    public static List<Integer> connectPortList = new ArrayList<>();


    /**
     * 本地端口监听配置
     */
    public static List<Integer> connectLocalPortList = new ArrayList<>();

    /**
     * 远程端口监听协议类型
     */
    public static List<String> connectProtocolList = new ArrayList<>();

    /**
     * 串口地址链表
     */
    public static List<String> commAddressList = new ArrayList<>();

    /**
     * 串口协议类型
     */
    public static List<String> commProtocolList = new ArrayList<>();

    /**
     * 城市编码
     */
    public static String cityCode;

    /**
     * 交通管理部门机构代码
     */
    public static String departmentCode;

    /**
     * 城市编码
     */
    public static List<Integer> areaNos;

    /**
     * ops 访问地址
     */
    public static String opsIp;

    /**
     * MQ的exchange名称
     */
    public static String exchangeName;
    /**
     * MQ的路由前缀名称
     */
    public static String routingKeyPrefix;

    /**
     * MQ获取数据项路口前缀
     */
    public static String routingKeyPrefixListen;

    /**
     * MQ获取数据消息队列名称
     */
    public static String scatsCmdQueue;


    /**
     * 中心机控制命令exchange
     */
    public static String centralExchange;

    /**
     * 中心机控制命令路由
     */
    public static String centralExchangeRouting;

    /**
     * 中心机控制命令接收队列
     */
    public static String centralCmdQueue;

    /**
     * 是否启用Sgp数据结构存储
     */
    public static volatile boolean enableUseSgp;

    /**
     * 数据请求的ip地址
     */
    public static String signalParamIp;

    /**
     * 是否检查信号机、路口数据项
     */
    public static boolean enableCheckSignalAndCrossingData = false;

    /**
     * 可配置的应答超时时间
     * */
    public static int timeOutSecond = 5;
    /**
     * 可配置的应答最大超时时间
     * */
    public static int timeOutMaxSecond = 30;

    /**
     * 过滤的信号品牌参数
     */
    public static List<String> brandList;

    /**
     * 过滤的信号区域号
     */
    public static List<String> areaList;


    public static final String USER_DEFINED_RULE_FOLDER = "C:\\rule1049";

    /**
     * Sets listen port list.
     *
     * @param listenPortList the listen port list
     */
    @Value("#{'${global.server.local.listens.port}'.split(',')}")
    public void setListenPortList(List<Integer> listenPortList) {
        if (null == listenPortList) {
            return;
        }

        if (listenPortList.isEmpty()) {
            return;
        }

        GlobalConfigure.listenPortList = listenPortList.stream().filter(integer -> {
            return integer != null && integer != 0;
        }).collect(Collectors.toList());
    }

    /**
     * Sets listen protocol list.
     *
     * @param listenProtocolList the listen protocol list
     */
    @Value("#{'${global.server.local.listens.protocol}'.split(',')}")
    public void setListenProtocolList(List<String> listenProtocolList) {
        if (null == listenProtocolList) {
            return;
        }

        if (listenProtocolList.isEmpty()) {
            return;
        }

        GlobalConfigure.listenProtocolList = listenProtocolList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect ip list.
     *
     * @param connectIPList the connect ip list
     */
    @Value("#{'${global.server.local.connect.ip}'.split(',')}")
    public void setConnectIPList(List<String> connectIPList) {
        if (null == connectIPList) {
            return;
        }

        if (connectIPList.isEmpty()) {
            return;
        }

        GlobalConfigure.connectIPList = connectIPList.stream().filter(ip -> {
            return ip != null && !ip.isEmpty();
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect port list.
     *
     * @param connectPortList the connect port list
     */
    @Value("#{'${global.server.local.connect.port}'.split(',')}")
    public void setConnectPortList(List<Integer> connectPortList) {
        if (null == connectPortList) {
            return;
        }

        if (connectPortList.isEmpty()) {
            return;
        }

        GlobalConfigure.connectPortList = connectPortList.stream().filter(port -> {
            return port != null && port != 0;
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect port list.
     *
     * @param connectPortList the connect port list
     */
    @Value("#{'${global.server.local.connect.localPort}'.split(',')}")
    public void setConnectLocalPortList(List<Integer> connectPortList) {
        if (null == connectPortList) {
            return;
        }

        if (connectPortList.isEmpty()) {
            return;
        }

        GlobalConfigure.connectLocalPortList = connectPortList.stream().filter(port -> {
            return port != null && port != 0;
        }).collect(Collectors.toList());
    }


    /**
     * Sets connect protocol list.
     *
     * @param connectProtocolList the connect protocol list
     */
    @Value("#{'${global.server.local.connect.protocol}'.split(',')}")
    public void setConnectProtocolList(List<String> connectProtocolList) {
        if (null == connectProtocolList) {
            return;
        }

        if (connectProtocolList.isEmpty()) {
            return;
        }

        GlobalConfigure.connectProtocolList = connectProtocolList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }


    @Value("#{'${global.server.local.comm.address}'.split(',')}")
    public void setCommAddressList(List<String> addressList) {
        if (null == addressList) {
            return;
        }

        if (addressList.isEmpty()) {
            return;
        }

        GlobalConfigure.commAddressList = addressList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }

    @Value("#{'${global.server.local.comm.protocol}'.split(',')}")
    public void setCommProtocolList(List<String> commList) {
        if (null == commList) {
            return;
        }

        if (commList.isEmpty()) {
            return;
        }

        GlobalConfigure.commProtocolList = commList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }


    @Value("${global.cityCode}")
    public void setCityCode(String cityCode) {
        GlobalConfigure.cityCode = cityCode;
    }

    @Value("${global.timeOutSecond:5}")
    public void setTimeOutSecond(int timeOutSecond) {
        GlobalConfigure.timeOutSecond = timeOutSecond;;
    }

    @Value("${global.timeOutMaxSecond:30}")
    public void setTimeOutMaxSecond(int timeOutMaxSecond) {
        GlobalConfigure.timeOutMaxSecond = timeOutMaxSecond;;
    }

    @Value("${global.departmentCode}")
    public void setDepartmentCode(String departmentCode) {
        GlobalConfigure.departmentCode = departmentCode;
    }

    @Value("#{'${global.areaNo}'.split(',')}")
    public void setAreaNo(List<String> areaNos) {
        if (null == areaNos) {
            return;
        }

        if (areaNos.isEmpty()) {
            return;
        }

        GlobalConfigure.areaNos = areaNos.stream().map(
                areaNo -> Integer.parseInt(areaNo.trim())
        ).collect(Collectors.toList());
    }


    @Value("${global.opsip}")
    public void setOpsIp(String opsIp) {
        GlobalConfigure.opsIp = opsIp;
    }

    /**
     * Sets routing key.
     *
     * @param exchangeName the routing key
     */
    @Value("${global.mq.exchange.name}")
    public void setExchangeName(String exchangeName) {
        GlobalConfigure.exchangeName = exchangeName;
    }

    @Value("${global.mq.exchange.routingKeyPrefix}")
    public void setRoutingKeyPrefix(String routingKeyPrefix) {
        GlobalConfigure.routingKeyPrefix = routingKeyPrefix;
    }

    @Value("${global.mq.exchange.routingKeyPrefixListen}")
    public void setRoutingKeyPrefixListen(String routingKeyPrefixListen) {
        GlobalConfigure.routingKeyPrefixListen = routingKeyPrefixListen;
    }

    @Value("${global.mq.exchange.scatsCmdQueue}")
    public void setScatsCmdQueue(String scatsCmdQueue) {
        GlobalConfigure.scatsCmdQueue = scatsCmdQueue;
    }

    @Value("${global.signalParamIp}")
    public void setSignalParamIp(String signalParamIp) {
        GlobalConfigure.signalParamIp = signalParamIp;
    }

    @Value("${global.enableCheckSignalAndCrossingData}")
    public void setEnableCheckSignalAndCrossingData(boolean enableCheckSignalAndCrossingData) {
        GlobalConfigure.enableCheckSignalAndCrossingData = enableCheckSignalAndCrossingData;
    }

    @Value("${global.enableUseSgp}")
    public void setEnableUseSgp(boolean enableUseSgp) {
        GlobalConfigure.enableUseSgp = enableUseSgp;
    }


    @Value("${global.mq.centralExchange.name}")
    public void setCentralExchange(String exchangeName) {
        GlobalConfigure.centralExchange = exchangeName;
    }

    @Value("${global.mq.centralExchange.routingKeyPrefix}")
    public void setCentralExchangeRouting(String routingKeyPrefix) {
        GlobalConfigure.centralExchangeRouting = routingKeyPrefix;
    }

    @Value("${global.mq.centralExchange.queue}")
    public void setCentralCmdQueue(String centralCmdQueue) {
        GlobalConfigure.centralCmdQueue = centralCmdQueue;
    }


    @Value("#{'${global.brandList}'.split(',')}")
    public void setBrandList(List<String> brandList) {
        if (null == brandList) {
            return;
        }

        if (brandList.isEmpty()) {
            return;
        }

        GlobalConfigure.brandList = brandList;
    }

    @Value("#{'${global.areaList}'.split(',')}")
    public void setAreaList(List<String> areaList) {
        if (null == areaList) {
            return;
        }

        if (areaList.isEmpty()) {
            return;
        }

        GlobalConfigure.areaList = areaList.stream().filter(
                areaNo -> {
                    if (areaNo == null) {
                        return false;
                    } else if (areaNo.trim().isEmpty()) {
                        return false;
                    }
                    return true;
                }
        ).collect(Collectors.toList());
    }

}
