# Eclipse project files
.project
.classpath
.settings

# IntelliJ IDEA project files and directories
*.iml
*.ipr
*.iws
.idea/
.shelf/

# Geany project file
.geany

# KDevelop project file and directory
.kdev4/
*.kdev4

# Build targets
/target
*/target

# Report directories
/reports
*/reports

# Mac-specific directory that no other operating system needs.
.DS_Store

# JVM crash logs
hs_err_pid*.log
replay_pid*.log

# JVM dumps
*.hprof.xz
*.threads

dependency-reduced-pom.xml

*/.unison.*

# exclude mainframer files
mainframer
.mainframer

# exclude docker-sync stuff
.docker-sync
*/.docker-sync

# exclude vscode files
.vscode/
*.factorypath

# exclude file created by the flatten plugin
.flattened-pom.xml
.java-version

# 日志
daa-log
*/daa-log

# 数据库
/db/*

# 前端资源文件
**/resources/static/

#jrebel产生的jar文件
jrebel*.jar
recording.jsonl
